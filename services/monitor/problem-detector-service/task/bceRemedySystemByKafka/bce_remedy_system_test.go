package bceRemedySystemByKafka

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	kafka "github.com/segmentio/kafka-go"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	remedySys_bce "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	remedySys "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bceRemedySystem"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	nrv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/configuration"
	remedySystemType "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/task/remedySystemType"
)

func newScheme(t *testing.T) *runtime.Scheme {
	t.Helper()
	scheme := runtime.NewScheme()
	if err := corev1.AddToScheme(scheme); err != nil {
		t.Fatalf("add corev1 to scheme: %v", err)
	}
	if err := nrv1.AddToScheme(scheme); err != nil {
		t.Fatalf("add nrv1 to scheme: %v", err)
	}
	return scheme
}

func newFakeClientWithObjects(t *testing.T, objs ...runtime.Object) client.Client {
	t.Helper()
	scheme := newScheme(t)
	return fake.NewFakeClientWithScheme(scheme, objs...)
}

func TestRemedyTaskToCondition_StatusAndType(t *testing.T) {
	task := &Task{}
	// Disk fail -> DiskHardwareFail and True when running
	cond := task.remedyTaskToCondition(&remedySys.TaskCommonArgs{TaskID: "t1", InstanceID: "i-1", ErrResult: "BCCSystemDiskError something"}, remedySys.TaskRunning)
	if cond.Type != remedySystemType.NodeConditionDiskFail {
		t.Fatalf("unexpected condition type: %s", cond.Type)
	}
	if cond.Status != corev1.ConditionTrue {
		t.Fatalf("unexpected condition status: %s", cond.Status)
	}

	// Non disk -> HardwareFail and False when closed
	cond = task.remedyTaskToCondition(&remedySys.TaskCommonArgs{TaskID: "t2", InstanceID: "i-2", ErrResult: "BccHardwareError"}, remedySys.TaskClosed)
	if cond.Type != remedySystemType.NodeConditionHardWareFail {
		t.Fatalf("unexpected condition type: %s", cond.Type)
	}
	if cond.Status != corev1.ConditionFalse {
		t.Fatalf("unexpected condition status: %s", cond.Status)
	}
}

func TestGetMatchedNodeRemediers(t *testing.T) {
	task := &Task{}
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "n1", Labels: map[string]string{"env": "prod", "pool": "p1"}}}

	nr1 := nrv1.NodeRemedier{ // match env=prod
		ObjectMeta: metav1.ObjectMeta{Name: "nr1"},
		Spec:       nrv1.NodeRemedierSpec{NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "prod"}}},
	}
	nr2 := nrv1.NodeRemedier{ // mismatch pool
		ObjectMeta: metav1.ObjectMeta{Name: "nr2"},
		Spec:       nrv1.NodeRemedierSpec{NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"pool": "p2"}}},
	}

	matched, err := task.getMatchedNodeRemediers(context.TODO(), node, []nrv1.NodeRemedier{nr1, nr2})
	if err != nil {
		t.Fatalf("getMatchedNodeRemediers err: %v", err)
	}
	if len(matched) != 1 || matched[0].Name != "nr1" {
		t.Fatalf("unexpected matched: %#v", matched)
	}
}

func TestShouldUpdateNodeCondition_MatchTrue(t *testing.T) {
	cli := newFakeClientWithObjects(t,
		&corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Labels: map[string]string{"env": "prod"}}},
		&nrv1.NodeRemedier{
			ObjectMeta: metav1.ObjectMeta{Name: "nr-prod", Namespace: CCENodeRemedierNamespace},
			Spec: nrv1.NodeRemedierSpec{
				NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "prod"}},
				UnhealthyConditions: []nrv1.UnhealthyCondition{{
					EnableCheck: true,
					Type:        remedySystemType.NodeConditionDiskFail,
					Status:      corev1.ConditionTrue,
				}},
			},
		},
	)

	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-1"}
	// ErrResult contains disk keyword -> target condition is DiskFail
	can, err := tsk.shouldUpdateNodeCondition(context.TODO(), cli, nodeInfo, remedySys.TaskRunning, &remedySys.TaskDetail{TaskID: "t1", ErrResult: "BCCSystemDiskError"})
	if err != nil {
		t.Fatalf("shouldUpdateNodeCondition err: %v", err)
	}
	if !can {
		t.Fatalf("expected can=true, got false")
	}
}

func TestShouldUpdateNodeCondition_NoMatchFalse(t *testing.T) {
	cli := newFakeClientWithObjects(t,
		&corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Labels: map[string]string{"env": "prod"}}},
		&nrv1.NodeRemedier{
			ObjectMeta: metav1.ObjectMeta{Name: "nr-dev", Namespace: CCENodeRemedierNamespace},
			Spec: nrv1.NodeRemedierSpec{
				NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "dev"}},
				UnhealthyConditions: []nrv1.UnhealthyCondition{{
					EnableCheck: true,
					Type:        remedySystemType.NodeConditionHardWareFail,
					Status:      corev1.ConditionTrue,
				}},
			},
		},
	)

	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-1"}
	can, err := tsk.shouldUpdateNodeCondition(context.TODO(), cli, nodeInfo, remedySys.TaskRunning, &remedySys.TaskDetail{TaskID: "t1", ErrResult: "BCCSystemDiskError"})
	if err != nil {
		t.Fatalf("shouldUpdateNodeCondition err: %v", err)
	}
	if can {
		t.Fatalf("expected can=false, got true")
	}
}

func TestShouldUpdateNodeCondition_DisabledConditionFalse(t *testing.T) {
	cli := newFakeClientWithObjects(t,
		&corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Labels: map[string]string{"env": "prod"}}},
		&nrv1.NodeRemedier{
			ObjectMeta: metav1.ObjectMeta{Name: "nr-prod", Namespace: CCENodeRemedierNamespace},
			Spec: nrv1.NodeRemedierSpec{
				NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "prod"}},
				UnhealthyConditions: []nrv1.UnhealthyCondition{{
					EnableCheck: false, // disabled
					Type:        remedySystemType.NodeConditionDiskFail,
					Status:      corev1.ConditionTrue,
				}},
			},
		},
	)

	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-1"}
	can, err := tsk.shouldUpdateNodeCondition(context.TODO(), cli, nodeInfo, remedySys.TaskRunning, &remedySys.TaskDetail{TaskID: "t1", ErrResult: "BCCSystemDiskError"})
	if err != nil {
		t.Fatalf("shouldUpdateNodeCondition err: %v", err)
	}
	if can {
		t.Fatalf("expected can=false due to disabled condition, got true")
	}
}

func TestUpdateNodeCondition_RunningAddsConditionAndAnnotation(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1"}}
	cli := newFakeClientWithObjects(t, node)

	tsk := &Task{}
	args := &remedySys.TaskCommonArgs{TaskID: "task-1", InstanceID: "i-1", ErrResult: "BCCSystemDiskError"}
	if err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-1"}, args, remedySys.TaskRunning); err != nil {
		t.Fatalf("updateNodeCondition err: %v", err)
	}

	var updated corev1.Node
	if err := cli.Get(context.TODO(), client.ObjectKey{Name: "node-1"}, &updated); err != nil {
		t.Fatalf("get updated node err: %v", err)
	}
	// Verify condition exists and status is True
	found := false
	for _, c := range updated.Status.Conditions {
		if c.Type == remedySystemType.NodeConditionDiskFail && c.Status == corev1.ConditionTrue {
			found = true
		}
	}
	if !found {
		t.Fatalf("expected DiskFail condition with True status")
	}
	// Verify annotation is set
	if updated.Annotations[BceRemedySystemTaskIDAnnotationKey] != "task-1" {
		t.Fatalf("expected annotation set, got: %v", updated.Annotations)
	}
}

func TestUpdateNodeCondition_ClosedRemovesAnnotationAndSetsFalse(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Annotations: map[string]string{BceRemedySystemTaskIDAnnotationKey: "task-1"}}}
	cli := newFakeClientWithObjects(t, node)

	tsk := &Task{}
	args := &remedySys.TaskCommonArgs{TaskID: "task-1", InstanceID: "i-1", ErrResult: "BccHardwareError"}
	if err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-1"}, args, remedySys.TaskClosed); err != nil {
		t.Fatalf("updateNodeCondition err: %v", err)
	}

	var updated corev1.Node
	if err := cli.Get(context.TODO(), client.ObjectKey{Name: "node-1"}, &updated); err != nil {
		t.Fatalf("get updated node err: %v", err)
	}
	// Verify condition type for non-disk is HardwareFail and status False
	found := false
	for _, c := range updated.Status.Conditions {
		if c.Type == remedySystemType.NodeConditionHardWareFail && c.Status == corev1.ConditionFalse {
			found = true
		}
	}
	if !found {
		t.Fatalf("expected HardwareFail condition with False status")
	}
	if _, ok := updated.Annotations[BceRemedySystemTaskIDAnnotationKey]; ok {
		t.Fatalf("expected annotation removed, got: %v", updated.Annotations)
	}
}

func TestUpdateNodeCondition_TaskArgsNil(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1"}}
	cli := newFakeClientWithObjects(t, node)
	tsk := &Task{}
	if err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-1"}, nil, remedySys.TaskRunning); err == nil {
		t.Fatalf("expected error when taskArgs is nil")
	}
}

func TestHandleCriticalEvent_EarlyValidationFailures(t *testing.T) {
	tsk := &Task{}
	// empty taskID
	evt := &remedySys.Event{Content: remedySys.Content{RawDate: remedySys.Raw{TaskID: ""}}}
	tsk.handleCriticalEvent(context.TODO(), evt, remedySys.TaskRunning)
	// nil remedySystemClient
	evt = &remedySys.Event{Content: remedySys.Content{RawDate: remedySys.Raw{TaskID: "t1"}}}
	tsk.handleCriticalEvent(context.TODO(), evt, remedySys.TaskRunning)
	// empty resources
	tsk.remedySystemClient = &stubRemedyClient{}
	tsk.handleCriticalEvent(context.TODO(), &remedySys.Event{Content: remedySys.Content{RawDate: remedySys.Raw{TaskID: "t1"}}, Resources: nil}, remedySys.TaskRunning)
	// empty userId
	tsk.handleCriticalEvent(context.TODO(), &remedySys.Event{UserId: "", Resources: []remedySys.Resource{{ResourceId: "i-1"}}, Content: remedySys.Content{RawDate: remedySys.Raw{TaskID: "t1"}}}, remedySys.TaskRunning)
}

// stubRemedyClient implements remedySys.Interface with no-op methods for compile.
type stubRemedyClient struct{}

func (s *stubRemedyClient) SetDebug(debug bool) {}
func (s *stubRemedyClient) ListRunningTasks(ctx context.Context, lsOptions *remedySys.ListOptions, signOpt *remedySys_bce.SignOption) (*remedySys.ListTasksResponse, error) {
	return nil, nil
}
func (s *stubRemedyClient) ListClosedTasks(ctx context.Context, lsOptions *remedySys.ListOptions, signOpt *remedySys_bce.SignOption) (*remedySys.ListClosedTaskResponse, error) {
	return nil, nil
}
func (s *stubRemedyClient) GetTask(ctx context.Context, getOpt *remedySys.GetOptions, signOpt *remedySys_bce.SignOption) (*remedySys.TaskDetail, error) {
	return nil, nil
}
func (s *stubRemedyClient) AuthorizeTask(ctx context.Context, authOpt *remedySys.AuthOptions, signOpt *remedySys_bce.SignOption) error {
	return nil
}
func (s *stubRemedyClient) UnAuthorizeTask(ctx context.Context, authOpt *remedySys.UnAuthOptions, signOpt *remedySys_bce.SignOption) error {
	return nil
}
func (s *stubRemedyClient) ConfirmTask(ctx context.Context, confirmOpt *remedySys.ConfirmOptions, signOpt *remedySys_bce.SignOption) error {
	return nil
}
func (s *stubRemedyClient) UnConfirmTask(ctx context.Context, confirmOpt *remedySys.UnConfirmOptions, signOpt *remedySys_bce.SignOption) error {
	return nil
}
func (s *stubRemedyClient) GetRepairRecords(ctx context.Context, getOpt *remedySys.GetRepairRecordsOptions, signOpt *remedySys_bce.SignOption) (*remedySys.RepairRecordResponse, error) {
	return nil, nil
}

func TestShouldUpdateNodeCondition_NodeNotFoundError(t *testing.T) {
	cli := newFakeClientWithObjects(t)
	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-404"}
	_, err := tsk.shouldUpdateNodeCondition(context.TODO(), cli, nodeInfo, remedySys.TaskRunning, &remedySys.TaskDetail{TaskID: "t1"})
	if err == nil {
		t.Fatalf("expected error when node not found")
	}
}

func TestGetMatchedNodeRemediers_InvalidSelector(t *testing.T) {
	tsk := &Task{}
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "n1"}}
	// Invalid label selector operator to trigger conversion error
	nr := nrv1.NodeRemedier{Spec: nrv1.NodeRemedierSpec{NodeSelector: metav1.LabelSelector{MatchExpressions: []metav1.LabelSelectorRequirement{{
		Key: "k", Operator: "InvalidOperator",
	}}}}}
	if _, err := tsk.getMatchedNodeRemediers(context.TODO(), node, []nrv1.NodeRemedier{nr}); err == nil {
		t.Fatalf("expected error for invalid selector")
	}
}

func TestUpdateNodeCondition_NodeNotFound(t *testing.T) {
	// no node created in fake client
	cli := newFakeClientWithObjects(t)
	tsk := &Task{}
	err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-not-exist"}, &remedySys.TaskCommonArgs{TaskID: "t1"}, remedySys.TaskRunning)
	if err == nil {
		t.Fatalf("expected error when node not found")
	}
}

func TestUpdateNodeCondition_UpdateExistingConditionStatusChange(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Annotations: map[string]string{BceRemedySystemTaskIDAnnotationKey: "task-1"}}}
	cli := newFakeClientWithObjects(t, node)
	tsk := &Task{}
	// first set DiskFail True
	args := &remedySys.TaskCommonArgs{TaskID: "task-1", InstanceID: "i-1", ErrResult: "BCCSystemDiskError"}
	if err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-1"}, args, remedySys.TaskRunning); err != nil {
		t.Fatalf("updateNodeCondition err: %v", err)
	}
	// then close task and expect status False while keeping DiskFail type
	if err := tsk.updateNodeCondition(context.TODO(), cli, &remedySystemType.Node{NodeName: "node-1"}, args, remedySys.TaskClosed); err != nil {
		t.Fatalf("updateNodeCondition close err: %v", err)
	}
	var updated corev1.Node
	if err := cli.Get(context.TODO(), client.ObjectKey{Name: "node-1"}, &updated); err != nil {
		t.Fatalf("get updated node err: %v", err)
	}
	foundFalse := false
	for _, c := range updated.Status.Conditions {
		if c.Type == remedySystemType.NodeConditionDiskFail && c.Status == corev1.ConditionFalse {
			foundFalse = true
		}
	}
	if !foundFalse {
		t.Fatalf("expected DiskFail condition with False status after close")
	}
}

func TestCommitMessage_RetriesAndSuccess(t *testing.T) {
	tsk := &Task{}
	var calls int32
	patch := gomonkey.ApplyFunc((*kafka.Reader).CommitMessages, func(_ *kafka.Reader, _ context.Context, _ ...kafka.Message) error {
		atomic.AddInt32(&calls, 1)
		if atomic.LoadInt32(&calls) < 2 {
			return fmt.Errorf("transient")
		}
		return nil
	})
	defer patch.Reset()

	// nil reader is fine because patched method does not access it
	tsk.commitMessage(context.TODO(), kafka.Message{})
	if atomic.LoadInt32(&calls) < 2 {
		t.Fatalf("expected at least 2 attempts, got %d", calls)
	}
}

func TestConsume_ChannelLifecycleAndNilEvents(t *testing.T) {
	// produce a sequence of messages to traverse branches
	tsk := &Task{config: &configuration.Config{KafkaConfig: &configuration.KafkaConfig{Url: "b1:9092", GroupID: "g", SubscribeTopics: "t"}, EventBufferCount: 2}}
	tsk.kafkaClient = &kafka.Reader{}

	// sequence: 1) non-BCC -> commit; 2) BCC invalid JSON -> commit; 3) BCC valid json, invalid Raw -> commit;
	// 4) BCC valid json, warning level -> no enqueue; 5) BCC valid json, critical unauthorized -> enqueue and commit after handle
	seq := []kafka.Message{
		{Value: []byte(`{"content":{"raw":"{}"}}`)},
		{Value: []byte(`BCE_BCC notjson`)},
		{Value: []byte(`{"mark":"BCE_BCC","level":"EVENT_LEVEL_CRITICAL","content":{"raw":"{bad json"}}`)},
		{Value: []byte(`{"mark":"BCE_BCC","level":"EVENT_LEVEL_WARNING","content":{"raw":"{\"taskId\":\"tW\",\"status\":\"unauthorized\"}"}}`)},
		{Value: []byte(`{"mark":"BCE_BCC","level":"EVENT_LEVEL_CRITICAL","content":{"raw":"{\"taskId\":\"tC\",\"status\":\"unauthorized\"}"}}`)},
	}
	var idx int32

	patchFetch := gomonkey.ApplyFunc((*kafka.Reader).FetchMessage, func(_ *kafka.Reader, _ context.Context) (kafka.Message, error) {
		i := atomic.AddInt32(&idx, 1) - 1
		if int(i) < len(seq) {
			return seq[i], nil
		}
		return kafka.Message{}, fmt.Errorf("stop")
	})
	defer patchFetch.Reset()

	// patch CommitMessages to succeed
	patchCommit := gomonkey.ApplyFunc((*kafka.Reader).CommitMessages, func(_ *kafka.Reader, _ context.Context, _ ...kafka.Message) error { return nil })
	defer patchCommit.Reset()

	// patch Close to no-op
	patchClose := gomonkey.ApplyFunc((*kafka.Reader).Close, func(_ *kafka.Reader) error { return nil })
	defer patchClose.Reset()

	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	_ = tsk.Consume(ctx)
}

func TestHandleCriticalEvent_GetTaskError(t *testing.T) {
	tsk := &Task{}
	tsk.remedySystemClient = &stubRemedyClient{}

	// Create a stub sts client
	stubSTS := &struct {
		sts.Interface
	}{}
	tsk.stsClient = stubSTS

	// Stub NewSignOption to return a valid SignOption
	patchNewSignOption := gomonkey.ApplyFunc((*struct{ sts.Interface }).NewSignOption, func(_ *struct{ sts.Interface }, _ context.Context, _ string) *remedySys_bce.SignOption {
		return &remedySys_bce.SignOption{}
	})
	defer patchNewSignOption.Reset()

	// Stub GetTask to return error
	patchGetTask := gomonkey.ApplyFunc((*stubRemedyClient).GetTask, func(_ *stubRemedyClient, _ context.Context, _ *remedySys.GetOptions, _ *remedySys_bce.SignOption) (*remedySys.TaskDetail, error) {
		return nil, fmt.Errorf("get task error")
	})
	defer patchGetTask.Reset()

	evt := &remedySys.Event{
		UserId:    "u-1",
		Resources: []remedySys.Resource{{ResourceId: "i-1"}},
		Content:   remedySys.Content{RawDate: remedySys.Raw{TaskID: "task-1", Status: string(remedySys.TaskStatusUnauthorized)}},
	}

	tsk.handleCriticalEvent(context.TODO(), evt, remedySys.TaskRunning)
}

func TestHandleCriticalEvent_TaskStatusNotMatch(t *testing.T) {
	tsk := &Task{}
	tsk.remedySystemClient = &stubRemedyClient{}

	// Create a stub sts client
	stubSTS := &struct {
		sts.Interface
	}{}
	tsk.stsClient = stubSTS

	// Stub NewSignOption to return a valid SignOption
	patchNewSignOption := gomonkey.ApplyFunc((*struct{ sts.Interface }).NewSignOption, func(_ *struct{ sts.Interface }, _ context.Context, _ string) *remedySys_bce.SignOption {
		return &remedySys_bce.SignOption{}
	})
	defer patchNewSignOption.Reset()

	// Stub GetTask to return task with different status
	patchGetTask := gomonkey.ApplyFunc((*stubRemedyClient).GetTask, func(_ *stubRemedyClient, _ context.Context, _ *remedySys.GetOptions, _ *remedySys_bce.SignOption) (*remedySys.TaskDetail, error) {
		return &remedySys.TaskDetail{TaskID: "task-1", Status: "running"}, nil
	})
	defer patchGetTask.Reset()

	evt := &remedySys.Event{
		UserId:    "u-1",
		Resources: []remedySys.Resource{{ResourceId: "i-1"}},
		Content:   remedySys.Content{RawDate: remedySys.Raw{TaskID: "task-1", Status: string(remedySys.TaskStatusUnauthorized)}},
	}

	tsk.handleCriticalEvent(context.TODO(), evt, remedySys.TaskRunning)
}

func TestHandleCriticalEvent_GetInstanceError(t *testing.T) {
	tsk := &Task{}
	tsk.remedySystemClient = &stubRemedyClient{}

	// Create a stub sts client
	stubSTS := &struct {
		sts.Interface
	}{}
	tsk.stsClient = stubSTS

	// Create a stub model client
	stubModel := &struct {
		models.Interface
	}{}
	tsk.model = stubModel

	// Stub NewSignOption to return a valid SignOption
	patchNewSignOption := gomonkey.ApplyFunc((*struct{ sts.Interface }).NewSignOption, func(_ *struct{ sts.Interface }, _ context.Context, _ string) *remedySys_bce.SignOption {
		return &remedySys_bce.SignOption{}
	})
	defer patchNewSignOption.Reset()

	// Stub GetTask to return valid task
	patchGetTask := gomonkey.ApplyFunc((*stubRemedyClient).GetTask, func(_ *stubRemedyClient, _ context.Context, _ *remedySys.GetOptions, _ *remedySys_bce.SignOption) (*remedySys.TaskDetail, error) {
		return &remedySys.TaskDetail{TaskID: "task-1", InstanceID: "i-1", Status: remedySys.TaskStatusUnauthorized}, nil
	})
	defer patchGetTask.Reset()

	// Stub GetInstanceByBCCID to return error
	patchGetInstance := gomonkey.ApplyFunc((*struct{ models.Interface }).GetInstanceByBCCID, func(_ *struct{ models.Interface }, _ context.Context, _ string, _ string) (*models.Instance, error) {
		return nil, fmt.Errorf("get instance error")
	})
	defer patchGetInstance.Reset()

	evt := &remedySys.Event{
		UserId:    "u-1",
		Resources: []remedySys.Resource{{ResourceId: "i-1"}},
		Content:   remedySys.Content{RawDate: remedySys.Raw{TaskID: "task-1", Status: string(remedySys.TaskStatusUnauthorized)}},
	}

	tsk.handleCriticalEvent(context.TODO(), evt, remedySys.TaskRunning)
}

func TestShouldUpdateNodeCondition_NodeInfoNil(t *testing.T) {
	tsk := &Task{}
	cli := newFakeClientWithObjects(t)
	if _, err := tsk.shouldUpdateNodeCondition(context.TODO(), cli, nil, remedySys.TaskRunning, &remedySys.TaskDetail{}); err == nil {
		t.Fatalf("expected error for nil nodeInfo")
	}
}

func TestNewBceRemedySystemTaskFromKafka_NilConfig(t *testing.T) {
	task, err := NewBceRemedySystemTaskFromKafka(context.TODO(), nil)
	if err == nil {
		t.Fatalf("expected error for nil config")
	}
	if task != nil {
		t.Fatalf("expected nil task")
	}
}

func TestNewBceRemedySystemTaskFromKafka_NilKafkaConfig(t *testing.T) {
	config := &configuration.Config{
		KafkaConfig: nil,
	}

	task, err := NewBceRemedySystemTaskFromKafka(context.TODO(), config)
	if err == nil {
		t.Fatalf("expected error for nil kafka config")
	}
	if task != nil {
		t.Fatalf("expected nil task")
	}
}

func TestInitKubeClient_GetKubeConfigError(t *testing.T) {
	tsk := &Task{}

	// Create a stub model client
	stubModel := &struct {
		models.Interface
	}{}
	tsk.model = stubModel

	// Stub GetKubeConfigCompatibility to return error
	patchGetKubeConfig := gomonkey.ApplyFunc((*struct{ models.Interface }).GetKubeConfigCompatibility, func(_ *struct{ models.Interface }, _ context.Context, _ string, _ string, _ models.KubeConfigType) (*models.KubeConfig, error) {
		return nil, fmt.Errorf("get kubeconfig error")
	})
	defer patchGetKubeConfig.Reset()

	nodeInfo := &remedySystemType.Node{ClusterID: "cluster-1", AccountID: "user-1"}
	if _, err := tsk.initKubeClient(context.TODO(), nodeInfo); err == nil {
		t.Fatalf("expected error for kubeconfig failure")
	}
}
