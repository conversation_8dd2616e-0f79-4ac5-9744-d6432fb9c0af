package bceRemedySystem

import (
	"context"
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	remedySys "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bceRemedySystem"
	nrv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/api/v1"
	remedySystemType "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/task/remedySystemType"
)

func newScheme(t *testing.T) *runtime.Scheme {
	t.Helper()
	scheme := runtime.NewScheme()
	if err := corev1.AddToScheme(scheme); err != nil {
		t.Fatalf("add corev1 to scheme: %v", err)
	}
	if err := nrv1.AddToScheme(scheme); err != nil {
		t.Fatalf("add nrv1 to scheme: %v", err)
	}
	return scheme
}

func newFakeClientWithObjects(t *testing.T, objs ...runtime.Object) client.Client {
	t.Helper()
	scheme := newScheme(t)
	return fake.NewFakeClientWithScheme(scheme, objs...)
}

func newCacheWithClient(cli client.Client) *remedySystemType.Cache {
	cache := remedySystemType.NewCache(nil)
	cache.ClusterClients = map[string]client.Client{"c-1": cli}
	return cache
}

func TestRemedyTaskToCondition_StatusAndType(t *testing.T) {
	task := &Task{}
	cond := task.remedyTaskToCondition(&remedySys.TaskCommonArgs{TaskID: "t1", InstanceID: "i-1", ErrResult: "BCCSystemDiskError something"}, remedySys.TaskRunning)
	if cond.Type != remedySystemType.NodeConditionDiskFail || cond.Status != corev1.ConditionTrue {
		t.Fatalf("unexpected condition: %#v", cond)
	}
	cond = task.remedyTaskToCondition(&remedySys.TaskCommonArgs{TaskID: "t2", InstanceID: "i-2", ErrResult: "BccHardwareError"}, remedySys.TaskClosed)
	if cond.Type != remedySystemType.NodeConditionHardWareFail || cond.Status != corev1.ConditionFalse {
		t.Fatalf("unexpected condition: %#v", cond)
	}
}

func TestShouldUpdateNodeCondition_MatchTrue(t *testing.T) {
	cli := newFakeClientWithObjects(t,
		&corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Labels: map[string]string{"env": "prod"}}},
		&nrv1.NodeRemedier{
			ObjectMeta: metav1.ObjectMeta{Name: "nr-prod", Namespace: CCENodeRemedierNamespace},
			Spec: nrv1.NodeRemedierSpec{
				NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "prod"}},
				UnhealthyConditions: []nrv1.UnhealthyCondition{{
					EnableCheck: true,
					Type:        remedySystemType.NodeConditionDiskFail,
					Status:      corev1.ConditionTrue,
				}},
			},
		},
	)
	cache := newCacheWithClient(cli)

	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-1", ClusterID: "c-1", InstanceID: "i-1"}
	can, err := tsk.shouldUpdateNodeCondition(context.TODO(), cache, nodeInfo, &remedySys.Task{TaskID: "t1", ErrResult: "BCCSystemDiskError"})
	if err != nil {
		t.Fatalf("shouldUpdateNodeCondition err: %v", err)
	}
	if !can {
		t.Fatalf("expected can=true, got false")
	}
}

func TestShouldUpdateNodeCondition_NoMatchFalse(t *testing.T) {
	cli := newFakeClientWithObjects(t,
		&corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Labels: map[string]string{"env": "prod"}}},
		&nrv1.NodeRemedier{
			ObjectMeta: metav1.ObjectMeta{Name: "nr-dev", Namespace: CCENodeRemedierNamespace},
			Spec: nrv1.NodeRemedierSpec{
				NodeSelector: metav1.LabelSelector{MatchLabels: map[string]string{"env": "dev"}},
				UnhealthyConditions: []nrv1.UnhealthyCondition{{
					EnableCheck: true,
					Type:        remedySystemType.NodeConditionHardWareFail,
					Status:      corev1.ConditionTrue,
				}},
			},
		},
	)
	cache := newCacheWithClient(cli)

	tsk := &Task{}
	nodeInfo := &remedySystemType.Node{NodeName: "node-1", ClusterID: "c-1", InstanceID: "i-1"}
	can, err := tsk.shouldUpdateNodeCondition(context.TODO(), cache, nodeInfo, &remedySys.Task{TaskID: "t1", ErrResult: "BCCSystemDiskError"})
	if err != nil {
		t.Fatalf("shouldUpdateNodeCondition err: %v", err)
	}
	if can {
		t.Fatalf("expected can=false, got true")
	}
}

func TestUpdateNodeCondition_RunningAddsConditionAndAnnotation(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1"}}
	cli := newFakeClientWithObjects(t, node)
	cache := newCacheWithClient(cli)

	tsk := &Task{}
	args := &remedySys.TaskCommonArgs{TaskID: "task-1", InstanceID: "i-1", ErrResult: "BCCSystemDiskError"}
	if err := tsk.updateNodeCondition(context.TODO(), cache, &remedySystemType.Node{NodeName: "node-1", ClusterID: "c-1"}, args, remedySys.TaskRunning); err != nil {
		t.Fatalf("updateNodeCondition err: %v", err)
	}

	var updated corev1.Node
	if err := cli.Get(context.TODO(), client.ObjectKey{Name: "node-1"}, &updated); err != nil {
		t.Fatalf("get updated node err: %v", err)
	}
	found := false
	for _, c := range updated.Status.Conditions {
		if c.Type == remedySystemType.NodeConditionDiskFail && c.Status == corev1.ConditionTrue {
			found = true
		}
	}
	if !found {
		t.Fatalf("expected DiskFail condition with True status")
	}
	if updated.Annotations[BceRemedySystemTaskIDAnnotationKey] != "task-1" {
		t.Fatalf("expected annotation set, got: %v", updated.Annotations)
	}
}

func TestUpdateNodeCondition_ClosedRemovesAnnotationAndSetsFalse(t *testing.T) {
	node := &corev1.Node{ObjectMeta: metav1.ObjectMeta{Name: "node-1", Annotations: map[string]string{BceRemedySystemTaskIDAnnotationKey: "task-1"}}}
	cli := newFakeClientWithObjects(t, node)
	cache := newCacheWithClient(cli)

	tsk := &Task{}
	args := &remedySys.TaskCommonArgs{TaskID: "task-1", InstanceID: "i-1", ErrResult: "BccHardwareError"}
	if err := tsk.updateNodeCondition(context.TODO(), cache, &remedySystemType.Node{NodeName: "node-1", ClusterID: "c-1"}, args, remedySys.TaskClosed); err != nil {
		t.Fatalf("updateNodeCondition err: %v", err)
	}

	var updated corev1.Node
	if err := cli.Get(context.TODO(), client.ObjectKey{Name: "node-1"}, &updated); err != nil {
		t.Fatalf("get updated node err: %v", err)
	}
	found := false
	for _, c := range updated.Status.Conditions {
		if c.Type == remedySystemType.NodeConditionHardWareFail && c.Status == corev1.ConditionFalse {
			found = true
		}
	}
	if !found {
		t.Fatalf("expected HardwareFail condition with False status")
	}
	if _, ok := updated.Annotations[BceRemedySystemTaskIDAnnotationKey]; ok {
		t.Fatalf("expected annotation removed, got: %v", updated.Annotations)
	}
}

// NOTE: updateNodeCondition logs taskArgs.TaskID before nil-check in this package,
// so we intentionally skip a nil taskArgs test here to avoid panic.
