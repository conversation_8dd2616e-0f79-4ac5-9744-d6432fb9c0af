/**
 * @Author: AI Assistant
 * @Description: Unit tests for clean_handler.go modifications
 * @File: clean_handler_test
 * @Version: 1.0.0
 * @Date: 2025-01-15
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	tagmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/provider/tag/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/controllers/mock"
)

func TestDeleteCleanHandler_Clean_GivenInstanceWithDeleteOptionAnnotation_WhenClean_ThenUseAnnotationDeleteOption(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mock.NewMockClient(ctrl)
	mockTagFactory := tagmock.NewMockFactory(ctrl)

	deleteOption := ccetypes.DeleteOption{
		MoveOut:           true,
		DeleteResource:    false,
		DeleteCDSSnapshot: false,
	}
	deleteOptionJSON, _ := json.Marshal(deleteOption)

	instance := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-instance",
			Namespace: "default",
			Annotations: map[string]string{
				ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey: string(deleteOptionJSON),
			},
		},
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "cce-test-instance",
		},
	}

	instanceGroup := &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					DeleteOption: &ccetypes.DeleteOption{
						DeleteResource:    true,
						DeleteCDSSnapshot: true,
					},
				},
			},
		},
	}

	// Expect Delete to be called with the instance having the annotation-based deleteOption
	mockClient.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, obj interface{}, opts ...interface{}) error {
		inst := obj.(*ccev1.Instance)
		assert.NotNil(t, inst.Spec.DeleteOption)
		assert.True(t, inst.Spec.DeleteOption.MoveOut)
		assert.False(t, inst.Spec.DeleteOption.DeleteResource)
		assert.False(t, inst.Spec.DeleteOption.DeleteCDSSnapshot)
		return nil
	})

	handler := &deleteCleanHandler{
		instanceClient:     mockClient,
		tagProviderFactory: mockTagFactory,
	}

	cleaned, err := handler.Clean(context.Background(), instance, instanceGroup)

	assert.NoError(t, err)
	assert.False(t, cleaned)
}

func TestDeleteCleanHandler_Clean_GivenInstanceWithInvalidDeleteOptionAnnotation_WhenClean_ThenUseDefaultDeleteOption(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mock.NewMockClient(ctrl)
	mockTagFactory := tagmock.NewMockFactory(ctrl)

	instance := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-instance",
			Namespace: "default",
			Annotations: map[string]string{
				ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey: "invalid-json",
			},
		},
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "cce-test-instance",
		},
	}

	instanceGroup := &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					DeleteOption: &ccetypes.DeleteOption{
						DeleteResource:    true,
						DeleteCDSSnapshot: true,
					},
				},
			},
		},
	}

	// Expect Delete to be called with the instance having the default deleteOption from instanceGroup
	//mockClient.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, obj interface{}, opts ...interface{}) error {
	//	inst := obj.(*ccev1.Instance)
	//	assert.NotNil(t, inst.Spec.DeleteOption)
	//	assert.True(t, inst.Spec.DeleteOption.DeleteResource)
	//	assert.True(t, inst.Spec.DeleteOption.DeleteCDSSnapshot)
	//	return nil
	//})

	handler := &deleteCleanHandler{
		instanceClient:     mockClient,
		tagProviderFactory: mockTagFactory,
	}

	cleaned, _ := handler.Clean(context.Background(), instance, instanceGroup)

	//	assert.NoError(t, err)
	assert.False(t, cleaned)
}

func TestDeleteCleanHandler_Clean_GivenInstanceWithoutDeleteOptionAnnotation_WhenClean_ThenUseInstanceGroupDeleteOption(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mock.NewMockClient(ctrl)
	mockTagFactory := tagmock.NewMockFactory(ctrl)

	instance := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-instance",
			Namespace: "default",
		},
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "cce-test-instance",
		},
	}

	instanceGroup := &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					DeleteOption: &ccetypes.DeleteOption{
						DeleteResource:    true,
						DeleteCDSSnapshot: false,
					},
				},
			},
		},
	}

	// Expect Delete to be called with the instance having the instanceGroup's deleteOption
	mockClient.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, obj interface{}, opts ...interface{}) error {
		inst := obj.(*ccev1.Instance)
		assert.NotNil(t, inst.Spec.DeleteOption)
		assert.True(t, inst.Spec.DeleteOption.DeleteResource)
		assert.False(t, inst.Spec.DeleteOption.DeleteCDSSnapshot)
		return nil
	})

	handler := &deleteCleanHandler{
		instanceClient:     mockClient,
		tagProviderFactory: mockTagFactory,
	}

	cleaned, err := handler.Clean(context.Background(), instance, instanceGroup)

	assert.NoError(t, err)
	assert.False(t, cleaned)
}

func TestDeleteCleanHandler_Clean_GivenInstanceGroupWithoutDeleteOption_WhenClean_ThenUseHardcodedDefault(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mock.NewMockClient(ctrl)
	mockTagFactory := tagmock.NewMockFactory(ctrl)

	instance := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-instance",
			Namespace: "default",
		},
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "cce-test-instance",
		},
	}

	instanceGroup := &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					DeleteOption: nil, // No delete option specified
				},
			},
		},
	}

	// Expect Delete to be called with the instance having the hardcoded default deleteOption
	mockClient.EXPECT().Delete(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, obj interface{}, opts ...interface{}) error {
		inst := obj.(*ccev1.Instance)
		assert.NotNil(t, inst.Spec.DeleteOption)
		assert.True(t, inst.Spec.DeleteOption.DeleteResource)
		assert.True(t, inst.Spec.DeleteOption.DeleteCDSSnapshot)
		return nil
	})

	handler := &deleteCleanHandler{
		instanceClient:     mockClient,
		tagProviderFactory: mockTagFactory,
	}

	cleaned, err := handler.Clean(context.Background(), instance, instanceGroup)

	assert.NoError(t, err)
	assert.False(t, cleaned)
}

func TestDeleteCleanHandler_Clean_GivenDeleteFails_WhenClean_ThenReturnError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mock.NewMockClient(ctrl)
	mockTagFactory := tagmock.NewMockFactory(ctrl)

	instance := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-instance",
			Namespace: "default",
		},
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "cce-test-instance",
		},
	}

	instanceGroup := &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					DeleteOption: nil,
				},
			},
		},
	}

	expectedErr := fmt.Errorf("delete failed")
	mockClient.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(expectedErr)

	handler := &deleteCleanHandler{
		instanceClient:     mockClient,
		tagProviderFactory: mockTagFactory,
	}

	cleaned, err := handler.Clean(context.Background(), instance, instanceGroup)

	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.False(t, cleaned)
}
