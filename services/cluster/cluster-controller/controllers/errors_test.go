package controllers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	ccerrors "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errors"
	corev1 "k8s.io/api/core/v1"
)

func TestErrorConstants(t *testing.T) {
	tests := []struct {
		name     string
		constant string
		expected string
	}{
		{
			name:     "ClusterMasterCreateFailed constant",
			constant: ClusterMasterCreateFailed,
			expected: "ClusterMasterCreateFailed",
		},
		{
			name:     "ClusterMasterDeleteFailed constant",
			constant: ClusterMasterDeleteFailed,
			expected: "ClusterMasterDeleteFailed",
		},
		{
			name:     "ClusterNodeDeleteFailed constant",
			constant: ClusterNodeDeleteFailed,
			expected: "ClusterNodeDeleteFailed",
		},
		{
			name:     "InstanceENINotDeleted constant",
			constant: InstanceENINotDeleted,
			expected: "InstanceENINotDeleted",
		},
		{
			name:     "InstanceVPCRouteNotDeleted constant",
			constant: InstanceVPCRouteNotDeleted,
			expected: "InstanceVPCRouteNotDeleted",
		},
		{
			name:     "InstanceBBCPrivateIPNotDeleted constant",
			constant: InstanceBBCPrivateIPNotDeleted,
			expected: "InstanceBBCPrivateIPNotDeleted",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.constant)
		})
	}
}

func TestErrorTypes(t *testing.T) {
	tests := []struct {
		name        string
		errorType   ccerrors.ErrType
		expectedMsg string
	}{
		{
			name:        "ErrTypeClusterMasterCreateFailed",
			errorType:   ErrTypeClusterMasterCreateFailed,
			expectedMsg: ClusterMasterCreateFailed,
		},
		{
			name:        "ErrTypeClusterMasterDeleteFailed",
			errorType:   ErrTypeClusterMasterDeleteFailed,
			expectedMsg: ClusterMasterDeleteFailed,
		},
		{
			name:        "ErrTypeClusterNodeDeleteFailed",
			errorType:   ErrTypeClusterNodeDeleteFailed,
			expectedMsg: ClusterNodeDeleteFailed,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that error type can be created and is not nil
			ctx := context.Background()
			err := tt.errorType.New(ctx, "test error")
			assert.NotNil(t, err)
			// Just verify the error is not nil and has some content
			assert.NotEmpty(t, err.Error())
		})
	}
}

func TestNodeConditionTypes(t *testing.T) {
	tests := []struct {
		name          string
		conditionType corev1.NodeConditionType
		expected      string
	}{
		{
			name:          "CCESubnetUnavailable condition type",
			conditionType: CCESubnetUnavailable,
			expected:      "CCESubnetUnavailable",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.conditionType))
		})
	}
}

func TestNodeConditionTypeUsage(t *testing.T) {
	t.Run("CCESubnetUnavailable can be used in NodeCondition", func(t *testing.T) {
		// Test that the condition type can be used in a real NodeCondition
		condition := corev1.NodeCondition{
			Type:    CCESubnetUnavailable,
			Status:  corev1.ConditionTrue,
			Reason:  "SubnetNotAvailable",
			Message: "CCE subnet is not available for this node",
		}

		assert.Equal(t, CCESubnetUnavailable, condition.Type)
		assert.Equal(t, corev1.ConditionTrue, condition.Status)
		assert.Equal(t, "SubnetNotAvailable", condition.Reason)
		assert.Equal(t, "CCE subnet is not available for this node", condition.Message)
	})

	t.Run("CCESubnetUnavailable type conversion", func(t *testing.T) {
		// Test type conversion and string representation
		conditionTypeStr := string(CCESubnetUnavailable)
		assert.Equal(t, "CCESubnetUnavailable", conditionTypeStr)

		// Test that it can be converted back
		convertedType := corev1.NodeConditionType(conditionTypeStr)
		assert.Equal(t, CCESubnetUnavailable, convertedType)
	})
}

func TestErrorTypeIntegration(t *testing.T) {
	t.Run("Error types can be used with different error messages", func(t *testing.T) {
		testCases := []struct {
			errorType ccerrors.ErrType
			message   string
		}{
			{ErrTypeClusterMasterCreateFailed, "failed to create master node"},
			{ErrTypeClusterMasterDeleteFailed, "failed to delete master node"},
			{ErrTypeClusterNodeDeleteFailed, "failed to delete worker node"},
		}

		for _, tc := range testCases {
			ctx := context.Background()
			err := tc.errorType.New(ctx, tc.message)
			assert.NotNil(t, err)
			assert.NotEmpty(t, err.Error())
		}
	})
}

func TestConstantsUniqueness(t *testing.T) {
	t.Run("All error constants should be unique", func(t *testing.T) {
		constants := []string{
			ClusterMasterCreateFailed,
			ClusterMasterDeleteFailed,
			ClusterNodeDeleteFailed,
			InstanceENINotDeleted,
			InstanceVPCRouteNotDeleted,
			InstanceBBCPrivateIPNotDeleted,
		}

		seen := make(map[string]bool)
		for _, constant := range constants {
			assert.False(t, seen[constant], "Duplicate constant found: %s", constant)
			seen[constant] = true
		}
	})

	t.Run("All node condition types should be unique", func(t *testing.T) {
		conditionTypes := []corev1.NodeConditionType{
			CCESubnetUnavailable,
		}

		seen := make(map[string]bool)
		for _, conditionType := range conditionTypes {
			typeStr := string(conditionType)
			assert.False(t, seen[typeStr], "Duplicate condition type found: %s", typeStr)
			seen[typeStr] = true
		}
	})
}
