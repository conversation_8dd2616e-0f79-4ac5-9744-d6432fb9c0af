/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  types.go
 * @Version: 1.0.0
 * @Date: 2020/6/23 4:47 下午
 */
package instancegroup

import (
	"context"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

const (
	UpdateFieldReplicas         = "replicas"
	UpdateFieldAutoscalerSpec   = "autoscalerSpec"
	UpdateFieldInstanceTemplate = "instanceTemplate"
	UpdateFieldPausedStatus     = "pausedStatus"
	UpdateFieldConfigure        = "configure"

	phaseReady                   = "ready"
	phaseNotReady                = "not_ready"
	InstancePhaseRunning         = "running"
	phaseReadySchedulingDisabled = "ready_scheduling_disabled"

	maxUserScriptSize float64 = 1024 * 16 // 16KB
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup Interface

type Interface interface {
	Create(ctx context.Context, spec *ccetypes.InstanceGroupSpec) (string, error)
	Update(ctx context.Context, spec *ccetypes.InstanceGroupSpec, opts UpdateOptions) error
	Delete(ctx context.Context, accountID, instanceGroupID string, opts DeleteOption) error
	UpdateCRD(ctx context.Context, instanceGroup *ccev1.InstanceGroup) error

	Get(ctx context.Context, accountID, instanceGroupID string) (*models.InstanceGroup, error)
	List(ctx context.Context, opts ListOptions) (*models.InstanceGroupList, error)
	GetCRD(ctx context.Context, accountID, instanceGroupID string) (*ccev1.InstanceGroup, error)

	ScaleUp(ctx context.Context, accountID, instanceGroupID string, replicas int, upReplica int) (string, error)
	ScaleDown(ctx context.Context, accountID, instanceGroupID string, instancesToBeRemoved []string, k8sNodesToBeRemoved []string, opts ScaleDownOption) (string, error)
	ScaleUpExistNode(ctx context.Context, service services.Interface, accountID, clusterID, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption, instanceCount int) (string, error)

	GetUpgradeComponentVersions(ctx context.Context, accountID, clusterID, instanceGroupID string) (*UpgradeComponents, error)
	CheckCASpecOkAfterDeleteInstances(ctx context.Context,
		clusterID string, instanceGroupDecrement map[string]int) ([]string, error)
	VerifyAndPadDefault(ctx context.Context, clusterSpec *ccetypes.ClusterSpec, instanceGroupSpec *ccetypes.InstanceGroupSpec, action string) error
	CreateInstanceGroupCR(ctx context.Context, instanceGroupID string, cluster *models.Cluster, spec *ccetypes.InstanceGroupSpec) error
}

// UpdateOptions - use UpdateOptions to specify updated fields and extra options
type UpdateOptions struct {
	// TODO: 可update的field应该做限制，这里枚举可update的field的白名单.
	Fields                 []string
	ScaleShrinkInstanceIDs []string // specify Instances to be added/removed
	ShrinkCleanPolicy      ccetypes.CleanPolicy
	InstanceDeleteOption   *ccetypes.DeleteOption
	PausedStatus           *ccetypes.PauseDetail
}

type DeleteOption struct {
	CleanPolicy          ccetypes.CleanPolicy
	InstanceDeleteOption *ccetypes.DeleteOption
}

// ScaleDownOption 从节点组移除节点选项
type ScaleDownOption struct {
	CleanPolicy          ccetypes.CleanPolicy
	InstanceDeleteOption *ccetypes.DeleteOption
}

type ListOptions struct {
	AccountID string
	ClusterID string
	Role      ccetypes.ClusterRole
	CAEnabled *bool
	// TODO: 分页和更多的筛选条件要看产品设计成怎么样。
	PageNo            int
	PageSize          int
	InstanceGroupID   string
	InstanceGroupName string
	OrderBy           string
	Order             string
	ChargingType      *string // 计费方式筛选
}

// UpgradeComponents 升级操作
type UpgradeComponents struct {
	Kubelet                UpgradeVersionList `json:"kubelet"`
	ContainerRuntime       UpgradeVersionList `json:"containerRuntime"`
	NvidiaContainerToolkit UpgradeVersionList `json:"nvidiaContainerToolkit"`
	XPUContainerToolkit    UpgradeVersionList `json:"xpuContainerToolkit"`
}

type UpgradeVersionList struct {
	CurrentVersion    string             `json:"currentVersion"`
	ComponentVersions []ComponentVersion `json:"componentVersions"`
}

type ComponentVersion struct {
	TargetVersion string `json:"targetVersion"`
	NeedDrainNode bool   `json:"needDrainNode"`
}
