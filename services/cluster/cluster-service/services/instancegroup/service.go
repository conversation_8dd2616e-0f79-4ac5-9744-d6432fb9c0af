/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instanceGroupService
 * @Version: 1.0.0
 * @Date: 2020/6/23 5:36 下午
 */
package instancegroup

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/hashicorp/go-multierror"
	"k8s.io/client-go/util/workqueue"

	"github.com/hashicorp/go-version"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/esg"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	k8serr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	kerrors "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/constant"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/task/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/securitygroup"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils/taints"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/controllers/instancegroup"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec"
	fillclients "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/quota"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/userscript"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/problem-detector-service/services/remedyrules"
)

type InstanceGroupService struct {
	log       logger.InterfaceEx
	model     models.Interface
	K8SClient meta.Interface

	filler                       *fillspec.BaseFiller
	fillClients                  fillclients.Clients
	quotaClient                  quota.Interface
	handler                      string
	rand                         utils.RandStringFunc
	skipUpdateInstanceGroupModel bool
	config                       *configuration.Config
	clientSet                    *clientset.ClientSet
}

const orphanInstanceSelector = "!" + ccetypes.InstanceGroupIDLabelKey + "," + ccetypes.ClusterIDLabelKey + "=%s," + ccetypes.ClusterRoleLabelKey + "=%s"

func NewService(ctx context.Context, accountID, userID string, clients *clients.Clients,
	model models.Interface, config *configuration.Config, clientSet *clientset.ClientSet) (*InstanceGroupService, error) {

	filler := &fillspec.BaseFiller{
		SDKClients: clients,
		Config:     config,
		UserID:     userID,
		AccountID:  accountID,
	}
	fillClients := fillclients.NewClient(ctx)
	quotaClient, err := quota.NewClient(ctx, accountID, clients, model)
	if err != nil {
		return nil, err
	}

	return &InstanceGroupService{
		log:                          logger.WithValues("service", "InstanceGroup"),
		model:                        model,
		K8SClient:                    clients.MetaK8SClient,
		filler:                       filler,
		fillClients:                  fillClients,
		quotaClient:                  quotaClient,
		handler:                      config.Handler,
		rand:                         utils.RandString,
		skipUpdateInstanceGroupModel: config.SkipUpdateInstanceGroupModel,
		config:                       config,
		clientSet:                    clientSet,
	}, nil
}

// Create - 创建节点组
//
// PARAMS:
//   - ctx: context.Context
//   - spec: *ccetypes.InstanceGroupSpec
//
// RETURNS:
//
//	string: InstanceGroupID
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) Create(ctx context.Context, spec *ccetypes.InstanceGroupSpec) (string, error) {
	log := s.log.WithValues("method", "instanceGroupService.Create")

	log.Infof(ctx, "creating InstanceGroup, spec: %s", utils.ToJSON(spec))

	cluster, err := s.model.GetCluster(ctx, spec.ClusterID, spec.AccountID)
	if err != nil {
		log.Errorf(ctx, "failed to get cluster, err: %v", err)
		return "", err
	}

	if err := s.VerifyAndPadDefault(ctx, cluster.Spec, spec, "create"); err != nil {
		log.Errorf(ctx, "failed to verify and pad default InstanceGroup spec, err: %v", err)
		return "", err
	}

	// 创建的时候直接写数据库是为了用户能够直接看到创建的结果
	instanceGroupModel := InstanceGroupSpecToModel(spec)
	instanceGroupIDs, err := s.model.CreateInstanceGroups(ctx, []*models.InstanceGroup{instanceGroupModel})
	if err != nil {
		log.Errorf(ctx, "failed to save InstanceGroup into db, err: %v", err)
		return "", err
	}

	if err = s.CreateInstanceGroupCR(ctx, instanceGroupIDs[0], cluster, spec); err != nil {
		log.Errorf(ctx, "failed to save InstanceGroup CR into db, err: %v", err)
		return "", err
	}
	return instanceGroupIDs[0], nil
}

func (s *InstanceGroupService) CreateInstanceGroupCR(ctx context.Context, instanceGroupID string, cluster *models.Cluster, spec *ccetypes.InstanceGroupSpec) (err error) {
	log := s.log.WithValues("method", "instanceGroupService.CreateInstanceGroupCR")

	spec.CCEInstanceGroupID = instanceGroupID

	instanceTmplCount := len(spec.InstanceTemplates)

	// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
	if instanceTmplCount == 0 {
		if spec.InstanceTemplate.Labels == nil {
			spec.InstanceTemplate.Labels = map[string]string{}
		}
		spec.InstanceTemplate.Labels[ccetypes.ClusterIDLabelKey] = spec.ClusterID
		spec.InstanceTemplate.Labels[ccetypes.InstanceGroupIDLabelKey] = spec.CCEInstanceGroupID
		spec.InstanceTemplate.Labels[ccetypes.ClusterRoleLabelKey] = string(spec.ClusterRole)

		if spec.InstanceTemplate.Annotations == nil {
			spec.InstanceTemplate.Annotations = map[string]string{}
		}

		if spec.ClusterAutoscalerSpec != nil && spec.ClusterAutoscalerSpec.Enabled {
			spec.InstanceTemplate.Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
		}
		spec.InstanceTemplate.InstanceGroupID = spec.CCEInstanceGroupID
		spec.InstanceTemplate.InstanceGroupName = spec.InstanceGroupName
		spec.InstanceTemplate.InstanceDeployType = s.filler.Config.InstanceDeployType

		err = constant.CheckIllegalLabelORAnnotation(spec.InstanceTemplate.InstanceSpec)
		if err != nil {
			return err
		}

		// GPU节点组才处理
		// TODO 改成使用IsGPUType方法
		if spec.InstanceTemplate.InstanceType == bcc.InstanceTypeG1 ||
			spec.InstanceTemplate.InstanceType == bcc.InstanceTypeBBCGPU ||
			spec.InstanceTemplate.InstanceType == bcc.InstanceTypeHPAS ||
			spec.InstanceTemplate.NeedGPU {
			clusterCRD, err := s.clientSet.MetaClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, cluster.Spec.ClusterID, &metav1.GetOptions{})
			if err != nil {
				log.Errorf(ctx, "failed to get cluster CRD, err: %v", err)
			}
			clusterToolkitVersion := ""
			if clusterCRD != nil {
				clusterToolkitVersion = clusterCRD.Spec.NvidiaContainerToolkitVersion
			}
			// nvidia-container-toolkit版本在InstanceTemplate中，该字段不保存到数据库
			// 当集群k8s版本等于或高于1.28时，新建节点时，默认创建最新版本，低版本不保存，走存量逻辑
			isAfter, err := cluster.Spec.K8SVersion.IsAfterOrEqual(ccetypes.K8S_1_28_8)
			if err != nil {
				logger.Errorf(ctx, "clusterSpec.K8SVersion.IsAfterOrEqual failed: %v", err)
			}
			if spec.InstanceTemplate.NvidiaContainerToolkitVersion == "" && isAfter {
				// 如果节点上NvidiaContainerToolkitVersion未指定，则使用集群的toolkit版本
				if clusterToolkitVersion == "" {
					spec.InstanceTemplate.NvidiaContainerToolkitVersion = ccetypes.GetInstanceDefaultInstallNvidiaContainerToolkitVersion(cluster.Spec.K8SVersion,
						spec.InstanceTemplate.RuntimeType, spec.InstanceTemplate.RuntimeVersion,
						string(spec.InstanceTemplate.InstanceOS.OSName), spec.InstanceTemplate.InstanceOS.OSVersion)
				} else {
					spec.InstanceTemplate.NvidiaContainerToolkitVersion = clusterToolkitVersion
				}
			}
		}

		// XPU Container Toolkit版本设置 - 针对昆仑芯节点（InstanceType=25）
		if spec.InstanceTemplate.InstanceType == bcc.InstanceTypeKunlun && spec.InstanceTemplate.XPUContainerToolkitVersion == "" {
			spec.InstanceTemplate.XPUContainerToolkitVersion = ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
				cluster.Spec.K8SVersion,
				spec.InstanceTemplate.RuntimeType,
				spec.InstanceTemplate.RuntimeVersion,
				string(spec.InstanceTemplate.InstanceOS.OSName),
				spec.InstanceTemplate.InstanceOS.OSVersion)
		}

		if spec.InstanceTemplate.DeploySetID == "" && len(spec.InstanceTemplate.DeploySetIDs) == 1 {
			spec.InstanceTemplate.DeploySetID = spec.InstanceTemplate.DeploySetIDs[0]
		}
	} else {
		for i := 0; i < instanceTmplCount; i++ {

			err = constant.CheckIllegalLabelORAnnotation(spec.InstanceTemplates[i].InstanceSpec)
			if err != nil {
				return err
			}

			if spec.InstanceTemplates[i].Labels == nil {
				spec.InstanceTemplates[i].Labels = map[string]string{}
			}
			spec.InstanceTemplates[i].Labels[ccetypes.ClusterIDLabelKey] = spec.ClusterID
			spec.InstanceTemplates[i].Labels[ccetypes.InstanceGroupIDLabelKey] = spec.CCEInstanceGroupID
			spec.InstanceTemplates[i].Labels[ccetypes.ClusterRoleLabelKey] = string(spec.ClusterRole)

			if spec.InstanceTemplates[i].Annotations == nil {
				spec.InstanceTemplates[i].Annotations = map[string]string{}
			}

			if spec.ClusterAutoscalerSpec != nil && spec.ClusterAutoscalerSpec.Enabled {
				spec.InstanceTemplates[i].Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
			}
			spec.InstanceTemplates[i].InstanceGroupID = spec.CCEInstanceGroupID
			spec.InstanceTemplates[i].InstanceGroupName = spec.InstanceGroupName
			spec.InstanceTemplates[i].InstanceDeployType = s.filler.Config.InstanceDeployType

			if spec.InstanceTemplates[i].DeploySetID == "" && len(spec.InstanceTemplates[i].DeploySetIDs) == 1 {
				spec.InstanceTemplates[i].DeploySetID = spec.InstanceTemplates[i].DeploySetIDs[0]
			}

			if i == 0 {
				spec.InstanceTemplate = spec.InstanceTemplates[0]
			}

			// GPU节点组才处理
			// TODO 改成使用IsGPUType方法
			if spec.InstanceTemplates[i].InstanceType == bcc.InstanceTypeG1 ||
				spec.InstanceTemplates[i].InstanceType == bcc.InstanceTypeBBCGPU ||
				spec.InstanceTemplates[i].InstanceType == bcc.InstanceTypeHPAS ||
				spec.InstanceTemplates[i].NeedGPU {
				clusterCRD, err := s.clientSet.MetaClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, cluster.Spec.ClusterID, &metav1.GetOptions{})
				if err != nil {
					log.Errorf(ctx, "failed to get cluster CRD, err: %v", err)
				}
				clusterToolkitVersion := ""
				if clusterCRD != nil {
					clusterToolkitVersion = clusterCRD.Spec.NvidiaContainerToolkitVersion
				}
				// 写入nvidia-container-toolkit版本在InstanceTemplate中，该字段不保存到数据库
				// key 格式为 '{K8SVersion}-{os}-{runtimeType}-{runtimeVersion}'
				// nvidia-container-toolkit版本在InstanceTemplate中，该字段不保存到数据库
				// 当集群k8s版本等于或高于1.28时，新建节点组时，默认创建最新版本，低版本不保存，走存量逻辑
				isAfter, err := cluster.Spec.K8SVersion.IsAfterOrEqual(ccetypes.K8S_1_28_8)
				if err != nil {
					logger.Errorf(ctx, "clusterSpec.K8SVersion.IsAfterOrEqual failed: %v", err)
				}
				if spec.InstanceTemplates[i].NvidiaContainerToolkitVersion == "" && isAfter {
					// 如果节点上NvidiaContainerToolkitVersion未指定，则使用集群的toolkit版本
					if clusterToolkitVersion == "" {
						targetVersion := ccetypes.GetInstanceDefaultInstallNvidiaContainerToolkitVersion(cluster.Spec.K8SVersion,
							spec.InstanceTemplates[i].RuntimeType, spec.InstanceTemplates[i].RuntimeVersion,
							string(spec.InstanceTemplates[i].InstanceOS.OSName), spec.InstanceTemplates[i].InstanceOS.OSVersion)
						spec.InstanceTemplates[i].NvidiaContainerToolkitVersion = targetVersion
						spec.InstanceTemplate.NvidiaContainerToolkitVersion = targetVersion
					} else {
						spec.InstanceTemplates[i].NvidiaContainerToolkitVersion = clusterToolkitVersion
						spec.InstanceTemplate.NvidiaContainerToolkitVersion = clusterToolkitVersion
					}
				}
			}
			// XPU Container Toolkit版本设置 - 针对昆仑芯节点（InstanceType=25）
			if spec.InstanceTemplates[i].InstanceType == bcc.InstanceTypeKunlun && spec.InstanceTemplates[i].XPUContainerToolkitVersion == "" {
				targetXPUVersion := ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
					cluster.Spec.K8SVersion,
					spec.InstanceTemplates[i].RuntimeType,
					spec.InstanceTemplates[i].RuntimeVersion,
					string(spec.InstanceTemplates[i].InstanceOS.OSName),
					spec.InstanceTemplates[i].InstanceOS.OSVersion)
				spec.InstanceTemplates[i].XPUContainerToolkitVersion = targetXPUVersion
				spec.InstanceTemplate.XPUContainerToolkitVersion = targetXPUVersion
			}
		}
	}

	spec.Selector = &ccetypes.InstanceSelector{
		LabelSelector: metav1.LabelSelector{
			MatchLabels: map[string]string{
				ccetypes.InstanceGroupIDLabelKey: spec.CCEInstanceGroupID,
			},
		},
	}

	ig := &ccev1.InstanceGroup{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: consts.MetaClusterDefaultNamespace,
			Name:      spec.CCEInstanceGroupID,
			Labels: map[string]string{
				ccetypes.ClusterIDLabelKey:       spec.ClusterID,
				ccetypes.InstanceGroupIDLabelKey: spec.CCEInstanceGroupID,
				ccetypes.ClusterRoleLabelKey:     string(spec.InstanceTemplate.ClusterRole),
			},
			Finalizers: []string{ccev1.InstanceGroupFinalizer},
		},
		Spec: *spec,
		Status: ccetypes.InstanceGroupStatus{
			ReadyReplicas: 0,
			Pause:         &ccetypes.PauseDetail{},
		},
	}

	log.Infof(ctx, "CreateInstanceGroup: %s", utils.ToJSON(ig))

	ig, err = s.K8SClient.CreateInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, ig)
	if err != nil {
		log.Errorf(ctx, "failed to create InstanceGroup in meta cluster, err: %v", err)
		return err
	}

	log.WithValues("instanceGroupID", ig.Spec.CCEInstanceGroupID).Infof(ctx, "InstanceGroup created")
	return nil
}

// Update - 更新节点组Spec
//
// PARAMS:
//   - ctx: context.Context
//   - spec: *ccetypes.InstanceGroupSpec
//   - opts: update options
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) Update(ctx context.Context, spec *ccetypes.InstanceGroupSpec, opts UpdateOptions) error {
	log := s.log.WithValues("method", "instanceGroupService.Update").
		WithValues("instanceGroupID", spec.CCEInstanceGroupID).
		WithValues("updateOptions", utils.ToJSON(opts))

	log.Infof(ctx, "update options: %+v, updating InstanceGroup spec to %s", opts, utils.ToJSON(spec))
	if len(opts.Fields) == 0 {
		return fmt.Errorf("empty update field")
	}

	// 查询 Cluster
	cluster, err := s.model.GetCluster(ctx, spec.ClusterID, spec.AccountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		return err
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", spec.ClusterID)
		return fmt.Errorf("cluster %s not exist", spec.ClusterID)
	}

	ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, spec.CCEInstanceGroupID, &metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get InstanceGroup from meta cluster, err: %v", err)
		return err
	}
	logger.Infof(ctx, "get InstanceGroup from meta cluster: %s", utils.ToJSON(ig))

	newSpec := ig.Spec
	newStatus := ig.Status

	userScriptService := userscript.NewUserScriptService(s.model)

	shouldResetPaused := false
	shouldCheckWorkflowConflict := true
	for _, field := range opts.Fields {
		switch field {
		case UpdateFieldReplicas:
			if newSpec.Replicas == spec.Replicas {
				continue
			}

			if newSpec.Replicas < spec.Replicas {
				quota, err := s.quotaClient.GetNodeQuota(ctx, spec.ClusterID)
				if err != nil {
					log.Errorf(ctx, "failed to get cluster node quota info, err: %v", err)
					return err
				}
				if spec.Replicas-newSpec.Replicas > quota.Quota-quota.Used {
					log.WithValues("quota", quota.Quota, "used", quota.Used, "nodeToBeAdded", spec.Replicas-newSpec.Replicas).Errorf(ctx, "exceed cluster node quota")
					return fmt.Errorf("exceed cluster node quota")
				}
				// 增加对托管集群规格的限制
				err = s.checkCCENodeNumLimitAndReplicas(ctx, cluster.Spec.ClusterID, cluster.Spec.AccountID, spec.Replicas-newSpec.Replicas)
				if err != nil {
					return err
				}

				// scale, 如果指定了需要加入节点组的instance，对该instance做标记，从而controller能够感知
				for _, instanceID := range opts.ScaleShrinkInstanceIDs {
					if err := s.markInstanceAsCandidate(ctx, spec.AccountID, instanceID, spec.CCEInstanceGroupID); err != nil {
						log.Errorf(ctx, "failed to mark instance to join InstanceGroup, err: %v", err)
						return err
					}
				}
			} else { // 缩容
				labelSelector := metav1.LabelSelector{
					MatchLabels: map[string]string{
						ccetypes.ClusterIDLabelKey:       spec.ClusterID,
						ccetypes.InstanceGroupIDLabelKey: spec.CCEInstanceGroupID,
						ccetypes.ClusterRoleLabelKey:     string(ccetypes.ClusterRoleNode),
					},
				}
				labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

				logger.Infof(ctx, "ListNodeinstance selectorLabel: %v", labelSelectorStr)
				instanceList, err := s.K8SClient.ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
					LabelSelector: labelSelectorStr,
				})
				if err != nil {
					log.Errorf(ctx, "failed to list instances, err: %v", err)
					return err
				}
				scaleDownDisabledMap := make(map[string]struct{})
				for _, ins := range instanceList.Items {
					// 记录开启缩容保护的节点
					if ins.Spec.ScaleDownDisabled {
						scaleDownDisabledMap[ins.Spec.CCEInstanceID] = struct{}{}
					}
				}
				// 缩容时，预期节点数应该大于等于需要排除开启缩容保护的节点
				if spec.Replicas < len(scaleDownDisabledMap) {
					logger.Errorf(ctx, "scale-down-disabled instances total is %d, could not scale down to %d", len(scaleDownDisabledMap), spec.Replicas)
					return fmt.Errorf("scale-down-disabled instances total is %d, could not scale down to %d", len(scaleDownDisabledMap), spec.Replicas)
				}

				// shrink，如果指定了需要移出的instance，设置该instance优先级为最低，从而controller能够感知
				for _, instanceID := range opts.ScaleShrinkInstanceIDs {
					// (去前端逻辑保持一致) 需要跳过开启缩容保护的节点
					if _, ok := scaleDownDisabledMap[instanceID]; ok {
						continue
					}
					if err := s.markInstanceToBeRemoved(ctx, spec.AccountID, instanceID, spec.CCEInstanceGroupID, opts.ShrinkCleanPolicy,
						opts.InstanceDeleteOption); err != nil {

						log.Errorf(ctx, "failed to mark instance to be removed from InstanceGroup, err: %v", err)
						return err
					}
				}
			}
			newSpec.Replicas = spec.Replicas
			newStatus.Pause = &ccetypes.PauseDetail{
				Paused:     false,
				Reason:     "",
				PausedTime: nil,
			}
			shouldResetPaused = true
		case UpdateFieldAutoscalerSpec:
			if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeHPAS && spec.ClusterAutoscalerSpec.Enabled {
				return models.ErrHPASNotImplemented.New(ctx, "HPAS set autoscaler not implemented")
			}
			// 更新ca配置不需要检查workflow冲突
			shouldCheckWorkflowConflict = false
			if err := validateClusterAutoscalerSpec(spec.ClusterAutoscalerSpec); err != nil {
				log.Errorf(ctx, "invalid cluster autoscaler spec, err: %v", err)
				return err
			}
			newSpec.ClusterAutoscalerSpec = spec.ClusterAutoscalerSpec
			if newSpec.ClusterAutoscalerSpec != nil && newSpec.ClusterAutoscalerSpec.Enabled {
				newSpec.InstanceTemplate.Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
			} else {
				delete(newSpec.InstanceTemplate.Labels, ccetypes.ClusterAutoscalerEnabledLabelKey)
			}
		case UpdateFieldInstanceTemplate:
			// 不是所有字段都支持用户更新，并且不是所有字段都返回给用户，所以不能直接覆盖
			newSpec.InstanceTemplate.DeployCustomConfig.KubeletRootDir = spec.InstanceTemplate.DeployCustomConfig.KubeletRootDir
			newSpec.InstanceTemplate.DeployCustomConfig.DockerConfig.DockerDataRoot = spec.InstanceTemplate.DeployCustomConfig.DockerConfig.DockerDataRoot

			// TODO 改成调用 userscript.CreateUserScripts
			if spec.InstanceTemplate.DeployCustomConfig.PreUserScript != "" {

				if utils.CheckStringGreaterThanMaxSize(spec.InstanceTemplate.DeployCustomConfig.PreUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					logger.Errorf(ctx, "Check user script size: %d greater than max size: %d ", len(spec.InstanceTemplate.DeployCustomConfig.PreUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
					return fmt.Errorf("脚本数据不能超过16KB")
				}

				userScriptID, err := userScriptService.Create(ctx, spec.InstanceTemplate.AccountID, spec.InstanceTemplate.UserID, spec.InstanceTemplate.DeployCustomConfig.PreUserScript)
				if err != nil {
					logger.Errorf(ctx, "Create user script failed: %v", err)
					return err
				}

				spec.InstanceTemplate.DeployCustomConfig.PreUserScript = userScriptID
			}

			if spec.InstanceTemplate.DeployCustomConfig.PostUserScript != "" {
				if utils.CheckStringGreaterThanMaxSize(spec.InstanceTemplate.DeployCustomConfig.PostUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					logger.Errorf(ctx, "Check user script size: %d greater than max size: %d ", len(spec.InstanceTemplate.DeployCustomConfig.PostUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
					return fmt.Errorf("脚本数据不能超过16KB")
				}

				userScriptID, err := userScriptService.Create(ctx, spec.InstanceTemplate.AccountID, spec.InstanceTemplate.UserID, spec.InstanceTemplate.DeployCustomConfig.PostUserScript)
				if err != nil {
					logger.Errorf(ctx, "Create user script failed: %v", err)
					return err
				}

				spec.InstanceTemplate.DeployCustomConfig.PostUserScript = userScriptID
			}

			// 校验
			if cluster.Spec.K8SCustomConfig.EnableHostname && spec.InstanceTemplate.InstanceName != strings.ToLower(spec.InstanceTemplate.InstanceName) {
				logger.Errorf(ctx, "instanceName: %s does not support uppercase letters", spec.InstanceTemplate.InstanceName)
				return fmt.Errorf("instanceName: %s does not support uppercase letters", spec.InstanceTemplate.InstanceName)
			}

			newSpec.InstanceTemplate.DeployCustomConfig.PreUserScript = spec.InstanceTemplate.DeployCustomConfig.PreUserScript
			newSpec.InstanceTemplate.DeployCustomConfig.PostUserScript = spec.InstanceTemplate.DeployCustomConfig.PostUserScript
			newSpec.InstanceTemplate.EhcClusterID = spec.InstanceTemplate.EhcClusterID

			newSpec.InstanceTemplate.DeployCustomConfig.EnableCordon = spec.InstanceTemplate.DeployCustomConfig.EnableCordon
			newSpec.InstanceTemplate.DeployCustomConfig.PostUserScriptFailedAutoCordon = spec.InstanceTemplate.DeployCustomConfig.PostUserScriptFailedAutoCordon

			newSpec.InstanceTemplate.Tags = spec.InstanceTemplate.Tags
			newSpec.InstanceTemplate.Labels = spec.InstanceTemplate.Labels
			// 不允许删除或修改的labels
			newSpec.InstanceTemplate.Labels[ccetypes.ClusterIDLabelKey] = newSpec.ClusterID
			newSpec.InstanceTemplate.Labels[ccetypes.InstanceGroupIDLabelKey] = newSpec.CCEInstanceGroupID
			newSpec.InstanceTemplate.Labels[ccetypes.ClusterRoleLabelKey] = string(newSpec.ClusterRole)
			if err = taints.CheckTaintsValidation(spec.InstanceTemplate.Taints); err != nil {
				log.Errorf(ctx, "invalid taints: %v", spec.InstanceTemplate.Taints)
				return models.ErrTaintsInvalid.New(ctx, err.Error())
			}
			newSpec.InstanceTemplate.Taints = spec.InstanceTemplate.Taints
			newSpec.InstanceTemplate.Annotations = spec.InstanceTemplate.Annotations
			newSpec.InstanceTemplate.CheckGPUDriver = spec.InstanceTemplate.CheckGPUDriver

		case UpdateFieldPausedStatus:
			newStatus.Pause = opts.PausedStatus
			shouldResetPaused = true
		case UpdateFieldConfigure:
			//同节点组中付费方式需要一致
			var newTemp []ccetypes.InstanceTemplate
			if ig.Spec.InstanceTemplate.InstanceChargingType != "" {
				for _, instanceTemplate := range spec.InstanceTemplates {
					if instanceTemplate.InstanceChargingType == "" {
						instanceTemplate.InstanceChargingType = ig.Spec.InstanceTemplate.InstanceChargingType
						instanceTemplate.InstancePreChargingOption = ig.Spec.InstanceTemplate.InstancePreChargingOption
					}
					if instanceTemplate.InstanceChargingType != "" && instanceTemplate.InstanceChargingType != ig.Spec.InstanceTemplate.InstanceChargingType {
						logger.Warnf(ctx, "InstanceChargingType of InstanceGroup and instanceTemplate must be the same")
						instanceTemplate.InstanceChargingType = ig.Spec.InstanceTemplate.InstanceChargingType
						instanceTemplate.InstancePreChargingOption = ig.Spec.InstanceTemplate.InstancePreChargingOption
					}
					newTemp = append(newTemp, instanceTemplate)
				}
			}
			spec.InstanceTemplates = newTemp
			spec, err := s.verifyAndUpdateInstanceGroupSpec(ctx, ig, spec)
			if err != nil {
				return err
			}
			newSpec = *spec

		default:
			return fmt.Errorf("unsupported update field in InstanceGroup spec")
		}
	}

	if shouldCheckWorkflowConflict {
		// 检查节点组是否存在冲突任务，不能更新
		err = s.exitConflictWorkflowByInstanceGroup(ctx, spec.CCEInstanceGroupID)
		if err != nil {
			log.Errorf(ctx, "exitConflictWorkflowByInstanceGroup, err: %v", err)
			return models.ErrWorkflowConflict.New(ctx, err.Error())
		}
	}

	ig.Spec = newSpec
	if shouldResetPaused {
		ig.Status.Pause = newStatus.Pause
	}

	logger.Infof(ctx, "instancegroup: %s will update to: %s", ig.Name, utils.ToJSON(ig))

	ig, err = s.K8SClient.UpdateInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, spec.CCEInstanceGroupID, ig)
	if err != nil {
		log.Errorf(ctx, "failed to update InstanceGroup to meta cluster, err: %v", err)
		return err
	}

	log.Infof(ctx, "InstanceGroup in meta cluster is updated")
	if !s.skipUpdateInstanceGroupModel {
		igModel, err := s.model.GetInstanceGroupByCCEID(ctx, spec.AccountID, spec.CCEInstanceGroupID)
		if err != nil {
			log.Errorf(ctx, "failed to get instanceGroup from db, err: %v", err)
			return err
		}

		igModel.Spec = &newSpec
		err = s.model.UpdateInstanceGroupSpec(ctx, spec.AccountID, spec.CCEInstanceGroupID, igModel.Spec)
		if err != nil {
			log.Errorf(ctx, "failed to update InstanceGroup spec in db, err: %v", err)
			return err
		}

		if shouldResetPaused {
			igModel.Status.Pause = newStatus.Pause
			if err := s.model.UpdateInstanceGroupStatus(ctx, spec.AccountID, spec.CCEInstanceGroupID, igModel.Status); err != nil {
				log.Errorf(ctx, "failed to update instanceGroup status in db, err: %v", err)
				return err
			}
		}
	}
	return err
}

// exitConflictWorkflowByInstanceGroup 检查节点组是否存在正在运行中的workflow
func (c *InstanceGroupService) exitConflictWorkflowByInstanceGroup(ctx context.Context, instanceGroupID string) error {
	workflowList, err := c.K8SClient.ListWorkflows(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, instanceGroupID),
	})
	if err != nil {
		logger.Errorf(ctx, "list workflows failed: %v", err)
		return err
	}
	for _, workflow := range workflowList.Items {
		// 校验是否存在冲突任务
		if workflow.Spec.WorkflowType != ccetypes.WorkflowTypeUpgradeKubeletConfig {
			continue
		}
		// 处于暂停、升级中、pending、确认中和删除中这几个状态时，不允许删除节点组内节点
		if workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePaused || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseUpgrading ||
			workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePending || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseVerifying ||
			workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseDeleting {
			return fmt.Errorf("instanceGroup %s is upgrading, can't update instanceGroup", instanceGroupID)
		}
	}

	return nil
}

// Delete - 删除节点组
//
// PARAMS:
//   - ctx: context.Context
//   - accountID: accountID
//   - instanceGroupID: instanceGroupID
//   - opts: delete options
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) Delete(ctx context.Context, accountID, instanceGroupID string, opts DeleteOption) error {
	log := s.log.WithValues("method", "instanceGroupService.Delete").
		WithValues("instanceGroupID", instanceGroupID)

	log.Infof(ctx, "deleting InstanceGroup, accountID: %s", accountID)
	if opts.CleanPolicy != "" {
		log = log.WithValues("cleanPolicy", opts.CleanPolicy)

		igModel, err := s.model.GetInstanceGroupByCCEID(ctx, accountID, instanceGroupID)
		if err != nil {
			log.Errorf(ctx, "failed to get instanceGroup from db, err: %v", err)
			return err
		}

		if igModel.Spec.CleanPolicy != opts.CleanPolicy {
			igModel.Spec.CleanPolicy = opts.CleanPolicy

			err := s.model.UpdateInstanceGroupSpec(ctx, accountID, instanceGroupID, igModel.Spec)
			if err != nil {
				log.Errorf(ctx, "failed to update InstanceGroup spec in db, err: %v", err)
				return err
			}

			ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
			if err != nil {
				log.Errorf(ctx, "failed to get InstanceGroup from meta cluster, err: %v", err)
				return err
			}
			ig.Spec.CleanPolicy = opts.CleanPolicy
			ig, err = s.K8SClient.UpdateInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, ig)
			if err != nil {
				log.Errorf(ctx, "failed to update InstanceGroup to meta cluster, err: %v", err)
				return err
			}
		}
	}

	if opts.InstanceDeleteOption != nil {
		instanceDeleteOptionJSON, err := json.Marshal(opts.InstanceDeleteOption)
		if err != nil {
			log.Errorf(ctx, "failed to marshal deletion option, err: %v", err)
			return err
		}

		instanceList, err := s.K8SClient.ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
			LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, instanceGroupID),
		})
		if err != nil {
			log.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to list instance belong to instanceGroup, err: %v", err)
			return err
		}

		// 批量更新instance annotation
		var wg sync.WaitGroup
		var errs []error
		wg.Add(len(instanceList.Items))
		buffer := make(chan int, 50)

		for i := 0; i < len(instanceList.Items); i++ {
			buffer <- i
			go func(instance *ccev1.Instance) {
				defer wg.Done()
				if instance.Annotations == nil {
					instance.Annotations = make(map[string]string)
				}
				instance.Annotations[ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey] = string(instanceDeleteOptionJSON)
				if err = s.K8SClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, instance.Name, instance); err != nil {
					log.Errorf(ctx, "failed to update instance to meta cluster, err: %v", err)
					errs = append(errs, err)
				}
				<-buffer
			}(&instanceList.Items[i])
		}
		wg.Wait()
		if len(errs) != 0 {
			return kerrors.NewAggregate(errs)
		}

	}

	if err := s.K8SClient.DeleteInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.DeleteOptions{}); err != nil {
		log.Errorf(ctx, "failed to delete InstanceGroup from meta cluster, err: %v", err)
		return err
	}

	log.Infof(ctx, "InstanceGroup deleted")
	return nil
}

// GetUpgradeComponentVersions 获取可升级版本信息。获取节点组所有节点的组件版本信息
// * 最后返回当前所有节点的最低版本
// * 可升级版本选择所有可升级版本的并集, 只返回比当前版本高的版本，runtime可升级版本存在docker和containerd时，只返回containerd版本。
func (s *InstanceGroupService) GetUpgradeComponentVersions(ctx context.Context, accountID, clusterID, instanceGroupID string) (*UpgradeComponents, error) {
	log := s.log.WithValues("method", "instanceGroupService.GetUpgradeComponentVersions").
		WithValues("instanceGroupID", instanceGroupID)

	// 获取集群信息，kubelet能升级到的最高版本从这里获取
	cluster, err := s.model.GetClusterByClusterID(ctx, clusterID)
	if err != nil {
		log.Errorf(ctx, "failed to get cluster, err: %v", err)
		return nil, err
	}

	// 获取节点组下所有节点
	k8sClient, err := s.clientSet.NewK8SClientWithClusterID(ctx, clusterID, s.model)
	if err != nil {
		return nil, fmt.Errorf("get k8s client failed: %v", err)
	}
	nodes, err := k8sClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, instanceGroupID),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list node belongs to instanceGroupID %s, err: %v", instanceGroupID, err)
	}
	nodeMap := make(map[string]*corev1.Node)
	for _, item := range nodes.Items {
		node := item
		for _, address := range node.Status.Addresses {
			if address.Type == corev1.NodeInternalIP {
				nodeMap[address.Address] = &node
			}
		}
	}

	InstancesCRD, err := s.clientSet.MetaClient.ListInstances(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s,%s=%s", ccetypes.ClusterIDLabelKey, clusterID, ccetypes.InstanceGroupIDLabelKey, instanceGroupID)},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to list instancesCRD instanceGroupID %s, err: %v", instanceGroupID, err)
	}

	//instanceCRDMap := make(map[string]*ccev1.Instance)
	result := &UpgradeComponents{
		Kubelet:                UpgradeVersionList{},
		ContainerRuntime:       UpgradeVersionList{},
		NvidiaContainerToolkit: UpgradeVersionList{},
		XPUContainerToolkit:    UpgradeVersionList{},
	}
	maxKubeletVersion := ComponentVersion{
		TargetVersion: string(cluster.Spec.K8SVersion),
	}

	result.Kubelet.ComponentVersions = append(result.Kubelet.ComponentVersions, maxKubeletVersion)

	targetRuntimeVersionMap := make(map[ccetypes.RuntimeVersion]struct{})
	targetToolkitVersionMap := make(map[string]struct{})
	targetXPUToolkitVersionMap := make(map[string]struct{})

	for _, item := range InstancesCRD.Items {
		crd := item
		node := nodeMap[crd.Status.Machine.VPCIP]
		if node != nil {
			// 获取当前节点kubelet版本，记录最低值。kubelet只能升级到控制面版本
			currentVersion := ccetypes.K8SVersion(strings.TrimPrefix(node.Status.NodeInfo.KubeletVersion, "v"))
			if result.Kubelet.CurrentVersion > string(currentVersion) || result.Kubelet.CurrentVersion == "" {
				result.Kubelet.CurrentVersion = string(currentVersion)
			}

			currentRuntimeVersion := ccetypes.RuntimeVersion(node.Status.NodeInfo.ContainerRuntimeVersion)
			if result.ContainerRuntime.CurrentVersion == "" {
				result.ContainerRuntime.CurrentVersion = string(currentRuntimeVersion)
			} else if result.ContainerRuntime.CurrentVersion != "" {
				isBefore, compareErr := currentRuntimeVersion.IsBefore(ccetypes.RuntimeVersion(result.ContainerRuntime.CurrentVersion))
				if compareErr == nil && isBefore {
					// 当前版本比记录的最低版本小的时候，更新result字段
					result.ContainerRuntime.CurrentVersion = string(currentRuntimeVersion)
				}
			}

			// 获取当前所有节点runtime版本可以升级版本的并集
			targetVersionRuntimeMap := currentRuntimeVersion.CanBeUpgradedToFE()
			for k, val := range targetVersionRuntimeMap {
				if _, exit := targetRuntimeVersionMap[k]; !exit {
					componentVersion := ComponentVersion{
						TargetVersion: string(k),
						NeedDrainNode: bool(val),
					}
					result.ContainerRuntime.ComponentVersions = append(result.ContainerRuntime.ComponentVersions, componentVersion)
					targetRuntimeVersionMap[k] = struct{}{}
				}
			}

			// toolkit
			if crd.Spec.InstanceType == bcc.InstanceTypeG1 || crd.Spec.InstanceType == bcc.InstanceTypeBBCGPU ||
				crd.Spec.InstanceType == bcc.InstanceTypeHPAS || crd.Spec.NeedGPU {
				currentToolkitVersion := crd.Spec.NvidiaContainerToolkitVersion
				if currentToolkitVersion == "" {
					// 兼容存量未获取到toolkit版本的节点，取默认安装版本
					currentToolkitVersion = utils.GetNvidiaToolkitVersion(&crd, cluster.Spec.K8SVersion)
				}

				// IsBefore 返回值：false 表示 v1 >= v2，true 表示 v1 < v2
				version1, err := version.NewVersion(currentToolkitVersion)
				if err != nil {
					logger.Infof(ctx, "failed to parse version %s, err: %v", currentToolkitVersion, err)
				}
				if result.NvidiaContainerToolkit.CurrentVersion == "" {
					result.NvidiaContainerToolkit.CurrentVersion = currentToolkitVersion
				} else {
					version2, err := version.NewVersion(result.NvidiaContainerToolkit.CurrentVersion)
					if err != nil {
						logger.Infof(ctx, "failed to parse version2 %s, err: %v", result.NvidiaContainerToolkit.CurrentVersion, err)
					}
					if version1.LessThan(version2) {
						result.NvidiaContainerToolkit.CurrentVersion = currentToolkitVersion
					}
				}
				// 获取当前所有节点可以升级的toolkit版本的并集
				os := ccetypes.GetToolkitVersionMapKeyOS(string(crd.Spec.InstanceOS.OSName), crd.Spec.InstanceOS.OSVersion)
				key := fmt.Sprintf("%s-%s-%s-%s", cluster.Spec.K8SVersion, os, crd.Spec.RuntimeType, crd.Spec.RuntimeVersion)
				targetVersions, getErr := ccetypes.GetSupportUpgradeToolkitVersion(key)
				if getErr != nil {
					return nil, getErr
				}

				for _, tVersion := range targetVersions {
					// 只展示比最低版本高的toolkit版本
					curVersion, err := version.NewVersion(result.NvidiaContainerToolkit.CurrentVersion)
					if err != nil {
						logger.Infof(ctx, "failed to parse version2 %s, err: %v", result.NvidiaContainerToolkit.CurrentVersion, err)
					}
					targetVersion, err := version.NewVersion(tVersion)
					if err != nil {
						logger.Infof(ctx, "failed to parse version2 %s, err: %v", result.NvidiaContainerToolkit.CurrentVersion, err)
					}
					if targetVersion.LessThanOrEqual(curVersion) {
						continue
					}
					if _, exit := targetToolkitVersionMap[tVersion]; !exit {
						componentVersion := ComponentVersion{
							TargetVersion: tVersion,
							NeedDrainNode: true,
						}
						result.NvidiaContainerToolkit.ComponentVersions = append(result.NvidiaContainerToolkit.ComponentVersions, componentVersion)
						targetToolkitVersionMap[tVersion] = struct{}{}
					}
				}
			}

			// XPU toolkit - 处理昆仑芯节点的XPU Container Toolkit版本
			// 单测要点：
			// 1. 正常场景：昆仑芯节点（InstanceType=25）正确获取XPU版本信息
			//    - 输入：昆仑芯节点CRD，包含XPUContainerToolkitVersion字段
			//    - 输出：result.XPUContainerToolkit包含当前版本和可升级版本列表
			// 2. 边界条件：
			//    - XPUContainerToolkitVersion为空字符串时，使用默认版本
			//    - 操作系统不支持XPU时，返回空的版本列表
			//    - 当前版本已是最新版本时，ComponentVersions为空
			// 3. 异常场景：
			//    - 版本解析失败时，记录日志但不中断流程
			//    - GetSupportUpgradeXPUToolkitVersion返回错误时，记录日志继续处理
			// 4. 并发安全：
			//    - targetXPUToolkitVersionMap的并发读写保护
			//    - 多个goroutine同时访问result.XPUContainerToolkit的线程安全
			// 5. 版本比较逻辑：
			//    - 只返回高于当前版本的升级版本
			//    - 版本排序按从高到低排列
			//    - 去重处理确保版本列表唯一性
			if crd.Spec.InstanceType == bcc.InstanceTypeKunlun {
				// ebc实例，在创建instance CRD时做了处理，根据IsomerismCard字段判断是昆仑芯的实例，都将instanceType类型转换为25，所以这里可以直接这样判断。
				// TODO 但是节点组模板未处理，ebc机器都是99.针对0节点组节点情况下，无法识别是不是需要返回xpu版本升级
				currentXPUToolkitVersion := crd.Spec.XPUContainerToolkitVersion
				if currentXPUToolkitVersion == "" {
					// 兼容存量未获取到xpu toolkit版本的节点，取默认安装版本
					currentXPUToolkitVersion = ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
						cluster.Spec.K8SVersion,
						crd.Spec.RuntimeType,
						crd.Spec.RuntimeVersion,
						string(crd.Spec.InstanceOS.OSName),
						crd.Spec.InstanceOS.OSVersion)
				}

				// IsBefore 返回值：false 表示 v1 >= v2，true 表示 v1 < v2
				version1, err := version.NewVersion(currentXPUToolkitVersion)
				if err != nil {
					logger.Infof(ctx, "failed to parse XPU version %s, err: %v", currentXPUToolkitVersion, err)
				}
				if result.XPUContainerToolkit.CurrentVersion == "" {
					result.XPUContainerToolkit.CurrentVersion = currentXPUToolkitVersion
				} else {
					version2, err := version.NewVersion(result.XPUContainerToolkit.CurrentVersion)
					if err != nil {
						logger.Infof(ctx, "failed to parse XPU version2 %s, err: %v", result.XPUContainerToolkit.CurrentVersion, err)
					}
					if version1.LessThan(version2) {
						result.XPUContainerToolkit.CurrentVersion = currentXPUToolkitVersion
					}
				}
				// 获取当前所有节点可以升级的xpu toolkit版本的并集
				os := ccetypes.GetXPUToolkitVersionMapKeyOS(string(crd.Spec.InstanceOS.OSName), crd.Spec.InstanceOS.OSVersion)
				targetVersions, getErr := ccetypes.GetSupportUpgradeXPUToolkitVersion(os)
				if getErr != nil {
					logger.Infof(ctx, "failed to get XPU upgrade versions for os %s, err: %v", os, getErr)
					// 继续处理，不中断整个流程
				} else {
					for _, tVersion := range targetVersions {
						// 只展示比最低版本高的xpu toolkit版本
						curVersion, err := version.NewVersion(result.XPUContainerToolkit.CurrentVersion)
						if err != nil {
							logger.Infof(ctx, "failed to parse XPU current version %s, err: %v", result.XPUContainerToolkit.CurrentVersion, err)
						}
						targetVersion, err := version.NewVersion(tVersion)
						if err != nil {
							logger.Infof(ctx, "failed to parse XPU target version %s, err: %v", tVersion, err)
						}
						if targetVersion.LessThanOrEqual(curVersion) {
							continue
						}
						if _, exit := targetXPUToolkitVersionMap[tVersion]; !exit {
							componentVersion := ComponentVersion{
								TargetVersion: tVersion,
								NeedDrainNode: true,
							}
							result.XPUContainerToolkit.ComponentVersions = append(result.XPUContainerToolkit.ComponentVersions, componentVersion)
							targetXPUToolkitVersionMap[tVersion] = struct{}{}
						}
					}
				}
			}
		}
	}
	// 当可升级runtime版本同时存在docker和containerd版本时，只显示containerd版本
	var containerdVersions []ComponentVersion
	for _, com := range result.ContainerRuntime.ComponentVersions {
		temp := com.TargetVersion
		ver := ccetypes.RuntimeVersion(temp)
		if ver.GetRuntimeType() == ccetypes.RuntimeTypeContainerd {
			containerdVersions = append(containerdVersions, com)
		}
	}

	if len(containerdVersions) > 0 && len(result.ContainerRuntime.ComponentVersions) > len(containerdVersions) {
		result.ContainerRuntime.ComponentVersions = containerdVersions
	}

	// 如果用户自己升了toolkit版本，且版本高于cce支持的升级的最高版本，这里targetVersion为空
	if len(result.NvidiaContainerToolkit.ComponentVersions) > 0 {
		sort.Slice(result.NvidiaContainerToolkit.ComponentVersions, func(i, j int) bool {
			v1, err := version.NewVersion(result.NvidiaContainerToolkit.ComponentVersions[i].TargetVersion)
			if err != nil {
				return false
			}
			v2, err := version.NewVersion(result.NvidiaContainerToolkit.ComponentVersions[j].TargetVersion)
			if err != nil {
				return false
			}
			return v1.GreaterThan(v2)
		})
	}

	// XPU toolkit版本排序
	if len(result.XPUContainerToolkit.ComponentVersions) > 0 {
		sort.Slice(result.XPUContainerToolkit.ComponentVersions, func(i, j int) bool {
			v1, err := version.NewVersion(result.XPUContainerToolkit.ComponentVersions[i].TargetVersion)
			if err != nil {
				return false
			}
			v2, err := version.NewVersion(result.XPUContainerToolkit.ComponentVersions[j].TargetVersion)
			if err != nil {
				return false
			}
			return v1.GreaterThan(v2)
		})
	}

	// 低于1.20的节点, 最高支持到containerd 1.5
	if kubeletVersion := result.Kubelet.CurrentVersion; kubeletVersion != "" {
		ok, err := ccetypes.K8SVersion(kubeletVersion).IsBefore(ccetypes.K8S_1_20_8)
		if err != nil {
			return nil, fmt.Errorf("failed to check k8s version %s, err: %v", kubeletVersion, err)
		}
		if ok {
			if result.ContainerRuntime.CurrentVersion != "" {
				runtimeVersion := ccetypes.RuntimeVersion(result.ContainerRuntime.CurrentVersion)
				if runtimeVersion.GetRuntimeType() == ccetypes.RuntimeTypeDocker {
					result.ContainerRuntime.ComponentVersions = []ComponentVersion{
						{
							TargetVersion: string(ccetypes.Containerd_1_5_4),
							NeedDrainNode: true,
						},
					}
				} else {
					result.ContainerRuntime.ComponentVersions = nil
				}
			}
		}
	}
	// 节点组没有节点场景下，kubelet不需要升级，直接返回集群版本即可。
	if len(InstancesCRD.Items) == 0 {
		result.Kubelet.CurrentVersion = result.Kubelet.ComponentVersions[0].TargetVersion
		// 容器运行时和toolkit直接使用集群模板，目前节点组上容器运行时和toolkit都是统一的跟机型无关
		igCrd, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		var tempInstanceTemplate ccetypes.InstanceTemplate
		tempInstanceTemplate = igCrd.Spec.InstanceTemplate
		if len(igCrd.Spec.InstanceTemplates) > 0 {
			tempInstanceTemplate = igCrd.Spec.InstanceTemplates[0]
		}
		result.ContainerRuntime.CurrentVersion = fmt.Sprintf("%s://%s", tempInstanceTemplate.RuntimeType, tempInstanceTemplate.RuntimeVersion)
		currentRuntimeVersion := ccetypes.RuntimeVersion(result.ContainerRuntime.CurrentVersion)
		for k, val := range currentRuntimeVersion.CanBeUpgradedToFE() {
			if _, exit := targetRuntimeVersionMap[k]; !exit {
				componentVersion := ComponentVersion{
					TargetVersion: string(k),
					NeedDrainNode: bool(val),
				}
				result.ContainerRuntime.ComponentVersions = append(result.ContainerRuntime.ComponentVersions, componentVersion)
				targetRuntimeVersionMap[k] = struct{}{}
			}
		}
		// toolkit版本信息也是最新的
		if tempInstanceTemplate.InstanceType == bcc.InstanceTypeG1 || tempInstanceTemplate.InstanceType == bcc.InstanceTypeBBCGPU ||
			tempInstanceTemplate.InstanceType == bcc.InstanceTypeHPAS || tempInstanceTemplate.NeedGPU {
			result.NvidiaContainerToolkit.CurrentVersion = tempInstanceTemplate.NvidiaContainerToolkitVersion
			if result.NvidiaContainerToolkit.CurrentVersion == "" {
				temCrd := &ccev1.Instance{
					Spec: tempInstanceTemplate.InstanceSpec,
				}
				result.NvidiaContainerToolkit.CurrentVersion = utils.GetNvidiaToolkitVersion(temCrd, cluster.Spec.K8SVersion)
			}
			// 获取当前所有节点可以升级的toolkit版本的并集
			os := ccetypes.GetToolkitVersionMapKeyOS(string(tempInstanceTemplate.InstanceOS.OSName), tempInstanceTemplate.InstanceOS.OSVersion)
			key := fmt.Sprintf("%s-%s-%s-%s", cluster.Spec.K8SVersion, os, tempInstanceTemplate.RuntimeType, tempInstanceTemplate.RuntimeVersion)
			targetVersions, getErr := ccetypes.GetSupportUpgradeToolkitVersion(key)
			if getErr != nil {
				return nil, getErr
			}

			for _, tVersion := range targetVersions {
				// 只展示比最低版本高的toolkit版本
				curVersion, err := version.NewVersion(result.NvidiaContainerToolkit.CurrentVersion)
				if err != nil {
					logger.Infof(ctx, "failed to parse version2 %s, err: %v", result.NvidiaContainerToolkit.CurrentVersion, err)
				}
				targetVersion, err := version.NewVersion(tVersion)
				if err != nil {
					logger.Infof(ctx, "failed to parse version2 %s, err: %v", result.NvidiaContainerToolkit.CurrentVersion, err)
				}
				if targetVersion.LessThanOrEqual(curVersion) {
					continue
				}
				if _, exit := targetToolkitVersionMap[tVersion]; !exit {
					componentVersion := ComponentVersion{
						TargetVersion: tVersion,
						NeedDrainNode: true,
					}
					result.NvidiaContainerToolkit.ComponentVersions = append(result.NvidiaContainerToolkit.ComponentVersions, componentVersion)
					targetToolkitVersionMap[tVersion] = struct{}{}
				}
			}
		}

		// xpu版本返回
		if tempInstanceTemplate.InstanceType == bcc.InstanceTypeKunlun || s.isKunlunByEBC(tempInstanceTemplate) {
			result.XPUContainerToolkit.CurrentVersion = tempInstanceTemplate.XPUContainerToolkitVersion
			if result.XPUContainerToolkit.CurrentVersion == "" {
				result.XPUContainerToolkit.CurrentVersion = ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
					cluster.Spec.K8SVersion,
					tempInstanceTemplate.RuntimeType,
					tempInstanceTemplate.RuntimeVersion,
					string(tempInstanceTemplate.InstanceOS.OSName),
					tempInstanceTemplate.InstanceOS.OSVersion)
			}
			componentVersion := ComponentVersion{
				TargetVersion: result.XPUContainerToolkit.CurrentVersion,
				NeedDrainNode: true,
			}
			result.XPUContainerToolkit.ComponentVersions = append(result.XPUContainerToolkit.ComponentVersions, componentVersion)
		}
	}

	sort.Slice(result.ContainerRuntime.ComponentVersions, func(i, j int) bool {
		v1 := ccetypes.RuntimeVersion(result.ContainerRuntime.ComponentVersions[i].TargetVersion)
		v2 := ccetypes.RuntimeVersion(result.ContainerRuntime.ComponentVersions[j].TargetVersion)
		ib, err := v1.IsBefore(v2)
		if err != nil {
			return false
		}
		// 这里逆序排列，新版本在前
		return !ib
	})

	log.Infof(ctx, "GetUpgradeComponentVersions success")
	return result, nil
}

// isKunlunByEBC ebc类型的判断昆仑芯
// 先看instanceResource.gpuType 值是否是kunlun开头，是的话，就是昆仑芯。临时方案。
// TODO 如果上面字段为空，则调用iaas新接口（未交付），判断这个spec是不是昆仑芯。
func (s *InstanceGroupService) isKunlunByEBC(instanceTemplate ccetypes.InstanceTemplate) bool {
	if instanceTemplate.InstanceType != bcc.InstanceTypeEBC {
		return false
	}
	// TODO 这里匹配kunlun开头字段为临时方案，后续待iaas提供接口，需要下掉该逻辑
	if instanceTemplate.InstanceResource.GPUType != "" &&
		strings.HasPrefix(strings.ToLower(string(instanceTemplate.InstanceResource.GPUType)), "kunlun") {
		return true
	}
	return false
}

// Get - 获取节点组详情
//
// PARAMS:
//   - ctx: context.Context
//   - accountID: accountID
//   - instanceGroupID: instanceGroupID
//
// RETURNS:
//
//	*models.InstanceGroup: nil if fail
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) Get(ctx context.Context, accountID, instanceGroupID string) (*models.InstanceGroup, error) {
	log := s.log.WithValues("method", "instanceGroupService.Get").
		WithValues("accountID", accountID).
		WithValues("instanceGroupID", instanceGroupID)

	igModel, err := s.model.GetInstanceGroupByCCEID(ctx, accountID, instanceGroupID)
	if err != nil {
		log.Errorf(ctx, "failed to get InstanceGroup from db, err: %v", err)
		return nil, err
	}

	ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, igModel.Spec.CCEInstanceGroupID, &metav1.GetOptions{})
	if err != nil {
		if !k8serr.IsNotFound(err) {
			log.WithValues("instanceGroupID", igModel.Spec.CCEInstanceGroupID).Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
			return nil, err
		}
		// 由于从meta集群删除节点组和从数据库删除节点组不是原子操作，会出现短暂的不同步的情况，所以需要特殊处理一下
		igModel.Deleted = true
		return igModel, nil
	}

	userScriptService := userscript.NewUserScriptService(s.model)

	if ig.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript != "" &&
		strings.HasPrefix(ig.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript, "cce-us-") {

		preUserScript, err := userScriptService.Get(ctx, ig.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript)
		if err != nil {
			logger.Errorf(ctx, "get pre user script failed: %v", err)
			return nil, err
		}
		ig.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript = preUserScript.ScriptContent
	}

	if ig.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript != "" &&
		strings.HasPrefix(ig.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript, "cce-us-") {

		postUserScript, err := userScriptService.Get(ctx, ig.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript)
		if err != nil {
			logger.Errorf(ctx, "get post user script failed: %v", err)
			return nil, err
		}
		ig.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript = postUserScript.ScriptContent
	}

	if ig.DeletionTimestamp != nil {
		igModel.DeletedAt = ig.DeletionTimestamp.Time
	}

	// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
	for i, instanceTemplate := range ig.Spec.InstanceTemplates {
		if instanceTemplate.DeployCustomConfig.PreUserScript != "" &&
			strings.HasPrefix(instanceTemplate.DeployCustomConfig.PreUserScript, "cce-us-") {

			preUserScript, err := userScriptService.Get(ctx, instanceTemplate.DeployCustomConfig.PreUserScript)
			if err != nil {
				logger.Errorf(ctx, "get pre user script failed: %v", err)
				return nil, err
			}
			ig.Spec.InstanceTemplates[i].DeployCustomConfig.PreUserScript = preUserScript.ScriptContent
		}

		if instanceTemplate.DeployCustomConfig.PostUserScript != "" &&
			strings.HasPrefix(instanceTemplate.DeployCustomConfig.PostUserScript, "cce-us-") {

			postUserScript, err := userScriptService.Get(ctx, instanceTemplate.DeployCustomConfig.PostUserScript)
			if err != nil {
				logger.Errorf(ctx, "get post user script failed: %v", err)
				return nil, err
			}
			ig.Spec.InstanceTemplates[i].DeployCustomConfig.PostUserScript = postUserScript.ScriptContent
		}
	}

	igModel.Spec.InstanceTemplate = ig.Spec.InstanceTemplate
	igModel.Spec.InstanceTemplates = ig.Spec.InstanceTemplates
	if err := s.mergeSecurityGroups(ctx, igModel, ig); err != nil {
		logger.Errorf(ctx, "parse securityGroups of instanceGroup err: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "get remedyRules binding information")
	var boundRulesID string
	if ig.Annotations == nil {
		ig.Annotations = make(map[string]string)
	}
	if v, ok := ig.Annotations[remedyrules.CCERemedyRuleIDOfInstanceGroup]; ok {
		boundRulesID = v
	}
	if ig.Spec.RemedyRulesBinding != nil {
		igModel.Spec.RemedyRulesBinding = &ccetypes.RemedyRulesBinding{
			EnableCheckANDRemedy: ig.Spec.RemedyRulesBinding.EnableCheckANDRemedy,
			RemedyRuleID:         boundRulesID,
		}
	}

	igModel.Spec.IAMRole = ig.Spec.IAMRole
	log.WithValues("InstanceGroup", utils.ToJSON(igModel)).Infof(ctx, "got InstanceGroup")
	return igModel, nil
}

func (s *InstanceGroupService) mergeSecurityGroups(ctx context.Context, igModel *models.InstanceGroup, igCR *ccev1.InstanceGroup) error {
	igModel.Spec.DefaultSecurityGroups = igCR.Spec.DefaultSecurityGroups
	igModel.Spec.SecurityGroupType = igCR.Spec.SecurityGroupType

	clusterID := igCR.Spec.ClusterID
	clusterCR, err := s.K8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "failed to get cluster, err: %v", err)
		return err
	}

	sdkClients := s.filler.SDKClients
	sgprovider := securitygroup.NewProvider(ctx, sdkClients.STSClient, sdkClients.BCCClient, sdkClients.BBCClient, sdkClients.HPASCClient)
	sgSet, esgSet, err := s.getSecurityGroupsInClusterVPC(ctx, sgprovider, clusterCR.Spec.VPCID, clusterCR.Spec.AccountID)
	if err != nil {
		logger.Errorf(ctx, "getSecurityGroupsInClusterVPC failed: %v", err)
		return err
	}

	if len(igModel.Spec.DefaultSecurityGroups) == 0 {
		sgs, err := s.getSecurityGroupsOfInstanceGroups(ctx, igCR, sgSet, esgSet)
		if err != nil {
			logger.Errorf(ctx, "getSecurityGroupsOfInstanceGroups failed: %v", err)
			return err
		}
		igModel.Spec.DefaultSecurityGroups = sgs
	}

	return nil
}

func (s *InstanceGroupService) getSecurityGroupsInClusterVPC(ctx context.Context, sgProvider *securitygroup.SecurityGroupProvider, vpcID string, accountID string) (sgSet map[string]*bccapi.SecurityGroupModel, esgSet map[string]*esg.EnterpriseSecurityGroup, err error) {
	g, ctx := errgroup.WithContext(ctx)

	var sgList *bccapi.ListSecurityGroupResult
	var esgList *esg.ListEsgResult
	g.Go(func() error {
		sg, err := sgProvider.GetNormalSecurityGroups(ctx, vpcID, "", accountID)
		if err != nil {
			logger.Errorf(ctx, "GetNormalSecurityGroups failed: %v", err)
			return err
		}
		sgList = sg
		return nil
	})

	g.Go(func() error {
		sg, err := sgProvider.GetEnterpriseSecurityGroups(ctx, vpcID, "", accountID)
		if err != nil {
			logger.Errorf(ctx, "GetEnterpriseSecurityGroups failed: %v", err)
			return err
		}
		esgList = sg
		return nil
	})

	if err := g.Wait(); err != nil {
		logger.Errorf(ctx, "getSecurityGroupsInClusterVPC failed: %v", err)
		return nil, nil, err
	}

	sgSet = make(map[string]*bccapi.SecurityGroupModel)
	esgSet = make(map[string]*esg.EnterpriseSecurityGroup)

	if sgList != nil {
		for i, sg := range sgList.SecurityGroups {
			sgSet[sg.Id] = &sgList.SecurityGroups[i]
		}
	}

	if esgList != nil {
		for i, sg := range esgList.EnterpriseSecurityGroups {
			esgSet[sg.Id] = &esgList.EnterpriseSecurityGroups[i]
		}
	}

	return sgSet, esgSet, nil
}

func (s *InstanceGroupService) getSecurityGroupsOfInstanceGroups(ctx context.Context, igCR *ccev1.InstanceGroup, sgSet map[string]*bccapi.SecurityGroupModel, esgSet map[string]*esg.EnterpriseSecurityGroup) ([]ccetypes.SecurityGroupV2, error) {
	instanceTpl := igCR.Spec.InstanceTemplate
	if len(igCR.Spec.InstanceTemplates) != 0 {
		instanceTpl = igCR.Spec.InstanceTemplates[0]
	}

	var (
		requiredSgName string
		optionalSgName string
	)
	switch instanceTpl.ClusterRole {
	case ccetypes.ClusterRoleMaster:
		requiredSgName = ccetypes.CCEMasterRequiredSGName
		optionalSgName = ccetypes.CCEMasterOptionalSGName
	case ccetypes.ClusterRoleNode:
		requiredSgName = ccetypes.CCENodeRequiredSGName
		optionalSgName = ccetypes.CCEMasterOptionalSGName
	}

	var sgs []ccetypes.SecurityGroupV2
	for _, sg := range sgSet {
		if sg == nil {
			continue
		}
		if (instanceTpl.SecurityGroup.EnableCCERequiredSecurityGroup && sg.Name == requiredSgName) ||
			(instanceTpl.SecurityGroup.EnableCCEOptionalSecurityGroup && sg.Name == optionalSgName) {
			sgs = append(sgs, ccetypes.SecurityGroupV2{
				Name: sg.Name,
				Type: ccetypes.SecurityGroupTypeNormal,
				ID:   sg.Id,
			})
		}
	}

	for _, sgID := range instanceTpl.SecurityGroup.CustomSecurityGroupIDs {
		if sg, exist := sgSet[sgID]; exist {
			sgs = append(sgs, ccetypes.SecurityGroupV2{
				ID:   sg.Id,
				Name: sg.Name,
				Type: ccetypes.SecurityGroupTypeNormal,
			})
		}
		if sg, exist := esgSet[sgID]; exist {
			sgs = append(sgs, ccetypes.SecurityGroupV2{
				ID:   sg.Id,
				Name: sg.Name,
				Type: ccetypes.SecurityGroupTypeEnterprise,
			})
		}
	}

	return sgs, nil
}

// ListClusterInstanceGroups 获取ig cr列表，查询cluster下所有ig，然后筛选需要的ig返回
func (s *InstanceGroupService) ListClusterInstanceGroups(ctx context.Context,
	clusterID string, instanceGroupIDs []string) (map[string]ccev1.InstanceGroup, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}
	if len(instanceGroupIDs) == 0 {
		return nil, fmt.Errorf("instanceGroupIDs is empty")
	}
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			ccetypes.ClusterIDLabelKey: clusterID,
		},
	}
	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)
	instanceGroupList, err := s.K8SClient.ListInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
		LabelSelector: labelSelectorStr,
	})
	if err != nil {
		logger.Errorf(ctx, "ListInstanceGroups failed: %v", err)
		return nil, err
	}

	toMap := func(s []ccev1.InstanceGroup) map[string]ccev1.InstanceGroup {
		m := make(map[string]ccev1.InstanceGroup)
		for _, ig := range s {
			m[ig.Spec.CCEInstanceGroupID] = ig
		}
		return m
	}
	instanceGroups := toMap(instanceGroupList.Items)
	result := make(map[string]ccev1.InstanceGroup, 0)

	for _, instanceGroupID := range instanceGroupIDs {
		ig, ok := instanceGroups[instanceGroupID]
		if !ok {
			logger.Errorf(ctx, "ListInstanceGroups instance group %s not found", instanceGroupID)
			return nil, fmt.Errorf("instance group %s not found", instanceGroupID)
		}
		if ig.Spec.ClusterID != clusterID {
			logger.Errorf(ctx, "ListInstanceGroups instance group %s is not owned by cluster %s", instanceGroupID, clusterID)
			return nil, fmt.Errorf("instance group %s is not owned by cluster %s", instanceGroupID, clusterID)
		}
		result[instanceGroupID] = ig
	}
	return result, nil
}

// CheckCASpecOkAfterDeleteInstances 检查删除节点组节点后，是否满足节点组自动伸缩范围
// instanceGroupDecrement: instanceGroupID => 要删除的节点数量
// 返回值：
//   - []string: 校验不通过的节点组id列表
//   - error: 表示异常问题
func (s *InstanceGroupService) CheckCASpecOkAfterDeleteInstances(ctx context.Context,
	clusterID string, instanceGroupDecrement map[string]int) ([]string, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}
	if len(instanceGroupDecrement) == 0 {
		return nil, nil
	}

	getMapKeys := func(m map[string]int) []string {
		keys := make([]string, 0)
		for k := range m {
			keys = append(keys, k)
		}
		return keys
	}
	instanceGroups, err := s.ListClusterInstanceGroups(ctx, clusterID, getMapKeys(instanceGroupDecrement))
	if err != nil {
		logger.Errorf(ctx, "ListClusterInstanceGroups failed: %v", err)
		return nil, err
	}

	notOkInstanceGroupIDs := make([]string, 0) // 校验不通过的节点组id列表
	for instanceGroupID, decrement := range instanceGroupDecrement {
		ig, ok := instanceGroups[instanceGroupID]
		if !ok {
			logger.Errorf(ctx, "ListClusterInstanceGroups instance group %s not found", instanceGroupID)
			return nil, fmt.Errorf("instance group %s not found", instanceGroupID)
		}
		// 没有开启自动伸缩，跳过
		if ig.Spec.ClusterAutoscalerSpec == nil || !ig.Spec.ClusterAutoscalerSpec.Enabled {
			continue
		}
		// 移除节点后节点数量小于最小伸缩范围
		if ig.Spec.Replicas-decrement < ig.Spec.ClusterAutoscalerSpec.MinReplicas {
			notOkInstanceGroupIDs = append(notOkInstanceGroupIDs, ig.Spec.CCEInstanceGroupID)
		}
	}
	return notOkInstanceGroupIDs, nil
}

// List - 获取节点组列表
//
// PARAMS:
//   - ctx: context.Context
//   - opts: list options
//
// RETURNS:
//
//	*models.InstanceGroupList: nil if fail
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) List(ctx context.Context, opts ListOptions) (*models.InstanceGroupList, error) {
	log := s.log.WithValues("method", "instanceGroupService.List").
		WithValues("List options", utils.ToJSON(opts))

	// TODO: 后面需要根据不同的ListOptions来调用不同的model层接口，要看具体产品设计
	opt := models.InstanceGroupListOption{
		AccountID:         opts.AccountID,
		ClusterID:         opts.ClusterID,
		Role:              opts.Role,
		CAEnabled:         opts.CAEnabled,
		PageNo:            opts.PageNo,
		PageSize:          opts.PageSize,
		TotalCountOnly:    false,
		InstanceGroupName: opts.InstanceGroupName,
		InstanceGroupID:   opts.InstanceGroupID,
		OrderBy:           opts.OrderBy,
		Order:             opts.Order,
		ChargingType:      opts.ChargingType, // 新增：传递计费方式参数
	}
	list, err := s.model.GetInstanceGroupsEx(ctx, opt)
	if err != nil {
		log.Errorf(ctx, "failed to list InstanceGroup from db, err: %v", err)
		return nil, err
	}

	userScriptService := userscript.NewUserScriptService(s.model)

	for _, item := range list.Items {
		ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, item.Spec.CCEInstanceGroupID, &metav1.GetOptions{})
		if err != nil {
			if !k8serr.IsNotFound(err) {
				log.WithValues("instanceGroupID", item.Spec.CCEInstanceGroupID).Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
				return nil, err
			}
			item.Deleted = true
			continue
		}

		if ig.DeletionTimestamp != nil {
			item.DeletedAt = ig.DeletionTimestamp.Time
		}

		// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
		if ig.Spec.InstanceTemplates != nil {
			for i, instanceTemplate := range ig.Spec.InstanceTemplates {
				if instanceTemplate.DeployCustomConfig.PreUserScript != "" &&
					strings.HasPrefix(instanceTemplate.DeployCustomConfig.PreUserScript, "cce-us-") {

					preUserScript, err := userScriptService.Get(ctx, instanceTemplate.DeployCustomConfig.PreUserScript)
					if err != nil {
						logger.Errorf(ctx, "get pre user script failed: %v", err)
						return nil, err
					}
					ig.Spec.InstanceTemplates[i].DeployCustomConfig.PreUserScript = preUserScript.ScriptContent
				}

				if instanceTemplate.DeployCustomConfig.PostUserScript != "" &&
					strings.HasPrefix(instanceTemplate.DeployCustomConfig.PostUserScript, "cce-us-") {

					postUserScript, err := userScriptService.Get(ctx, instanceTemplate.DeployCustomConfig.PostUserScript)
					if err != nil {
						logger.Errorf(ctx, "get post user script failed: %v", err)
						return nil, err
					}
					ig.Spec.InstanceTemplates[i].DeployCustomConfig.PostUserScript = postUserScript.ScriptContent
				}
			}

			item.Spec.InstanceTemplates = ig.Spec.InstanceTemplates
		}

		if item.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript != "" &&
			strings.HasPrefix(item.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript, "cce-us-") {

			preUserScript, err := userScriptService.Get(ctx, item.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript)
			if err != nil {
				logger.Errorf(ctx, "get pre user script failed: %v", err)
				return nil, err
			}
			item.Spec.InstanceTemplate.DeployCustomConfig.PreUserScript = preUserScript.ScriptContent
		}

		if item.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript != "" &&
			strings.HasPrefix(item.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript, "cce-us-") {

			postUserScript, err := userScriptService.Get(ctx, item.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript)
			if err != nil {
				logger.Errorf(ctx, "get post user script failed: %v", err)
				return nil, err
			}
			item.Spec.InstanceTemplate.DeployCustomConfig.PostUserScript = postUserScript.ScriptContent
		}

		if ig.Spec.RemedyRulesBinding != nil {
			if item.Spec.RemedyRulesBinding == nil {
				item.Spec.RemedyRulesBinding = &ccetypes.RemedyRulesBinding{
					EnableCheckANDRemedy: ig.Spec.RemedyRulesBinding.EnableCheckANDRemedy,
					RemedyRuleID:         ig.Spec.RemedyRulesBinding.RemedyRuleID,
				}
			}
			item.Spec.RemedyRulesBinding = ig.Spec.RemedyRulesBinding
		}

		item.Spec.DefaultSecurityGroups = ig.Spec.DefaultSecurityGroups
	}

	log.WithValues("InstanceGroups", utils.ToJSON(list)).Infof(ctx, "list InstanceGroup succeed")

	return list, nil
}

func (s *InstanceGroupService) UpdateCRD(ctx context.Context, instanceGroup *ccev1.InstanceGroup) error {
	log := s.log.WithValues("method", "instanceGroupService.UpdateCRD").
		WithValues("instanceGroupID", instanceGroup.Spec.CCEInstanceGroupID)

	log.Infof(ctx, "going to update instanceGroup: %s", utils.ToJSON(instanceGroup))
	if err := s.model.UpdateInstanceGroupDBAndCRD(ctx, instanceGroup.Spec.AccountID, instanceGroup.Spec.CCEInstanceGroupID, instanceGroup, s.K8SClient.UpdateInstanceGroup); err != nil {
		log.Errorf(ctx, "failed to update instanceGroup, err: %v", err)
		return err
	}
	return nil
}

func (s *InstanceGroupService) GetCRD(ctx context.Context, accountID, instanceGroupID string) (*ccev1.InstanceGroup, error) {
	log := s.log.WithValues("method", "instanceGroupService.GetCRD").
		WithValues("accountID", accountID).
		WithValues("instanceGroupID", instanceGroupID)

	instanceGroup, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
		return nil, err
	}

	if instanceGroup.Spec.AccountID != accountID {
		log.Errorf(ctx, "instanceGroup belong to account: %s, not %s", instanceGroup.Spec.AccountID, accountID)
		return nil, fmt.Errorf("not found")
	}
	return instanceGroup, nil
}

func (s *InstanceGroupService) ScaleUp(ctx context.Context, accountID, instanceGroupID string, replicas int, upReplicas int) (string, error) {
	log := s.log.WithValues("accountID", accountID).
		WithValues("instanceGroupID", instanceGroupID)

	ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
	if err != nil {
		log.WithValues("instanceGroupID", ig.Spec.CCEInstanceGroupID).Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
		return "", nil
	}

	if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeHPAS {
		return "", models.ErrHPASNotImplemented.New(ctx, "HPAS scaleup not implemented")
	}

	if ig.Spec.AccountID != accountID {
		log.Errorf(ctx, "instancegroup belong to account: %s, not %s", ig.Spec.AccountID, accountID)
		return "", fmt.Errorf("permision deny")
	}

	quota, err := s.quotaClient.GetNodeQuota(ctx, ig.Spec.ClusterID)
	if err != nil {
		log.Errorf(ctx, "failed to get cluster node quota info, err: %v", err)
		return "", err
	}

	isUpN, err := isReplicaUpN(replicas, upReplicas)
	if err != nil {
		return "", err
	}
	if !isUpN {
		if ig.Spec.Replicas == replicas {
			log.Errorf(ctx, "instancegroup target replicas is already equal to scaling up target replicas, now: %d, scale up to: %d", ig.Spec.Replicas, replicas)
			return "", fmt.Errorf("instancegroup target replicas is already equal to scaling up target replicas")
		}
		if ig.Spec.Replicas > replicas {
			log.Errorf(ctx, "instancegroup target replicas is already greater than scaling up target replicas, now: %d, scale up to: %d", ig.Spec.Replicas, replicas)
			return "", fmt.Errorf("instancegroup target replicas is already greater than scaling up target replicas")
		}
		if replicas-ig.Spec.Replicas > quota.Quota-quota.Used {
			log.WithValues("quota", quota.Quota, "used", quota.Used, "nodeToBeAdded", replicas-ig.Spec.Replicas).Errorf(ctx, "exceed cluster node quota")
			return "", fmt.Errorf("exceed cluster node quota")
		}

		// 增加对托管集群规格的限制
		// replicas为ScaleUp 这个方法要修改节点组的期望节点数值
		err = s.checkCCENodeNumLimitAndReplicas(ctx, ig.Spec.ClusterID, ig.Spec.AccountID, replicas-ig.Spec.Replicas)
		if err != nil {
			return "", err
		}
	} else {
		if upReplicas > quota.Quota-quota.Used {
			log.WithValues("quota", quota.Quota, "used", quota.Used, "nodeToBeAdded", replicas-ig.Spec.Replicas).Errorf(ctx, "exceed cluster node quota")
			return "", fmt.Errorf("exceed cluster node quota")
		}
		// 增加对托管集群规格的限制
		// upReplicas为ScaleUp 这个方法要对节点组新增的节点数量
		err = s.checkCCENodeNumLimitAndReplicas(ctx, ig.Spec.ClusterID, ig.Spec.AccountID, upReplicas)
		if err != nil {
			return "", err
		}
	}
	// 检查节点组是否存在冲突任务，不能更新
	err = s.exitConflictWorkflowByInstanceGroup(ctx, ig.Spec.CCEInstanceGroupID)
	if err != nil {
		log.Errorf(ctx, "exitConflictWorkflowByInstanceGroup, err: %v", err)
		return "", err
	}

	randName := s.rand(8)
	var backoffLimit int32 = 20 // 20次尝试

	task := &ccev1.Task{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: consts.MetaClusterDefaultNamespace,
			Name:      "task-" + ig.Spec.CCEInstanceGroupID + "-scaleup-" + randName,
			Labels: map[string]string{
				ccetypes.ClusterIDLabelKey:                               ig.Spec.ClusterID,
				ccetypes.InstanceGroupIDLabelKey:                         ig.Spec.CCEInstanceGroupID,
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Finalizers: []string{ccev1.DBSyncFinalizer},
		},
		Spec: ccetypes.TaskSpec{
			Handler:      s.handler,
			TaskType:     ccetypes.TaskTypeInstanceGroupReplicas,
			UserID:       ig.Spec.UserID,
			AccountID:    ig.Spec.AccountID,
			CreatedTime:  utils.Now().Format(time.RFC3339Nano),
			BackoffLimit: &backoffLimit,
			TargetRef: &ccetypes.TaskTargetReference{
				Name:      ig.Name,
				Namespace: ig.Namespace,
			},
			AntiAffinity: ccetypes.TaskAntiAffinity{
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Operation: runtime.RawExtension{
				Object: &ccev1.InstanceGroupReplicasOperation{
					TypeMeta: metav1.TypeMeta{},
					Spec: ccetypes.InstanceGroupReplicasOperationSpec{
						OperationType:         ccetypes.InstanceGroupOperationTypeScalingUp,
						UpToReplicas:          replicas,
						UpReplicas:            upReplicas,
						MachineNameRandString: randName,
						// todo 这里出错了
						Delta: replicas - ig.Spec.Replicas,
					},
				},
			},
		},
	}

	log.Infof(ctx, "going to create task: %s", utils.ToJSON(task))
	task, err = s.K8SClient.CreateTask(ctx, task)
	if err != nil {
		log.Errorf(ctx, "failed to create scaling up task, err: %v", err)
		return "", err
	}

	return task.Name, nil
}

func (s *InstanceGroupService) ScaleUpExistNode(ctx context.Context, service services.Interface, accountID, clusterID, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption, instanceCount int) (string, error) {
	log := s.log.WithValues("accountID", accountID).
		WithValues("instanceGroupID", instanceGroupID)

	// 获取 Cluster
	cluster, err := s.K8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil || cluster == nil {
		log.WithValues("Cluster", clusterID).Errorf(ctx, "GetCluster failed , err: %v", err)
		return "", nil
	}

	// 获取 InstanceGroup
	ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
	if err != nil {
		log.WithValues("instanceGroupID", ig.Spec.CCEInstanceGroupID).Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
		return "", nil
	}

	if err := s.checkInstanceSecurityGroupCount(ctx, param.ExistedInstances, cluster, ig); err != nil {
		log.WithValues("instanceGroupID", ig.Spec.CCEInstanceGroupID).Errorf(ctx, err.Error())
		return "", err
	}

	// 校验 InstanceGroup 账户
	if ig.Spec.AccountID != accountID {
		log.Errorf(ctx, "instancegroup belong to account: %s, not %s", ig.Spec.AccountID, accountID)
		return "", fmt.Errorf("permision deny")
	}
	// 校验集群配额
	quota, err := s.quotaClient.GetNodeQuota(ctx, ig.Spec.ClusterID)
	if err != nil {
		log.Errorf(ctx, "failed to get cluster node quota info, err: %v", err)
		return "", err
	}
	if instanceCount > quota.Quota-quota.Used {
		log.WithValues("quota", quota.Quota, "used", quota.Used, "nodeToBeAdded", instanceCount).Errorf(ctx, "exceed cluster node quota")
		return "", fmt.Errorf("exceed cluster node quota")
	}
	if ig.Spec.ClusterAutoscalerSpec != nil && ig.Spec.ClusterAutoscalerSpec.Enabled {
		log.Errorf(ctx, "failed to scaleUpExistNodeInCluster, err: ClusterAutoscaler is enabled")
		return "", fmt.Errorf("failed to scaleUpExistNodeInCluster, err: ClusterAutoscaler is enabled")
	}
	var joinInstanceInfo []*ccetypes.InstancesToJoin
	var failedBindRoleInstanceIDs []string
	var partialErr string
	if param.Incluster {
		// 节点在集群中, 校验节点参数
		newJoinInstanceInfo, err := s.scaleUpExistNodeInCluster(ctx, ig, param)
		if err != nil {
			log.Errorf(ctx, "failed to scaleUpExistNodeInCluster, err: %v", err)
			return "", err
		}
		//BindInstanceRole(ctx context.Context, roleName string, instanceIDs []string, option *bce.SignOption) (*bccapi.BindInstanceRoleResult, error)
		// 添加集群内已有节点时，在 controller 中绑定已有节点因当前架构问题，可能会导致用户在 iaas 侧修改
		// 只有 bbc 和 ebc 需要绑定 iam 角色
		if ig.Spec.IAMRole != nil && ig.Spec.IAMRole.RoleName != "" && (ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeBCC || ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeEBC) {
			if joinInstanceInfo, failedBindRoleInstanceIDs, err = s.syncInstanceRole(ctx, ig, newJoinInstanceInfo, log); err != nil {
				return "", err
			}
		} else {
			joinInstanceInfo = newJoinInstanceInfo
		}
	} else {

		//这里如果节点组有nvidiaContainerToolkit版本，节点未指定，则使用节点组版本
		for _, ins := range param.ExistedInstances {
			// GPU节点才处理
			if ins.InstanceSpec.NeedGPU || ins.InstanceSpec.InstanceType.IsGPUType() {
				if ins.InstanceSpec.NvidiaContainerToolkitVersion != "" {
					continue
				}
				for _, temp := range ig.Spec.InstanceTemplates {
					if temp.InstanceTemplateID == ins.InstanceSpec.InstanceTemplateID && temp.NvidiaContainerToolkitVersion != "" {
						ins.InstanceSpec.NvidiaContainerToolkitVersion = temp.NvidiaContainerToolkitVersion
						break
					}
				}
			}

			// XPU节点处理 - 针对昆仑芯节点（InstanceType=25）
			if ins.InstanceSpec.InstanceType == bcc.InstanceTypeKunlun {
				if ins.InstanceSpec.XPUContainerToolkitVersion == "" {
					// 如果节点未指定XPU版本，则使用节点组版本或默认版本
					for _, temp := range ig.Spec.InstanceTemplates {
						if temp.InstanceTemplateID == ins.InstanceSpec.InstanceTemplateID && temp.XPUContainerToolkitVersion != "" {
							ins.InstanceSpec.XPUContainerToolkitVersion = temp.XPUContainerToolkitVersion
							break
						}
					}
					// 如果节点组也没有指定，则使用默认版本
					if ins.InstanceSpec.XPUContainerToolkitVersion == "" {
						ins.InstanceSpec.XPUContainerToolkitVersion = ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
							cluster.Spec.K8SVersion,
							ins.InstanceSpec.RuntimeType,
							ins.InstanceSpec.RuntimeVersion,
							string(ins.InstanceSpec.InstanceOS.OSName),
							ins.InstanceSpec.InstanceOS.OSVersion)
					}
				}
			}

			ins.InstanceSpec.InstanceGroupID = instanceGroupID
			s.setUserData(param.InstallGpuDriver, ins, ig)
		}

		// 节点不在集群中，校验节点参数&创建instance
		joinInstanceInfo, err = s.scaleUpExistNodeNotInCluster(ctx, service, cluster, ig, param.UseInstanceGroupConfig,
			param.ExistedInstances, param.UseInstanceGroupConfigWithDiskInfo)
		if err != nil {
			log.Errorf(ctx, "failed to scaleUpExistNodeNotInCluster, err: %v", err)
			if !models.ErrPartialSuccess.Is(err) {
				return "", err
			} else {
				partialErr = err.Error()
			}
		}
	}
	if len(joinInstanceInfo) == 0 {
		return "", fmt.Errorf("not exist instance to attacch")
	}

	randName := s.rand(8)
	var backoffLimit int32 = 20 // 20次尝试

	task := &ccev1.Task{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: consts.MetaClusterDefaultNamespace,
			Name:      "task-" + ig.Spec.CCEInstanceGroupID + "-scaleupexist-" + randName,
			Labels: map[string]string{
				ccetypes.ClusterIDLabelKey:                               ig.Spec.ClusterID,
				ccetypes.InstanceGroupIDLabelKey:                         ig.Spec.CCEInstanceGroupID,
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Annotations: map[string]string{
				"partial-error": partialErr,
			},
			Finalizers: []string{ccev1.DBSyncFinalizer},
		},
		Spec: ccetypes.TaskSpec{
			Handler:      s.handler,
			TaskType:     ccetypes.TaskTypeInstanceGroupReplicas,
			UserID:       ig.Spec.UserID,
			AccountID:    ig.Spec.AccountID,
			CreatedTime:  utils.Now().Format(time.RFC3339Nano),
			BackoffLimit: &backoffLimit,
			TargetRef: &ccetypes.TaskTargetReference{
				Name:      ig.Name,
				Namespace: ig.Namespace,
			},
			AntiAffinity: ccetypes.TaskAntiAffinity{
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Operation: runtime.RawExtension{
				Object: &ccev1.InstanceGroupReplicasOperation{
					TypeMeta: metav1.TypeMeta{},
					Spec: ccetypes.InstanceGroupReplicasOperationSpec{
						OperationType:   ccetypes.InstanceGroupOperationTypeExistedScaleup,
						InstancesToJoin: joinInstanceInfo,
						Delta:           len(joinInstanceInfo),
					},
				},
			},
		},
	}

	log.Infof(ctx, "going to create task: %s", utils.ToJSON(task))
	task, err = s.K8SClient.CreateTask(ctx, task)
	if err != nil {
		log.Errorf(ctx, "failed to create scaling up task, err: %v", err)
		return "", err
	}
	if len(failedBindRoleInstanceIDs) > 0 {
		return "", fmt.Errorf("instances: %v, bind role: %s failed", failedBindRoleInstanceIDs, ig.Spec.IAMRole.RoleName)
	}

	return task.Name, nil
}

func (s *InstanceGroupService) setUserData(installGpuDriver bool, ins *ccesdk.InstanceSet, ig *ccev1.InstanceGroup) {
	// 如果用户选择安装 gpu 驱动，但是未传入 userdata，则使用节点组的 userdata，适配 openapi 的场景
	if installGpuDriver && ins.InstanceSpec.UserData == "" {
		if len(ig.Spec.InstanceTemplates) > 0 {
			for _, template := range ig.Spec.InstanceTemplates {
				if template.UserData != "" {
					ins.InstanceSpec.UserData = template.UserData
					break
				}
			}
		} else {
			if ig.Spec.InstanceTemplate.UserData != "" {
				ins.InstanceSpec.UserData = ig.Spec.InstanceTemplate.UserData
			}
		}
	}
}

// syncInstanceRole returns failed bind role instanceIDs and error
func (s *InstanceGroupService) syncInstanceRole(ctx context.Context, ig *ccev1.InstanceGroup, newJoinInstanceInfo []*ccetypes.InstancesToJoin,
	log logger.InterfaceEx) ([]*ccetypes.InstancesToJoin, []string, error) {
	var (
		failedInstance   []string
		joinInstanceInfo []*ccetypes.InstancesToJoin
		instanceIDs      []string
	)
	for _, i := range newJoinInstanceInfo {
		instanceIDs = append(instanceIDs, i.ExistedInstanceID)
	}
	if ig.Spec.IAMRole != nil && ig.Spec.IAMRole.RoleName != "" {
		if bindResult, err := s.clientSet.OpenBCCClient.BindInstanceRole(ctx, ig.Spec.IAMRole.RoleName, instanceIDs, s.clientSet.STSClient.NewSignOption(ctx, ig.Spec.AccountID)); err != nil {
			log.Errorf(ctx, "failed to bindInstanceRole, err: %v", err)
			return nil, nil, err
		} else if len(bindResult.FailInstances) > 0 {
			for _, failInstance := range bindResult.FailInstances {
				log.Errorf(ctx, "failed to bindInstanceRole: %s, err: %v", failInstance.InstanceId, failInstance.FailMessage)
				failedInstance = append(failedInstance, failInstance.InstanceId)
			}
			for _, join := range newJoinInstanceInfo {
				if !utils.ContainItem(failedInstance, join.ExistedInstanceID) {
					joinInstanceInfo = append(joinInstanceInfo, join)
				}
			}
		} else {
			joinInstanceInfo = newJoinInstanceInfo
		}
	} else {
		// 解绑操作，暂时不用
		//// bcc openapi
		//openBccInstances, err := s.clientSet.OpenBCCClient.ListInstancesByInstanceIDs(ctx, strings.Join(instanceIDs, ","), s.clientSet.STSClient.NewSignOption(ctx, ig.Spec.AccountID))
		//if err != nil {
		//	logger.Errorf(ctx, "BCCClient.ListInstances failed: %v", err)
		//	return nil, nil, err
		//}
		//
		//var roleAndInstances = make(map[string][]string, len(newJoinInstanceInfo))
		//for _, ins := range openBccInstances.Instances {
		//	if ins.RoleName != "" {
		//		if _, ok := roleAndInstances[ins.RoleName]; !ok {
		//			roleAndInstances[ins.RoleName] = make([]string, 0)
		//		}
		//		roleAndInstances[ins.RoleName] = append(roleAndInstances[ins.RoleName], ins.InstanceID)
		//	}
		//}
		//for roleName, insIDs := range roleAndInstances {
		//	if unBindResult, err := s.clientSet.OpenBCCClient.UnbindInstanceRole(ctx, roleName, insIDs, s.clientSet.STSClient.NewSignOption(ctx, ig.Spec.AccountID)); err != nil {
		//		return nil, nil, err
		//	} else if len(unBindResult.FailInstances) > 0 {
		//		for _, failInstance := range unBindResult.FailInstances {
		//			log.Errorf(ctx, "failed to bindInstanceRole: %s, err: %v", failInstance.InstanceId, failInstance.FailMessage)
		//			failedInstance = append(failedInstance, failInstance.InstanceId)
		//		}
		//		for _, join := range newJoinInstanceInfo {
		//			if !utils.ContainItem(failedInstance, unBindResult.FailInstances) {
		//				joinInstanceInfo = append(joinInstanceInfo, join)
		//			}
		//		}
		//	}
		//}
	}
	return joinInstanceInfo, failedInstance, nil
}

func (s *InstanceGroupService) checkInstanceSecurityGroupCount(ctx context.Context, instanceSets []*ccesdk.InstanceSet, cluster *ccev1.Cluster, ig *ccev1.InstanceGroup) error {
	sgProvider := securitygroup.NewProvider(ctx, s.filler.SDKClients.STSClient, s.filler.SDKClients.BCCClient, s.filler.SDKClients.BBCClient, s.filler.SDKClients.HPASCClient)

	for _, instanceSet := range instanceSets {
		ins := instanceSet.InstanceSpec
		if !ins.Existed || ins.ExistedOption.ExistedInstanceID == "" {
			continue
		}

		insSgCount := securitygroup.GetSgCountOfInstance(&ins)
		igSgCount := securitygroup.GetSgCountOfInstanceGroup(ig)

		nSgs, eSgs, err := sgProvider.GetSecurityGroups(ctx, cluster.Spec.VPCID, ins.ExistedOption.ExistedInstanceID, cluster.Spec.AccountID)
		if err != nil {
			logger.Errorf(ctx, "GetSecurityGroups failed: %v", err)
			return err
		}

		total := 0
		if igSgCount != 0 {
			total = igSgCount + len(nSgs.SecurityGroups) + len(eSgs.EnterpriseSecurityGroups)
		} else {
			total = insSgCount + len(nSgs.SecurityGroups) + len(eSgs.EnterpriseSecurityGroups)
		}

		if total > 10 {
			return securitygroup.NewSgCountLimitError()
		}
	}
	return nil
}

func (s *InstanceGroupService) scaleUpExistNodeInCluster(ctx context.Context, ig *ccev1.InstanceGroup, param ccesdk.ScaleUpExistInstanceGroupOption) ([]*ccetypes.InstancesToJoin, error) {
	ExistedInstancesInCluster := param.ExistedInstancesInCluster
	// 获取游离节点列表
	orphanInstanceList, err := s.K8SClient.ListInstances(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: fmt.Sprintf(orphanInstanceSelector, ig.Labels[ccetypes.ClusterIDLabelKey], ig.Labels[ccetypes.ClusterRoleLabelKey]),
	})
	if err != nil {
		return nil, err
	}
	orphanInstanceInfo := make([]*ccetypes.InstancesToJoin, 0)
	orphanInstances := make([]ccev1.Instance, 0)

	// 放开cpu，mem & gpuCount 的限制
	// 机器类型、cpu、内存等需一致
	//cpu, mem, gpuCount := ig.Spec.InstanceTemplate.CPU, ig.Spec.InstanceTemplate.MEM, ig.Spec.InstanceTemplate.GPUCount
	//if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeBBC {
	//	bbcCpu, bbcMem, err := s.getCPUAndMEM(ctx, &ig.Spec.InstanceTemplate.InstanceSpec)
	//	if err != nil {
	//		logger.Errorf(ctx, "failed to get bbc cpu and mem: %s", err)
	//		return nil, err
	//	}
	//	cpu, mem, gpuCount = bbcCpu, bbcMem, 0
	//}
	machineType := ig.Spec.InstanceTemplate.MachineType
	chargingType := ig.Spec.InstanceTemplate.InstanceChargingType
	// 新建 机器ID&instance map
	var InstanceIDMap = make(map[string]ccev1.Instance)
	for _, ins := range orphanInstanceList.Items {
		if ins.Status.Machine.InstanceID != "" {
			// 没有机器ID，可能是节点没创建成功
			//if ins.Spec.MachineType == ccetypes.MachineTypeBBC && ins.Spec.CPU == 0 && ins.Spec.MEM == 0 {
			//	if ins.Spec.BBCOption.Flavor == "" {
			//		logger.Infof(ctx, "skip bbc instance: %s", ins.Name)
			//		ins.Spec.CPU, ins.Spec.MEM = cpu, mem
			//	} else {
			//		bbcCpu, bbcMem, err := s.getCPUAndMEM(ctx, &ins.Spec)
			//		if err != nil {
			//			logger.Errorf(ctx, "failed to get bbc cpu and mem: %s", err)
			//			return nil, err
			//		}
			//		ins.Spec.CPU, ins.Spec.MEM = bbcCpu, bbcMem
			//	}
			//}
			InstanceIDMap[ins.Status.Machine.InstanceID] = ins
		}
		InstanceIDMap[ins.Name] = ins
	}

	for _, ins := range ExistedInstancesInCluster {
		if orphanIns, ok := InstanceIDMap[ins.ExistedInstanceID]; ok &&
			orphanIns.Status.InstancePhase == ccetypes.InstancePhaseRunning {
			//if machineType != orphanIns.Spec.MachineType || cpu != orphanIns.Spec.CPU || mem != orphanIns.Spec.MEM || gpuCount != orphanIns.Spec.GPUCount {
			//	return nil, errors.New(fmt.Sprintf("invalid config: machineType|cpu|mem|gpuCount must be equal, instanceID: %s", orphanIns.Spec.CCEInstanceID))
			//}

			// AIInfra的节点可以跳过MachineType&InstanceChargingType检查
			if orphanIns.Spec.AIInfraOption.TemplateID != "" {
				if orphanIns.Status.Machine.InstanceID == "" {
					return nil, fmt.Errorf("instance %s has no Machine instanceID", orphanIns.Name)
				}
				// 游离节点且状态为running，可以加入节点组
				orphanInstanceInfo = append(orphanInstanceInfo, &ccetypes.InstancesToJoin{
					ExistedInstanceID: orphanIns.Status.Machine.InstanceID,
					InstanceName:      orphanIns.Name,
				})
				orphanInstances = append(orphanInstances, orphanIns)
				continue
			}

			if machineType != orphanIns.Spec.MachineType {
				return nil, errors.New(fmt.Sprintf("invalid config: machineType must be %s", machineType))
			}
			if chargingType != orphanIns.Spec.InstanceChargingType {
				return nil, errorcode.NewInvalidParam(fmt.Sprintf("invalid config: InstanceChargingType must be %s", chargingType))
			}
			if orphanIns.Status.Machine.InstanceID == "" {
				return nil, fmt.Errorf("instance %s has no Machine instanceID", orphanIns.Name)
			}
			// 游离节点且状态为running，可以加入节点组
			orphanInstanceInfo = append(orphanInstanceInfo, &ccetypes.InstancesToJoin{
				ExistedInstanceID: orphanIns.Status.Machine.InstanceID,
				InstanceName:      orphanIns.Name,
			})
			orphanInstances = append(orphanInstances, orphanIns)
		} else {
			// 不是游离节点，直接报错返回
			logger.Errorf(ctx, "instance %s status unavailable", ins.ExistedInstanceID)
			return nil, errorcode.NewInvalidInstanceStatus("instance status unavailable")
		}
	}
	for _, ins := range orphanInstances {
		// 保证两个位置的label一致
		// TODO 看不懂，只能理解ins.Spec.Labels的优先级最高，暂不改动。
		if ins.Spec.Labels == nil {
			ins.Spec.Labels = map[string]string{}
		}
		// 集群内节点添加到节点组时，同步节点组主机型的显存共享配置到节点上
		// TODO npu目前不支持开启显存共享
		if ig.Spec.InstanceTemplate.Labels != nil {
			if value, ok := ig.Spec.InstanceTemplate.Labels[ccetypes.GPUSharePluginLabelKey]; ok {
				ins.Spec.Labels[ccetypes.GPUSharePluginLabelKey] = value
			}
		}
		ins.Labels = ins.Spec.Labels

		// 如果该节点曾经在节点组内，在移入前先清理原来的annotations，作为兜底
		delete(ins.Annotations, ccetypes.InstanceGroupCleanPolicyAnnotationKey)
		delete(ins.Annotations, ccetypes.InstancePriorityAnnotationKey)
		delete(ins.Annotations, ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey)

		if ins.Spec.Annotations == nil {
			ins.Spec.Annotations = map[string]string{}
		}
		if ig.Spec.InstanceTemplate.EhcClusterID != "" {
			ins.Spec.EhcClusterID = ig.Spec.InstanceTemplate.EhcClusterID
		}

		// 如果节点在加入集群时设置 iam 角色，但是节点组未设置，这里会被释放掉
		ins.Spec.IAMRole = ig.Spec.IAMRole

		ins.Spec.DeleteOption = ig.Spec.InstanceTemplate.DeleteOption
		// TODO 纳管集群内已有节点，默认复用节点组配置
		ins.Spec.ScaleDownDisabled = ig.Spec.InstanceTemplate.ScaleDownDisabled

		if err = s.K8SClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, ins.Name, &ins); err != nil {
			return nil, fmt.Errorf("failed to update instance to meta cluster, err: %v", err)
		}
	}
	return orphanInstanceInfo, nil
}

func checkMachineType(ctx context.Context, ig *ccev1.InstanceGroup, orphanInstances []ccev1.Instance) error {
	// 机器类型、cpu、内存等需一致
	cpu, mem, gpuCount := ig.Spec.InstanceTemplate.CPU, ig.Spec.InstanceTemplate.MEM, ig.Spec.InstanceTemplate.GPUCount
	machineType := ig.Spec.InstanceTemplate.MachineType
	chargingType := ig.Spec.InstanceTemplate.InstanceChargingType
	for _, instance := range orphanInstances {
		if machineType != instance.Spec.MachineType || cpu != instance.Spec.CPU || mem != instance.Spec.MEM || gpuCount != instance.Spec.GPUCount {
			return errors.New(fmt.Sprintf("invalid config: machineType|cpu|mem|gpuCount must be equal, instanceID: %s", instance.Status.Machine.InstanceID))
		}
		if chargingType != instance.Spec.InstanceChargingType {
			return errors.New(fmt.Sprintf("invalid config: InstanceChargingType must be %s", chargingType))
		}
	}
	return nil
}

// getCPUAndMEM - 从flavor接口获取
func (s *InstanceGroupService) getCPUAndMEM(ctx context.Context, instance *ccetypes.InstanceSpec) (int, int, error) {
	cpu := 0
	mem := 0

	flavors, err := s.filler.SDKClients.BBCClient.GetFlavors(ctx, s.filler.SDKClients.STSClient.NewSignOption(ctx, instance.AccountID))
	if err != nil {
		logger.Errorf(ctx, "failed to get bbc flavors: %s", err)
		return cpu, mem, err
	}

	for _, flavor := range flavors.Flavors {
		if flavor.FlavorId == string(instance.BBCOption.Flavor) {
			cpu = flavor.CpuCount
			mem = flavor.MemoryCapacityInGB
			return cpu, mem, nil
		}
	}

	logger.Errorf(ctx, "flavor is: %s, no matched one in: %s", instance.BBCOption.Flavor, utils.ToJSON(flavors.Flavors))
	return cpu, mem, nil
}

// scaleUpExistNodeNotInCluster 该函数用于在节点组中已经存在的节点，但是不在集群中的情况下进行扩容。
// 参数：
//
//	ctx context.Context - 上下文信息，包含请求信息和超时信息。
//	service services.Interface - 服务接口，提供实例操作相关方法。
//	cluster *ccev1.Cluster - 集群对象，包含集群的基础信息。
//	ig *ccev1.InstanceGroup - 节点组对象，包含节点组的基础信息。
//	useInstanceGroupConfig bool - 是否使用节点组默认配置，true表示使用，false表示不使用。
//	instances []*ccesdk.InstanceSet - 要扩容的节点列表，每个节点都包含了节点的配置信息。
//
// 返回值：
//
//	[]*ccetypes.InstancesToJoin - 返回一个包含了已经创建好的节点名称和已经存在的节点 ID 的切片，用于后续处理。
//	    InstancesToJoin.ExistedInstanceID - 已经存在的节点 ID。
//	    InstancesToJoin.InstanceName - 已经创建好的节点名称。
//	 error - 如果发生错误，返回错误信息；否则返回 nil。
func (s *InstanceGroupService) scaleUpExistNodeNotInCluster(ctx context.Context, service services.Interface,
	cluster *ccev1.Cluster, ig *ccev1.InstanceGroup,
	useInstanceGroupConfig bool, instances []*ccesdk.InstanceSet, useInstanceGroupConfigWithDiskInfo bool) ([]*ccetypes.InstancesToJoin, error) {
	// 使用节点组默认配置
	if useInstanceGroupConfig {
		instances = s.overrideInstancesByInstanceGroup(ctx, ig, instances, useInstanceGroupConfigWithDiskInfo)
		// 使用节点组的容器运行时配置，如果节点组容器运行时没设置，则使用集群级别
		for _, ins := range instances {
			if ins.InstanceSpec.RuntimeVersion == "" && ins.InstanceSpec.RuntimeType == "" {
				if ig.Spec.InstanceTemplate.RuntimeVersion != "" && ig.Spec.InstanceTemplate.RuntimeType != "" {
					ins.InstanceSpec.RuntimeVersion = ig.Spec.InstanceTemplate.RuntimeVersion
					ins.InstanceSpec.RuntimeType = ig.Spec.InstanceTemplate.RuntimeType
				} else if cluster.Spec.RuntimeVersion != "" && cluster.Spec.RuntimeType != "" {
					ins.InstanceSpec.RuntimeVersion = cluster.Spec.RuntimeVersion
					ins.InstanceSpec.RuntimeType = cluster.Spec.RuntimeType
				} else {
					// TODO 目前由于前端没有可以指定运行时的版本入口，这里暂时打印日志，后续运行时版本在1950行代码中决定。
					logger.Errorf(ctx, "instancegroup and cluster runtimeVersion are empty. ")
				}
			}
		}
	}
	logger.Infof(ctx, "ywjtest-2 instances = %s", utils.ToJSON(instances))
	// 补全 NodeSpecs 字段
	if err := service.EnsureInstanceSets(ctx, ccetypes.ClusterRoleNode, &(cluster.Spec), instances); err != nil {
		return nil, err
	}

	// 放开cpu，mem & gpuCount 的限制
	// 机器类型、cpu、内存等需一致
	//cpu, mem, gpuCount := ig.Spec.InstanceTemplate.CPU, ig.Spec.InstanceTemplate.MEM, ig.Spec.InstanceTemplate.GPUCount
	//if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeBBC {
	//	bbcCpu, bbcMem, err := s.getCPUAndMEM(ctx, &ig.Spec.InstanceTemplate.InstanceSpec)
	//	if err != nil {
	//		logger.Errorf(ctx, "failed to get bbc cpu and mem: %s", err)
	//		return nil, err
	//	}
	//	cpu, mem, gpuCount = bbcCpu, bbcMem, 0
	//}
	machineType := ig.Spec.InstanceTemplate.MachineType
	chargingType := ig.Spec.InstanceTemplate.InstanceChargingType
	for _, ins := range instances {
		//if machineType != ins.InstanceSpec.MachineType || cpu != ins.InstanceSpec.CPU || mem != ins.InstanceSpec.MEM || gpuCount != ins.InstanceSpec.GPUCount {
		//	return nil, errors.New(fmt.Sprintf("invalid config: machineType|cpu|mem|gpuCount must be equal, instanceID: %s", ins.InstanceSpec.CCEInstanceID))
		//}
		if machineType != ins.InstanceSpec.MachineType {
			return nil, errors.New(fmt.Sprintf("invalid config: machineType must be %s", machineType))
		}
		if chargingType != ins.InstanceSpec.InstanceChargingType {
			return nil, errorcode.NewInvalidParam(fmt.Sprintf("invalid config: InstanceChargingType must be %s", chargingType))
		}
		// 校验 instance 是否与cce 集群属于同一 vpc
		if ins.InstanceSpec.VPCID != cluster.Spec.VPCID {
			logger.Errorf(ctx, "invalid config: instance: %s InstanceSpec.VPCID must be %s", ins.InstanceSpec.CCEInstanceID, cluster.Spec.VPCID)
			return nil, errorcode.NewInvalidVPC("invalid vpc config")
		}
	}

	// 或不使用节点组默认配置，当脚本内容不为空，则创建user script
	if !useInstanceGroupConfig {
		userScriptService := userscript.NewUserScriptService(s.model)
		// TODO 改成调用 userscript.CreateUserScripts
		for _, nodeSpec := range instances {
			if nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript != "" {

				if utils.CheckStringGreaterThanMaxSize(nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					return nil, fmt.Errorf("Check user script size: %d greater than max size: %d ", len(nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
				}

				userScriptID, err := userScriptService.Create(ctx, nodeSpec.InstanceSpec.AccountID, nodeSpec.InstanceSpec.UserID, nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript)
				if err != nil {
					return nil, err
				}

				nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript = userScriptID
			}
			if nodeSpec.InstanceSpec.DeployCustomConfig.PostUserScript != "" {

				if utils.CheckStringGreaterThanMaxSize(nodeSpec.InstanceSpec.DeployCustomConfig.PostUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					return nil, fmt.Errorf("Check user script size: %d greater than max size: %d ", len(nodeSpec.InstanceSpec.DeployCustomConfig.PreUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
				}

				userScriptID, err := userScriptService.Create(ctx, nodeSpec.InstanceSpec.AccountID, nodeSpec.InstanceSpec.UserID, nodeSpec.InstanceSpec.DeployCustomConfig.PostUserScript)
				if err != nil {
					return nil, err
				}

				nodeSpec.InstanceSpec.DeployCustomConfig.PostUserScript = userScriptID
			}
			// 校验kubelet和容器运行时数据目录是否符合预期
			err := fillspec.SetAndCheckDataDir(nodeSpec.InstanceSpec.DeployCustomConfig, nodeSpec.InstanceSpec.RuntimeType)
			if err != nil {
				logger.Errorf(ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
				return nil, fmt.Errorf("invalid data Dir, err is: %s", err.Error())
			}
		}
	}
	// NodeSpecs 转成 Models
	var nodes []*models.Instance
	nodes, err := service.InstanceSetsToModel(ctx, instances)
	if err != nil {
		return nil, err
	}
	// 创建数据库记录
	_, err = s.model.CreateInstances(ctx, cluster.Spec.ClusterID, nodes)
	if err != nil {
		return nil, err
	}
	// 创建 CRD
	logger.Infof(ctx, "Create Instance in MetaCluster: %v", utils.ToJSON(nodes))

	instancesToCreate := make([]*ccev1.Instance, 0)
	for _, ins := range nodes {
		// instance CRD
		cceInstanceID := ins.Spec.CCEInstanceID
		if ins.Spec.Labels == nil {
			ins.Spec.Labels = map[string]string{}
		}

		if ins.Spec.Annotations == nil {
			ins.Spec.Annotations = map[string]string{}
		}

		// instance CRD
		instanceCRD := &ccev1.Instance{
			TypeMeta: metav1.TypeMeta{
				Kind:       ccev1.InstanceKind,
				APIVersion: ccev1.InstanceAPIVersion,
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: consts.MetaClusterDefaultNamespace,
				Name:      cceInstanceID,
				Labels: map[string]string{
					ccetypes.ClusterIDLabelKey:   ins.Spec.ClusterID,
					ccetypes.ClusterRoleLabelKey: string(ins.Spec.ClusterRole),
				},
				Annotations: map[string]string{ccetypes.InstancePriorityAnnotationKey: fmt.Sprintf("%d", ins.Spec.CCEInstancePriority)},
				Finalizers:  []string{ccev1.InstanceFinalizer},
			},
		}

		instanceCRD.Spec = *(ins.Spec.DeepCopy())

		// 兼容老集群, 写入 Status, 避免 Reconcile 流程
		if ins.Status != nil {
			instanceCRD.Status = *(ins.Status.DeepCopy())
		}

		logger.Infof(ctx, "Create Instance in MetaCluster: %v", utils.ToJSON(instanceCRD))
		instancesToCreate = append(instancesToCreate, instanceCRD)
	}
	instanceToJoin, err := s.createInstances(ctx, instancesToCreate, 3)
	logger.Infof(ctx, "scaleUpExistNodeNotInCluster createInstances, instanceToJoin:%v, err:%v", utils.ToJSON(instanceToJoin), err)
	if len(instanceToJoin) > 0 && err != nil {
		return instanceToJoin, models.ErrPartialSuccess.New(ctx, err.Error())
	}
	return instanceToJoin, err
}

func (s *InstanceGroupService) createInstances(ctx context.Context, instances []*ccev1.Instance, retryTimes int) ([]*ccetypes.InstancesToJoin, error) {
	log := logger.WithValues("fn", "instanceGroupService.createInstances")
	type insTask struct {
		instance *ccev1.Instance
		rollback bool
	}

	workWg := sync.WaitGroup{}
	taskWg := sync.WaitGroup{}

	errs := &multierror.Error{}
	instancesToJoin := make([]*ccetypes.InstancesToJoin, 0)
	mutex := sync.Mutex{}

	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())
	taskWg.Add(len(instances))
	for _, ins := range instances {
		log.Infof(ctx, "add task: %v", ins.Name)
		queue.Add(insTask{
			instance: ins,
		})
	}

	workerNum := 5
	workWg.Add(workerNum)
	for i := 0; i < workerNum; i++ {
		j := i
		go func() {
			log := log.WithValues("workerID", j)
			defer func() {
				log.Infof(ctx, "worker exit")
				workWg.Done()
			}()
			for {
				item, shutdown := queue.Get()
				if shutdown {
					return
				}
				task := item.(insTask)
				var err error
				if !task.rollback {
					_, err = s.K8SClient.CreateInstance(ctx, consts.MetaClusterDefaultNamespace, task.instance)
				} else {
					err = s.model.UpdateInstancePhase(ctx, task.instance.Name, task.instance.Spec.AccountID, ccetypes.InstancePhaseDeleted)
				}
				log.Infof(ctx, "get task: %v, isRollback:%v, err:%v", task.instance.Name, task.rollback, err)
				if err != nil {
					if queue.NumRequeues(task) < retryTimes {
						taskWg.Add(1)
						queue.AddRateLimited(task)
						log.Infof(ctx, "requeue task: %v, isRollback:%v", task.instance.Name, task.rollback)
					} else {
						action := "create instance"
						if task.rollback {
							action = "rollback instance"
						}
						mutex.Lock()
						errs = multierror.Append(errs, fmt.Errorf("%v:%v:%v", task.instance.Name, action, err))
						mutex.Unlock()

						if !task.rollback {
							taskWg.Add(1)
							queue.Add(insTask{
								instance: task.instance,
								rollback: true,
							})
							log.Infof(ctx, "add rollback task: %v", task.instance.Name)
						}
						queue.Forget(task)
					}
				} else {
					if !task.rollback {
						mutex.Lock()
						instancesToJoin = append(instancesToJoin, &ccetypes.InstancesToJoin{
							ExistedInstanceID: task.instance.Spec.ExistedOption.ExistedInstanceID,
							InstanceName:      task.instance.Name,
						})
						log.Infof(ctx, "task %v done, machine id:%v", task.instance.Name, task.instance.Spec.ExistedOption.ExistedInstanceID)
						mutex.Unlock()
					}
					queue.Forget(task)
				}
				queue.Done(task)
				taskWg.Done()
			}
		}()
	}

	taskWg.Wait()
	queue.ShutDown()
	workWg.Wait()

	return instancesToJoin, errs.ErrorOrNil()
}

func (s *InstanceGroupService) overrideInstancesByInstanceGroup(ctx context.Context, ig *ccev1.InstanceGroup, instances []*ccesdk.InstanceSet, useInstanceGroupConfigWithDiskInfo bool) []*ccesdk.InstanceSet {
	getSgFromInstanceGroup := func(ig *ccev1.InstanceGroup) []ccetypes.SecurityGroupV2 {
		var expectedSgs []ccetypes.SecurityGroupV2

		if len(ig.Spec.InstanceTemplate.VPCConfig.SecurityGroups) != 0 {
			expectedSgs = ig.Spec.InstanceTemplate.VPCConfig.SecurityGroups
		}
		if len(ig.Spec.DefaultSecurityGroups) != 0 {
			expectedSgs = ig.Spec.DefaultSecurityGroups
		}
		return expectedSgs
	}

	getSgTypeOfInstance := func(ins *ccesdk.InstanceSet) ccetypes.SecurityGroupType {
		if len(ins.InstanceSpec.SecurityGroups) != 0 {
			return ins.InstanceSpec.SecurityGroups[0].Type
		}
		return ins.InstanceSpec.SecurityGroupType
	}

	getSgTypeOfInstanceGroup := func(ig *ccev1.InstanceGroup) ccetypes.SecurityGroupType {
		if len(ig.Spec.DefaultSecurityGroups) != 0 {
			return ig.Spec.DefaultSecurityGroups[0].Type
		}
		if len(ig.Spec.InstanceTemplate.VPCConfig.SecurityGroups) != 0 {
			return ig.Spec.InstanceTemplate.VPCConfig.SecurityGroups[0].Type
		}

		return ig.Spec.InstanceTemplate.SecurityGroupType
	}
	var ephemeralDiskList []ccetypes.EphemeralDiskConfig
	var cdsList ccetypes.CDSConfigList

	var newInstances []*ccesdk.InstanceSet
	for _, ins := range instances {
		ins.InstanceSpec.VPCConfig = ccetypes.VPCConfig{
			SecurityGroup:     ig.Spec.InstanceTemplate.VPCConfig.SecurityGroup,
			SecurityGroupType: ig.Spec.InstanceTemplate.VPCConfig.SecurityGroupType,
		}
		ins.InstanceSpec.EhcClusterID = ig.Spec.InstanceTemplate.EhcClusterID
		ins.InstanceSpec.DeployCustomConfig = ig.Spec.InstanceTemplate.DeployCustomConfig
		ins.InstanceSpec.Tags = ig.Spec.InstanceTemplate.Tags
		ins.InstanceSpec.RelationTag = ig.Spec.InstanceTemplate.RelationTag

		if getSgTypeOfInstance(ins) != getSgTypeOfInstanceGroup(ig) {
			ins.InstanceSpec.SecurityGroups = getSgFromInstanceGroup(ig)
		} else {
			ins.InstanceSpec.SecurityGroups = append(ins.InstanceSpec.SecurityGroups, getSgFromInstanceGroup(ig)...)
		}
		// TODO GPU节点组才处理,但是这里ins是前端传递的参数，里面没有InstanceType类型，这里默认都赋值。CPU节点，该字段无影响。
		// 节点未指定版本，则使用节点组版本
		if ins.InstanceSpec.NvidiaContainerToolkitVersion == "" && ig.Spec.InstanceTemplate.NvidiaContainerToolkitVersion != "" {
			ins.InstanceSpec.NvidiaContainerToolkitVersion = ig.Spec.InstanceTemplate.NvidiaContainerToolkitVersion
		}

		// XPU节点处理 - 节点未指定XPU版本，则使用节点组版本
		if ins.InstanceSpec.XPUContainerToolkitVersion == "" && ig.Spec.InstanceTemplate.XPUContainerToolkitVersion != "" {
			ins.InstanceSpec.XPUContainerToolkitVersion = ig.Spec.InstanceTemplate.XPUContainerToolkitVersion
		}
		ins.InstanceSpec.IAMRole = ig.Spec.IAMRole

		if ins.InstanceSpec.MachineType == ccetypes.MachineTypeHPAS && useInstanceGroupConfigWithDiskInfo {
			ins.InstanceSpec.InstanceResource.EphemeralDiskList = ig.Spec.InstanceTemplate.InstanceResource.EphemeralDiskList
		}

		if !useInstanceGroupConfigWithDiskInfo && ins.InstanceSpec.InstanceResource.CDSList == nil && ins.InstanceSpec.InstanceResource.EphemeralDiskList == nil {
			ins.InstanceSpec.InstanceResource.CDSList = cdsList
			ins.InstanceSpec.InstanceResource.EphemeralDiskList = ephemeralDiskList
		}

		// 节点缩容保护字段同步
		ins.InstanceSpec.ScaleDownDisabled = ig.Spec.InstanceTemplate.ScaleDownDisabled

		// bugfix ebc扩容节点丢失label和annotation标签问题
		if ins.InstanceSpec.Labels == nil || len(ins.InstanceSpec.Labels) <= 0 {
			ins.InstanceSpec.Labels = ig.Spec.InstanceTemplate.Labels
		} else {
			for key, value := range ig.Spec.InstanceTemplate.Labels {
				ins.InstanceSpec.Labels[key] = value
			}
		}
		if ins.InstanceSpec.Annotations == nil || len(ins.InstanceSpec.Annotations) <= 0 {
			ins.InstanceSpec.Annotations = ig.Spec.InstanceTemplate.Annotations
		} else {
			for key, value := range ig.Spec.InstanceTemplate.Annotations {
				ins.InstanceSpec.Annotations[key] = value
			}
		}
		if ins.InstanceSpec.Taints == nil || len(ins.InstanceSpec.Annotations) <= 0 {
			ins.InstanceSpec.Taints = ig.Spec.InstanceTemplate.Taints
		} else {
			for _, temp := range ig.Spec.InstanceTemplate.Taints {
				ins.InstanceSpec.Taints = append(ins.InstanceSpec.Taints, temp)
			}
		}

		newInstances = append(newInstances, ins)
	}

	return newInstances
}

func (s *InstanceGroupService) ScaleDown(ctx context.Context, accountID, instanceGroupID string, instancesToBeRemoved []string, k8sNodesToBeRemoved []string, opts ScaleDownOption) (string, error) {
	log := s.log.WithValues("accountID", accountID).
		WithValues("instanceGroupID", instanceGroupID)

	if instancesToBeRemoved == nil {
		instancesToBeRemoved = []string{}
	}
	instancesToBeRemovedSet := map[string]struct{}{}
	for _, instanceID := range instancesToBeRemoved {
		if _, found := instancesToBeRemovedSet[instanceID]; found {
			return "", fmt.Errorf("duplicated instances to be removed found")
		}
		instancesToBeRemovedSet[instanceID] = struct{}{}
	}
	k8sNodesToBeRemovedSet := map[string]struct{}{}
	for _, nodeName := range k8sNodesToBeRemoved {
		if _, found := k8sNodesToBeRemovedSet[nodeName]; found {
			return "", fmt.Errorf("duplicated k8s node to be removed found")
		}
		k8sNodesToBeRemovedSet[nodeName] = struct{}{}
	}

	ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
	if err != nil {
		log.WithValues("instanceGroupID", ig.Spec.CCEInstanceGroupID).Errorf(ctx, "failed to get instanceGroup from meta cluster, err: %v", err)
		return "", nil
	}

	if ig.Spec.AccountID != accountID {
		log.Errorf(ctx, "instancegroup belong to account: %s, not %s", ig.Spec.AccountID, accountID)
		return "", fmt.Errorf("permision deny")
	}

	if len(instancesToBeRemoved) == 0 && len(k8sNodesToBeRemoved) > 0 {
		// 查询 Cluster
		cluster, err := s.model.GetCluster(ctx, ig.Spec.ClusterID, accountID)
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %v", err)
			return "", fmt.Errorf("GetCluster failed: %v", err)
		}
		if cluster == nil {
			logger.Errorf(ctx, "cluster %s not exist", ig.Spec.ClusterID)
			return "", fmt.Errorf("cluster %s not exist", ig.Spec.ClusterID)
		}
		// 数据库查询全量 Instance
		list, err := s.model.GetInstancesByInstanceGroupID(ctx, accountID, ig.Spec.ClusterID, instanceGroupID,
			"", "", "", "", 100000, 1)
		if err != nil {
			logger.Errorf(ctx, "GetInstancesByInstanceGroupID failed: %v", err)
			return "", fmt.Errorf("GetInstancesByInstanceGroupID failed")
		}
		for i := 0; i < len(k8sNodesToBeRemoved); i++ {
			find := false
			for j := 0; j < len(list.Items); j++ {
				k8sNodeName := utils.GetNodeName(ctx, list.Items[j].Status.Machine.VPCIP, list.Items[j].Status.Machine.Hostname, list.Items[j].Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)
				if k8sNodeName == k8sNodesToBeRemoved[i] {
					find = true
					instancesToBeRemoved = append(instancesToBeRemoved, list.Items[j].Spec.CCEInstanceID)
				}
			}
			if !find {
				return "", fmt.Errorf("k8sNode %s not exist", k8sNodesToBeRemoved[i])
			}
		}

	}

	removingInstances := make([]string, 0)
	if len(ig.Spec.InstancesToBeRemoved) > 0 {
		for _, instanceId := range instancesToBeRemoved {
			if _, ok := ig.Spec.InstancesToBeRemoved[instanceId]; ok {
				removingInstances = append(removingInstances, instanceId)
			}
		}
	}
	// 如果待移除节点正在移除中拒绝请求
	if len(removingInstances) > 0 {
		return "", models.ErrIgInstanceRemovalInProgress.New(ctx,
			fmt.Sprintf("节点 %v 正在移除中", removingInstances))
	}

	// 检查节点组是否存在冲突任务，不能更新
	err = s.exitConflictWorkflowByInstanceGroup(ctx, ig.Spec.CCEInstanceGroupID)
	if err != nil {
		log.Errorf(ctx, "exitConflictWorkflowByInstanceGroup, err: %v", err)
		return "", err
	}

	// 选择节点移除策略
	cleanPolicy, deleteOption := buildCleanPolicyAndDeleteOption(ig, opts)

	randName := s.rand(8)
	var backoffLimit int32 = 20 // 20次尝试

	task := &ccev1.Task{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: consts.MetaClusterDefaultNamespace,
			Name:      "task-" + ig.Spec.CCEInstanceGroupID + "-scaledown-" + randName,
			Labels: map[string]string{
				ccetypes.ClusterIDLabelKey:                               ig.Spec.ClusterID,
				ccetypes.InstanceGroupIDLabelKey:                         ig.Spec.CCEInstanceGroupID,
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Finalizers: []string{ccev1.DBSyncFinalizer},
		},
		Spec: ccetypes.TaskSpec{
			Handler:      s.handler,
			TaskType:     ccetypes.TaskTypeInstanceGroupReplicas,
			UserID:       ig.Spec.UserID,
			AccountID:    ig.Spec.AccountID,
			CreatedTime:  utils.Now().Format(time.RFC3339Nano),
			BackoffLimit: &backoffLimit,
			TargetRef: &ccetypes.TaskTargetReference{
				Name:      ig.Name,
				Namespace: ig.Namespace,
			},
			AntiAffinity: ccetypes.TaskAntiAffinity{
				ccetypes.InstanceGroupReplicasTaskScaleOperationLabelKey: ig.Spec.CCEInstanceGroupID,
			},
			Operation: runtime.RawExtension{
				Object: &ccev1.InstanceGroupReplicasOperation{
					TypeMeta: metav1.TypeMeta{},
					Spec: ccetypes.InstanceGroupReplicasOperationSpec{
						OperationType:        ccetypes.InstanceGroupOperationTypeScalingDown,
						InstancesToBeDeleted: instancesToBeRemoved,
						CleanPolicy:          cleanPolicy,
						DeleteOption:         deleteOption,
					},
				},
			},
		},
	}

	log.Infof(ctx, "going to create task: %s", utils.ToJSON(task))
	task, err = s.K8SClient.CreateTask(ctx, task)
	if err != nil {
		log.Errorf(ctx, "failed to create scaling up task, err: %v", err)
		return "", err
	}

	return task.Name, nil
}

// markInstanceAsCandidate - 标记instances为加入节点组的候选节点
//
// PARAMS:
//   - ctx: context.Context
//   - accountID: accountID
//   - instanceID: instanceID
//   - instanceGroupID: instanceGroupID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) markInstanceAsCandidate(ctx context.Context, accountID, instanceID, instanceGroupID string) error {
	log := s.log.WithValues("method", "instanceGroupService.markInstanceAsCandidate").
		WithValues("instanceID", instanceID).
		WithValues("instanceGroupID", instanceGroupID)

	iModel, err := s.model.GetInstanceByCCEID(ctx, instanceID, accountID)
	if err != nil {
		log.Errorf(ctx, "failed to get instance from db, err: %v", err)
		return err
	}

	if iModel.Spec.Labels == nil {
		iModel.Spec.Labels = map[string]string{}
	}

	if igID, found := iModel.Spec.Labels[ccetypes.InstanceGroupIDLabelKey]; (found && igID != "" && igID != instanceGroupID) ||
		(iModel.Spec.InstanceGroupID != "" && iModel.Spec.InstanceGroupID != instanceGroupID) {
		// instance 已经在别的节点组，不能重复加入
		log.Errorf(ctx, "duplicated joining, instance belong to other InstanceGroup")
		return fmt.Errorf("duplicated joining, instance belong to other InstanceGroup")
	}

	// 仅仅允许把running的instance移入instanceGroup
	if iModel.Status.InstancePhase != ccetypes.InstancePhaseRunning {
		log.Errorf(ctx, "not running instance can not join instanceGroup")
		return fmt.Errorf("not running instance can not join instanceGroup")
	}

	if igID, found := iModel.Spec.Labels[ccetypes.JoiningInstanceGroupIDLabelKey]; found {
		if igID != instanceGroupID {
			// instance 正在加入别的节点组
			log.Errorf(ctx, "duplicated joining, instance is joining other InstanceGroup")
			return fmt.Errorf("duplicated joining, instance is joining other InstanceGroup")
		}
	} else {
		iModel.Spec.Labels[ccetypes.JoiningInstanceGroupIDLabelKey] = instanceGroupID
		if err := s.model.UpdatePartInstanceSpec(ctx, accountID, instanceID, iModel.Spec); err != nil {
			log.Errorf(ctx, "failed to update Instance spec label in db, err: %v", err)
			return err
		}
	}

	if iModel.Spec.Annotations == nil {
		iModel.Spec.Annotations = map[string]string{}
	}

	instance, err := s.K8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, &metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get instance from meta cluster, err: %v", err)
		return err
	}

	// 保证两个位置的label一致
	if instance.Spec.Labels == nil {
		instance.Spec.Labels = map[string]string{}
	}
	instance.Spec.Labels[ccetypes.JoiningInstanceGroupIDLabelKey] = instanceGroupID
	instance.Labels = instance.Spec.Labels
	// 如果该节点曾经在节点组内，在移入前先清理原来的annotations，作为兜底
	delete(instance.Annotations, ccetypes.InstanceGroupCleanPolicyAnnotationKey)
	delete(instance.Annotations, ccetypes.InstancePriorityAnnotationKey)
	delete(instance.Annotations, ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey)

	if instance.Spec.Annotations == nil {
		instance.Spec.Annotations = map[string]string{}
	}

	if err = s.K8SClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, instance); err != nil {
		log.Errorf(ctx, "failed to update instance to meta cluster, err: %v", err)
		return err
	}

	return nil
}

// markInstanceToBeRemoved - 标记instances为优先移出节点组的节点
//
// PARAMS:
//   - ctx: context.Context
//   - accountID: accountID
//   - instanceID: instanceID
//   - instanceGroupID: instanceGroupID
//   - cleanPolicy: 为退出节点组的节点设置ccetypes.CleanPolicy
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) markInstanceToBeRemoved(ctx context.Context, accountID, instanceID, instanceGroupID string,
	cleanPolicy ccetypes.CleanPolicy, deleteOption *ccetypes.DeleteOption) error {

	log := s.log.WithValues("method", "instanceGroupService.markInstanceToBeRemoved").
		WithValues("instanceID", instanceID).
		WithValues("instanceGroupID", instanceGroupID)

	iModel, err := s.model.GetInstanceByCCEID(ctx, instanceID, accountID)
	if err != nil {
		log.Errorf(ctx, "failed to get instance from db, err: %v", err)
		return err
	}

	if igID, found := iModel.Spec.Labels[ccetypes.InstanceGroupIDLabelKey]; (found && igID != instanceGroupID) || iModel.Spec.InstanceGroupID != instanceGroupID {
		// instance 在别的节点组，不能从指定节点组退出
		log.Errorf(ctx, "invalid removing, instance belong to other InstanceGroup")
		return fmt.Errorf("invalid removing, instance belong to other InstanceGroup")
	}

	instance, err := s.K8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, &metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get instance from meta cluster, err: %v", err)
		return err
	}

	if instance.Annotations == nil {
		instance.Annotations = map[string]string{}
	}
	instance.Annotations[ccetypes.InstancePriorityAnnotationKey] = "0"
	if cleanPolicy != "" {
		instance.Annotations[ccetypes.InstanceGroupCleanPolicyAnnotationKey] = string(cleanPolicy)
	}

	// 把deleteOption序列化保存到annotation中，从而InstanceGroup controller能够获取对于单台的instance的deleteOption设置，
	// 而不需要修改整个InstanceGroup spec中整体的设置。
	if deleteOption != nil {
		instanceDeleteOptionJSON, err := json.Marshal(deleteOption)
		if err != nil {
			log.Errorf(ctx, "failed to marshal deletion option, err: %v", err)
			return err
		}
		instance.Annotations[ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey] = string(instanceDeleteOptionJSON)
	}

	if err = s.K8SClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, instance); err != nil {
		log.Errorf(ctx, "failed to update instance to meta cluster, err: %v", err)
		return err
	}

	return nil
}

// verifyAndPadDefault - 校验节点组spec配置并且自动填充部分需要自动生成的字段
//
// PARAMS:
//   - ctx: context.Context
//   - clusterSpec: *ccetypes.ClusterSpec
//   - instanceGroupSpec: *ccetypes.InstanceGroupSpec
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (s *InstanceGroupService) VerifyAndPadDefault(ctx context.Context, clusterSpec *ccetypes.ClusterSpec, instanceGroupSpec *ccetypes.InstanceGroupSpec, action string) error {
	log := s.log.WithValues("method", "instanceGroupService.verifyAndPadDefault")

	if action == "create" {
		quota, err := s.quotaClient.WithCache(quota.GetNodeCache()).GetNodeQuotaWithCache(ctx, clusterSpec.ClusterID, instanceGroupSpec.Replicas)
		if err != nil {
			log.Errorf(ctx, "failed to get cluster node quota info, err: %v", err)
			return err
		}

		if instanceGroupSpec.Replicas > quota.Quota-quota.Used {
			log.WithValues("quota", quota.Quota, "used", quota.Used, "instanceGroup.replicas", instanceGroupSpec.Replicas).Errorf(ctx, "exceed cluster node quota")
			return fmt.Errorf("exceed cluster node quota")
		}

		// 创建节点组时 增加对托管集群规格的限制
		err = s.checkCCENodeNumLimitAndReplicas(ctx, clusterSpec.ClusterID, clusterSpec.AccountID, instanceGroupSpec.Replicas)
		if err != nil {
			return err
		}
	}

	instanceTmplCount := len(instanceGroupSpec.InstanceTemplates)

	instances := []*ccetypes.InstanceSpec{}
	// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
	if instanceTmplCount > 0 {
		for i := 0; i < len(instanceGroupSpec.InstanceTemplates); i++ {
			ins := instanceSpecTmplToSpec(&instanceGroupSpec.InstanceTemplates[i])
			instances = append(instances, ins)
			// 预付费实例不支持自动扩缩容
			if instanceGroupSpec.InstanceTemplates[i].InstanceChargingType == bcc.PaymentTimingPrepaid &&
				instanceGroupSpec.ClusterAutoscalerSpec != nil && instanceGroupSpec.ClusterAutoscalerSpec.Enabled {
				return fmt.Errorf("invalid config: Prepaid instance do not support clusterAutoscaler")
			}
		}
	} else {
		ins := instanceSpecTmplToSpec(&instanceGroupSpec.InstanceTemplate)
		instances = append(instances, ins)
		// 预付费实例不支持自动扩缩容
		if instanceGroupSpec.InstanceTemplate.InstanceChargingType == bcc.PaymentTimingPrepaid &&
			instanceGroupSpec.ClusterAutoscalerSpec != nil && instanceGroupSpec.ClusterAutoscalerSpec.Enabled {
			return fmt.Errorf("invalid config: Prepaid instance do not support clusterAutoscaler")
		}
	}

	// 机器类型、cpu、内存等需一致
	var machineType ccetypes.MachineType
	cpu, mem, gpuCount := 0, 0, 0
	instanceTempIDs := map[string]bool{}
	for i := 0; i < len(instances); i++ {
		instance := instances[i]
		if i == 0 {
			machineType = instance.MachineType
			cpu = instance.CPU
			mem = instance.MEM
			gpuCount = instance.GPUCount
		} else {
			if instance.MachineType != machineType || cpu != instance.CPU || mem != instance.MEM || gpuCount != instance.GPUCount {
				return errors.New(fmt.Sprintf("invalid config: machineType|cpu|mem|gpuCount must be equal"))
			}
		}

		log.Infof(ctx, "start validate instance precharge option")
		if err := s.filler.ResourceChargingOption(ctx, instance); err != nil {
			log.Errorf(ctx, "failed to fill resource charging option, err: %v", err)
			return err
		}
		log.Infof(ctx, "validate instance precharge option success")

		// 生成instanceTemplateID
		instanceTemplateID := ""
		if instance.InstanceTemplateID == "" {
			for i := 0; i < 10; i++ {
				id := utils.RandString(8)
				_, ok := instanceTempIDs[id]
				if ok {
					continue
				}
				instanceTemplateID = id
				instanceTempIDs[id] = true
				break
			}
		} else {
			instanceTemplateID = instance.InstanceTemplateID
			instanceTempIDs[instance.InstanceTemplateID] = true
		}
		if instanceTemplateID == "" {
			return errors.New("check InstanceTemplateID Duplicated")
		}
		instance.InstanceTemplateID = instanceTemplateID
	}

	userScriptService := userscript.NewUserScriptService(s.model)

	wg := &sync.WaitGroup{}
	filterErrs := make([]error, 0)
	for i := 0; i < len(instances); i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			instance := instances[i]
			// 初始化 instanceFiller
			filler, err := s.fillClients.NewInstanceFiller(ctx, s.filler, instanceGroupSpec.ClusterRole, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "NewInstanceFiller failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			wg2 := sync.WaitGroup{} // 一些耗时长的动作，并发执行

			// Handler
			if s.filler.Config.Handler != "" {
				instance.Handler = s.filler.Config.Handler
			}

			// ClusterRole
			instance.ClusterRole = instanceGroupSpec.ClusterRole

			// RuntimeType & RuntimeVersion
			if instanceGroupSpec.ClusterRole == ccetypes.ClusterRoleNode {
				if instance.RuntimeType == "" || instance.RuntimeVersion == "" {
					// 节点运行时版本和节点组配置一致
					instance.RuntimeType = clusterSpec.RuntimeType
					instance.RuntimeVersion = clusterSpec.RuntimeVersion
					if instance.RuntimeType == "" || instance.RuntimeVersion == "" {
						instance.RuntimeType = ccetypes.DefaultRuntimeVersion.GetRuntimeType()
						instance.RuntimeVersion = ccetypes.DefaultRuntimeVersion.GetRuntimeVersion()
					}
				} else {
					logger.Infof(ctx, "instance use custom runtime, "+
						"instanceGroupId:%s, instanceId:%s, instanceRuntimeType:%s, instanceRuntimeVersion:%s",
						instance.InstanceGroupID, instance.CCEInstanceID, instance.RuntimeType, instance.RuntimeVersion)
				}
			}

			// ClusterID
			if clusterSpec.ClusterID != "" {
				instance.ClusterID = clusterSpec.ClusterID
			}

			// UserID, AccountID
			if clusterSpec.UserID == "" || clusterSpec.AccountID == "" {
				filterErrs = append(filterErrs, fmt.Errorf("clusterSpec.UserID or clusterSpec.AccountID is empty"))
				return
			}

			if clusterSpec.K8SCustomConfig.EnableHostname && instance.InstanceName != strings.ToLower(instance.InstanceName) {
				logger.Errorf(ctx, "instanceName: %s does not support uppercase letters", instance.InstanceName)
				filterErrs = append(filterErrs, fmt.Errorf("instanceName: %s does not support uppercase letters", instance.InstanceName))
				return
			}

			instance.UserID = s.filler.UserID
			instance.AccountID = s.filler.AccountID

			// Existed & ExistedOption
			existed, existedOption, err := filler.ExistedOption(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "Existed failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.Existed = existed
			instance.ExistedOption = existedOption

			// MasterType
			machineType, instanceType, bbcOption, err := filler.InstanceType(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "MachineType failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.MachineType = machineType
			instance.InstanceType = instanceType
			instance.BBCOption = bbcOption

			// VPC 相关配置
			wg2.Add(1)
			go func(instance *ccetypes.InstanceSpec) {
				defer wg2.Done()
				vpcConfig, err := filler.VPC(ctx, clusterSpec, instance)
				if err != nil {
					log.Errorf(ctx, "invalid VPCConfig: %v", err)
					filterErrs = append(filterErrs, err)
					return
				}
				instance.VPCConfig = vpcConfig
			}(instance)

			// 使用 ClusterSpec 尽可能补全字段
			if instance.VPCConfig.VPCID == "" {
				instance.VPCConfig.VPCID = clusterSpec.VPCID
			}

			if instance.VPCConfig.VPCUUID == "" {
				instance.VPCConfig.VPCUUID = clusterSpec.VPCUUID
			}

			// 机器规格配置
			// TODO 待兼容openapi传入参数中没有instanceResource.cpu和mem字段
			instanceResource, err := filler.InstanceResource(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "InstanceResource failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}

			if instanceResource.RootDiskType == "" {
				// 默认使用 RootDiskTypeSSD
				instanceResource.RootDiskType = bcc.StorageTypeHP1
			}

			instance.InstanceResource = instanceResource

			// 机器 Image 配置
			wg2.Add(1)
			go func(instance *ccetypes.InstanceSpec) {
				defer wg2.Done()
				imageID, imageUUID, instanceOS, err := filler.Image(ctx, clusterSpec.DeepCopy(), instance.DeepCopy())
				if err != nil {
					log.Errorf(ctx, "Image failed: %v", err)
					filterErrs = append(filterErrs, err)
					return
				}
				instance.ImageID = imageID
				instance.ImageUUID = imageUUID
				instance.InstanceOS = instanceOS
			}(instance)

			// EIP 配置
			needEIP, eipOption, err := filler.EIP(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "EIP failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.NeedEIP = needEIP
			instance.EIPOption = eipOption

			// AdminPassword & SSHKeyName
			adminPassword, sshKeyID, err := filler.AdminPasswd(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "Image failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.AdminPassword = adminPassword
			instance.SSHKeyID = sshKeyID

			// 付费信息配置
			chargingType, preChargingOption, err := filler.ChargingType(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "ChargingType failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.InstanceChargingType = chargingType
			instance.InstancePreChargingOption = preChargingOption

			// DeleteOption
			deleteOption, err := filler.DeleteOption(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "DeleteOption failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.DeleteOption = &deleteOption

			// DeployCustomConfig 校验
			_, err = filler.DeployCustomConfig(ctx, clusterSpec, instance)
			if err != nil {
				logger.Errorf(ctx, "DeployCustomConfig failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}

			// TODO 设置DeployCustomConfig中默认值应该在校验之前
			instance.DeployCustomConfig.KubeReserved = k8s.GenKubeletReserved(ctx,
				instance.CPU, instance.MEM, instance.DeployCustomConfig.KubeReserved)
			instance.DeployCustomConfig.SystemReserved = k8s.GenKubeletReserved(ctx,
				instance.CPU, instance.MEM, instance.DeployCustomConfig.SystemReserved)

			// Tags
			tagList, err := filler.Tags(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "Image failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.Tags = tagList

			// Labels
			labels, err := filler.Labels(ctx, clusterSpec, instance)
			if err != nil {
				log.Errorf(ctx, "Labels failed: %v", err)
				filterErrs = append(filterErrs, err)
				return
			}
			instance.Labels = labels

			wg2.Wait()
			tmpl := instanceSpecToInstanceSpecTmpl(instance)
			// TODO 改成调用 userscript.CreateUserScripts
			if tmpl.InstanceSpec.DeployCustomConfig.PreUserScript != "" {
				if utils.CheckStringGreaterThanMaxSize(tmpl.InstanceSpec.DeployCustomConfig.PreUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					logger.Errorf(ctx, "Check user script size: %d greater than max size: %d ", len(tmpl.InstanceSpec.DeployCustomConfig.PreUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
					filterErrs = append(filterErrs, fmt.Errorf("脚本数据不能超过16KB"))
				}

				userScriptID, err := userScriptService.Create(ctx, tmpl.InstanceSpec.AccountID, tmpl.InstanceSpec.UserID, tmpl.InstanceSpec.DeployCustomConfig.PreUserScript)
				if err != nil {
					logger.Errorf(ctx, "Create user script failed: %v", err)
					filterErrs = append(filterErrs, err)
				}

				tmpl.InstanceSpec.DeployCustomConfig.PreUserScript = userScriptID
			}

			if tmpl.InstanceSpec.DeployCustomConfig.PostUserScript != "" {
				if utils.CheckStringGreaterThanMaxSize(tmpl.InstanceSpec.DeployCustomConfig.PostUserScript, int(math.Ceil(maxUserScriptSize/3))*4) {
					logger.Errorf(ctx, "Check user script size: %d greater than max size: %d ", len(tmpl.InstanceSpec.DeployCustomConfig.PostUserScript), int(math.Ceil(maxUserScriptSize/3))*4)
					filterErrs = append(filterErrs, fmt.Errorf("脚本数据不能超过16KB"))
				}

				userScriptID, err := userScriptService.Create(ctx, tmpl.InstanceSpec.AccountID, tmpl.InstanceSpec.UserID, tmpl.InstanceSpec.DeployCustomConfig.PostUserScript)
				if err != nil {
					logger.Errorf(ctx, "Create user script failed: %v", err)
					filterErrs = append(filterErrs, err)
				}
				tmpl.InstanceSpec.DeployCustomConfig.PostUserScript = userScriptID
			}

			// TODO 使用ccetypes.UpdateSpecTemplatesAndResetTemplate方法处理，减少重复代码
			if instanceTmplCount > 0 {
				// 新数据结构，多子网多可用区
				instanceGroupSpec.InstanceTemplates[i] = *tmpl
				if instanceGroupSpec.InstanceTemplate.CCEInstancePriority == 0 {
					instanceGroupSpec.InstanceTemplate.CCEInstancePriority = instancegroup.DefaultInstancePriority
				}
			} else {
				// 兼容旧数据结构
				instanceGroupSpec.InstanceTemplate = *tmpl
				//所有新建节点的priority都赋予instancegroup.DefaultInstancePriority默认值
				instanceGroupSpec.InstanceTemplate.CCEInstancePriority = instancegroup.DefaultInstancePriority
			}
		}(i)
	}

	wg.Wait()
	if len(filterErrs) != 0 {
		return fmt.Errorf("prepare err: %v", filterErrs)
	}

	//TODO InstanceGroupName的校验：最大长度、非法字符等
	if instanceGroupSpec.InstanceGroupName == "" {
		return fmt.Errorf("empty instanceGroup name")
	}
	if instanceGroupSpec.Replicas < 0 {
		return fmt.Errorf("negative replicas")
	}
	if err := validateClusterAutoscalerSpec(instanceGroupSpec.ClusterAutoscalerSpec); err != nil {
		return err
	}
	if instanceGroupSpec.Handler == "" {
		instanceGroupSpec.Handler = s.handler
	}
	instanceGroupSpec.ClusterID = clusterSpec.ClusterID
	instanceGroupSpec.AccountID = clusterSpec.AccountID
	instanceGroupSpec.UserID = clusterSpec.UserID
	if instanceGroupSpec.ShrinkPolicy == "" {
		instanceGroupSpec.ShrinkPolicy = ccetypes.DefaultShrinkPolicy
	}
	if instanceGroupSpec.UpdatePolicy == "" {
		instanceGroupSpec.UpdatePolicy = ccetypes.DefaultUpdatePolicy
	}
	if instanceGroupSpec.CleanPolicy == "" {
		instanceGroupSpec.CleanPolicy = ccetypes.DefaultCleanPolicy
	}

	return nil
}

func (s *InstanceGroupService) verifyAndUpdateInstanceGroupSpec(ctx context.Context, ig *ccev1.InstanceGroup, newSpec *ccetypes.InstanceGroupSpec) (*ccetypes.InstanceGroupSpec, error) {
	log := s.log.WithValues("method", "instanceGroupService.verifyAndUpdateInstanceGroupSpec")
	var templates []ccetypes.InstanceTemplate
	if len(newSpec.InstanceTemplates) > 0 {
		templates = newSpec.InstanceTemplates
	} else {
		templates = []ccetypes.InstanceTemplate{
			newSpec.InstanceTemplate,
		}
	}
	var oldTemplate ccetypes.InstanceTemplate
	if len(ig.Spec.InstanceTemplates) > 0 {
		oldTemplate = ig.Spec.InstanceTemplates[0]
	} else {
		oldTemplate = ig.Spec.InstanceTemplate
	}
	cluster, err := s.model.GetCluster(ctx, newSpec.ClusterID, newSpec.AccountID)
	if err != nil {
		log.Errorf(ctx, "failed to get cluster, err: %v", err)
		return nil, err
	}

	for i := 0; i < len(templates); i++ {
		// taint校验
		if err = taints.CheckTaintsValidation(templates[i].Taints); err != nil {
			log.Errorf(ctx, "invalid taints: %v", templates[i].Taints)
			return nil, models.ErrTaintsInvalid.New(ctx, err.Error())
		}

		// 机器类型
		if oldTemplate.MachineType != templates[i].MachineType {
			log.Errorf(ctx, "machineType can not change: %s", newSpec.InstanceTemplate.MachineType)
			return nil, fmt.Errorf("changing machineType is not allowed")
		}
	}

	newSpec.ClusterRole = ig.Spec.ClusterRole
	// TODO 使用ccetypes.UpdateSpecTemplatesAndResetTemplate方法处理，减少重复代码
	log.Infof(ctx, "ig:%v oldTemplate.RuntimeType:%v, oldTemplate.RuntimeVersion:%v",
		ig.Name, oldTemplate.RuntimeType, oldTemplate.RuntimeVersion)
	log.Infof(ctx, "ig:%v new.RuntimeType:%v, new.RuntimeVersion:%v",
		ig.Name, newSpec.InstanceTemplate.RuntimeType, newSpec.InstanceTemplate.RuntimeVersion)
	for i, tpl := range newSpec.InstanceTemplates {
		log.Infof(ctx, "ig:%v, i:%v new.RuntimeType:%v, new.RuntimeVersion:%v",
			ig.Name, i, tpl.RuntimeType, tpl.RuntimeVersion)
	}
	if oldTemplate.RuntimeType != "" && oldTemplate.RuntimeVersion != "" {
		for i := range newSpec.InstanceTemplates {
			if newSpec.InstanceTemplates[i].RuntimeType == "" || newSpec.InstanceTemplates[i].RuntimeVersion == "" {
				newSpec.InstanceTemplates[i].RuntimeType = oldTemplate.RuntimeType
				newSpec.InstanceTemplates[i].RuntimeVersion = oldTemplate.RuntimeVersion
			}
		}
		if len(newSpec.InstanceTemplates) == 0 {
			if newSpec.InstanceTemplate.RuntimeType == "" || newSpec.InstanceTemplate.RuntimeVersion == "" {
				newSpec.InstanceTemplate.RuntimeType = oldTemplate.RuntimeType
				newSpec.InstanceTemplate.RuntimeVersion = oldTemplate.RuntimeVersion
			}
		}
	}
	// 存在的风险就是之前存在的配置现在不存在，校验会报错
	if err := s.VerifyAndPadDefault(ctx, cluster.Spec, newSpec, "update"); err != nil {
		log.Errorf(ctx, "failed to verify InstanceGroup spec, err: %v", err)
		return nil, err
	}

	// 根据新配置更新instancegroup
	newSpec.Handler = ig.Spec.Handler
	// label selector
	newSpec.Selector = ig.Spec.Selector
	// TODO 使用ccetypes.UpdateSpecTemplatesAndResetTemplate方法处理，减少重复代码
	if len(newSpec.InstanceTemplates) > 0 {
		for i := 0; i < len(newSpec.InstanceTemplates); i++ {
			// 默认label
			newSpec.InstanceTemplates[i].Labels[ccetypes.ClusterIDLabelKey] = ig.Spec.ClusterID
			newSpec.InstanceTemplates[i].Labels[ccetypes.InstanceGroupIDLabelKey] = ig.Spec.CCEInstanceGroupID
			newSpec.InstanceTemplates[i].Labels[ccetypes.ClusterRoleLabelKey] = string(ig.Spec.ClusterRole)

			if newSpec.ClusterAutoscalerSpec != nil && newSpec.ClusterAutoscalerSpec.Enabled {
				newSpec.InstanceTemplates[i].Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
			} else {
				delete(newSpec.InstanceTemplates[i].Labels, ccetypes.ClusterAutoscalerEnabledLabelKey)
			}
			newSpec.InstanceTemplates[i].InstanceGroupName = newSpec.InstanceGroupName
			newSpec.InstanceTemplates[i].InstanceGroupID = newSpec.CCEInstanceGroupID
			if i == 0 {
				newSpec.InstanceTemplate = newSpec.InstanceTemplates[0]
			}
		}
	} else {
		// 默认label
		newSpec.InstanceTemplate.Labels[ccetypes.ClusterIDLabelKey] = ig.Spec.ClusterID
		newSpec.InstanceTemplate.Labels[ccetypes.InstanceGroupIDLabelKey] = ig.Spec.CCEInstanceGroupID
		newSpec.InstanceTemplate.Labels[ccetypes.ClusterRoleLabelKey] = string(ig.Spec.ClusterRole)
		// label selector
		newSpec.Selector = ig.Spec.Selector
		if newSpec.ClusterAutoscalerSpec != nil && newSpec.ClusterAutoscalerSpec.Enabled {
			newSpec.InstanceTemplate.Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
		} else {
			delete(newSpec.InstanceTemplate.Labels, ccetypes.ClusterAutoscalerEnabledLabelKey)
		}
		newSpec.InstanceTemplate.InstanceGroupName = newSpec.InstanceGroupName
		newSpec.InstanceTemplate.InstanceGroupID = ig.Spec.InstanceTemplate.InstanceGroupID
	}
	if ig.Spec.InstancesToBeRemoved != nil {
		if newSpec.InstancesToBeRemoved == nil {
			newSpec.InstancesToBeRemoved = ig.Spec.InstancesToBeRemoved
		} else {
			for key, value := range ig.Spec.InstancesToBeRemoved {
				newSpec.InstancesToBeRemoved[key] = value
			}
		}
	}

	if ig.Spec.MachinesToJoin != nil {
		if newSpec.MachinesToJoin == nil {
			newSpec.MachinesToJoin = ig.Spec.MachinesToJoin
		} else {
			for key, value := range ig.Spec.MachinesToJoin {
				newSpec.MachinesToJoin[key] = value
			}
		}
	}

	if ig.Spec.RemedyRulesBinding != nil {
		if newSpec.RemedyRulesBinding == nil {
			newSpec.RemedyRulesBinding = ig.Spec.RemedyRulesBinding
		}
	}

	// 允许编辑节点组安全组
	if len(ig.Spec.DefaultSecurityGroups) != 0 && len(newSpec.DefaultSecurityGroups) == 0 {
		newSpec.DefaultSecurityGroups = ig.Spec.DefaultSecurityGroups
	}

	return newSpec, nil
}

func InstanceGroupSpecToModel(spec *ccetypes.InstanceGroupSpec) *models.InstanceGroup {
	ig := &models.InstanceGroup{
		BaseModel: models.BaseModel{},
		Spec:      spec,
		Status: &ccetypes.InstanceGroupStatus{
			ReadyReplicas:       0,
			UndeliveredMachines: ccetypes.UndeliveredMachines{},
			Pause:               &ccetypes.PauseDetail{},
		},
		Deleted: false,
	}
	return ig
}

func instanceSpecTmplToSpec(tmpl *ccetypes.InstanceTemplate) *ccetypes.InstanceSpec {
	spec := tmpl.InstanceSpec.DeepCopy()
	return spec
}

func instanceSpecToInstanceSpecTmpl(spec *ccetypes.InstanceSpec) *ccetypes.InstanceTemplate {
	tmpl := ccetypes.InstanceTemplate{
		InstanceSpec: *spec,
	}
	return &tmpl
}

func validateClusterAutoscalerSpec(spec *ccetypes.ClusterAutoscalerSpec) error {
	if spec == nil {
		return nil
	}

	if spec.Enabled {
		if spec.MinReplicas > spec.MaxReplicas {
			return models.ErrIgReplicasMaxLessThanMin.New(context.TODO(), "最大节点数应大于等于最小节点数")
		}

		if spec.ScalingGroupPriority < 0 {
			return fmt.Errorf("scaling group priority can not be nagative")
		}
	}

	return nil
}

func isReplicaUpN(upToReplicas int, upReplicas int) (bool, error) {
	switch {
	case upToReplicas == 0 && upReplicas > 0:
		return true, nil
	case upToReplicas > 0 && upReplicas == 0:
		return false, nil
	default:
		return false, fmt.Errorf("invalid task upToReplicas/upReplicas")
	}
}

// buildCleanPolicyAndDeleteOption 选择节点缩容时，构造移除策略，若用户未传入则选择节点组级别的移除策略
func buildCleanPolicyAndDeleteOption(ig *ccev1.InstanceGroup, opts ScaleDownOption) (ccetypes.CleanPolicy, *ccetypes.DeleteOption) {
	cleanPolicy := ig.Spec.CleanPolicy
	var deleteOption *ccetypes.DeleteOption
	// 用户有传入则使用用户传入的参数作为移除策略
	if opts.CleanPolicy == ccetypes.RemainCleanPolicy {
		cleanPolicy = ccetypes.RemainCleanPolicy
	} else if opts.CleanPolicy == ccetypes.DeleteCleanPolicy {
		cleanPolicy = ccetypes.DeleteCleanPolicy
		if opts.InstanceDeleteOption != nil {
			deleteOption = &ccetypes.DeleteOption{
				DrainNode:         opts.InstanceDeleteOption.DrainNode,
				MoveOut:           opts.InstanceDeleteOption.MoveOut,
				DeleteResource:    opts.InstanceDeleteOption.DeleteResource,
				DeleteCDSSnapshot: opts.InstanceDeleteOption.DeleteCDSSnapshot,
				Rebuild:           opts.InstanceDeleteOption.Rebuild,
			}
		}
	}
	return cleanPolicy, deleteOption
}

func GetInstanceGroupStatus(ctx context.Context, userk8sClient kubernetes.Interface, instanceGroupID string) (igStatus *ccesdk.InstanceGroupStatus, err error) {
	if userk8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return nil, fmt.Errorf("k8sClient not initialized")
	}
	selector := labels.SelectorFromSet(labels.Set(map[string]string{"instance-group-id": instanceGroupID})).String()
	options := metav1.ListOptions{
		LabelSelector:   selector,
		ResourceVersion: "0",
	}
	nodeList, err := userk8sClient.CoreV1().Nodes().List(ctx, options)
	if err != nil {
		logger.Warnf(ctx, "list k8s nodes failed: %v", err)
		return nil, err
	}

	status := instanceGroupStatus(ctx, nodeList)

	return status, nil
}

func instanceGroupStatus(ctx context.Context, nodes *corev1.NodeList) (igStatus *ccesdk.InstanceGroupStatus) {
	igStatus = &ccesdk.InstanceGroupStatus{}
	for i := 0; i < len(nodes.Items); i++ {
		InstancePhase := instance.K8sNodeStatus(ctx, &nodes.Items[i])
		logger.Infof(ctx, "InstancePhase is %v", string(InstancePhase))
		switch InstancePhase {
		case phaseReady:
			igStatus.ReadyReplicas++
		case InstancePhaseRunning:
			igStatus.ReadyReplicas++
		case phaseNotReady:
			igStatus.NotReadyReplicas++
		case phaseReadySchedulingDisabled:
			igStatus.ReadyReplicas++
		}
	}
	return igStatus
}

func (s *InstanceGroupService) checkCCENodeNumLimitAndReplicas(ctx context.Context, clusterID, accountID string, addNum int) error {

	check, err := instance.CheckCCENodeNumLimit(ctx, clusterID, accountID, addNum, s.config, s.model)
	if err != nil {
		logger.Errorf(ctx, "check cce cluster node num limit failed: %v", err)
		return fmt.Errorf("check cce cluster node num limit failed: %v", err)
	}
	if !check && err == nil {
		logger.Errorf(ctx, "Exceeding the number of nodes by the cluster flavor limits.")
		return fmt.Errorf("Exceeding the number of nodes by the cluster flavor limits.")
	}

	return nil
}
