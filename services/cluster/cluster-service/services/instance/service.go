package instance

import (
	"context"
	"errors"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"

	inmemCache "github.com/patrickmn/go-cache"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/client-go/kubernetes"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/app/app-service/resource/node"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/image/retriever"
)

const (
	VKNodeLabel = "type=virtual-kubelet"

	// 卡类型主要就这几种：NvidiaGPU、AscendResourceNamePrefix、KunkunResourceNameSuffix
	NvidiaGPU                = "nvidia.com/gpu"
	CGPUPrefix               = "baidu.com/"
	CGPUSuffix               = "_cgpu"
	CGPUMemorySuffix         = "_memory"
	AscendResourceNamePrefix = "huawei.com/Ascend" // huawei.com/Ascend910
	KunkunResourceNameSuffix = "xpu"

	NodeCacheKeyTemplate              = "%s-nodes"
	PodCacheKeyTemplate               = "%s-pods"
	ResourceCacheDefaultExpiration    = 120 * time.Second
	ResourceCacheDefaultPurgeInterval = 10 * time.Minute
)

// gpuDisplayNameMap resourceName 转换为前端展示名，与 cce-gpu-manager 中的 cce-gputype-configmap 对应
var gpuDisplayNameMap = map[string]string{
	"baidu.com/a800_80g_cgpu": "NVIDIA A800-SXM4-80GB",
	"baidu.com/a100_80g_cgpu": "NVIDIA A100-SXM4-80GB",
	"baidu.com/a100_40g_cgpu": "NVIDIA A100-SXM4-40GB",
	"baidu.com/a10_24g_cgpu":  "NVIDIA A10",
	"baidu.com/a30_24g_cgpu":  "NVIDIA A30",
	"baidu.com/v100_32g_cgpu": "Tesla V100-SXM2-32GB",
	"baidu.com/v100_16g_cgpu": "Tesla V100-SXM2-16GB",
	"baidu.com/t4_16g_cgpu":   "Tesla T4",
	"baidu.com/rtx_3090_cgpu": "NVIDIA GeForce RTX 3090",
	"baidu.com/rtx_3080_cgpu": "NVIDIA GeForce RTX 3080",
	"baidu.com/rtx_4090_cgpu": "NVIDIA GeForce RTX 4090",
	"baidu.com/h800_80g_cgpu": "NVIDIA H800",
	"baidu.com/cgpu":          "NVIDIA GPU",
	"nvidia.com/gpu":          "NVIDIA GPU",
	"baidu.com/l20_cgpu":      "L20",

	// Ascend 芯片
	"huawei.com/Ascend910": "Huawei Ascend910",
	"baidu.com/xpu":        "KUNLUNXIN-R480",
	"kunlunxin.com/xpu":    "KUNLUNXIN-P800",
}

var gpuTypeMap = map[string]string{
	"baidu.com/a800_80g_cgpu": "A800",
	"baidu.com/a100_80g_cgpu": "A100",
	"baidu.com/a100_40g_cgpu": "A100",
	"baidu.com/a10_24g_cgpu":  "A10",
	"baidu.com/a30_24g_cgpu":  "A30",
	"baidu.com/v100_32g_cgpu": "V100",
	"baidu.com/v100_16g_cgpu": "V100",
	"baidu.com/t4_16g_cgpu":   "T4",
	"baidu.com/rtx_3090_cgpu": "3090",
	"baidu.com/rtx_3080_cgpu": "3080",
	"baidu.com/rtx_4090_cgpu": "4090",
	"baidu.com/h800_80g_cgpu": "H800",
	"baidu.com/l20_cgpu":      "L20",

	// Ascend 芯片
	"huawei.com/Ascend910": "Ascend910",
	"baidu.com/xpu":        "R480",
	"kunlunxin.com/xpu":    "P800",
}

func getGpuDisplayNameMap() map[string]string {
	return gpuDisplayNameMap
}

func getGpuTypeMap() map[string]string {
	return gpuTypeMap
}

// service 是 instance.Interface 的实现
type service struct {
	accountID            string
	config               *configuration.Config
	clients              *clients.Clients
	models               models.Interface
	services             services.Interface
	k8sClient            kubernetes.Interface
	clusterResourceCache *inmemCache.Cache
	imageRetriever       retriever.Interface
}

// NewService 初始化 instance.service
func NewService(ctx context.Context, accountID string, config *configuration.Config, clients *clients.Clients,
	models models.Interface, services services.Interface, k8sClient kubernetes.Interface) (Interface, error) {
	if accountID == "" {
		return nil, fmt.Errorf("accountID is empty")
	}

	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	if clients == nil {
		return nil, fmt.Errorf("clients is nil")
	}

	if models == nil {
		return nil, fmt.Errorf("models is nil")
	}

	return &service{
		accountID: accountID,
		config:    config,
		clients:   clients,
		models:    models,
		services:  services,
		k8sClient: k8sClient,
	}, nil
}

func (s *service) WithResourceCache(resourceCache *inmemCache.Cache) Interface {
	s.clusterResourceCache = resourceCache
	return s
}

func (s *service) WithImageRetriever(imageRetriever retriever.Interface) Interface {
	s.imageRetriever = imageRetriever
	return s
}

// 处理节点分页参数
func handleInstancePageParams(pageParams ccesdk.PageParams, enableHostname bool) ccesdk.PageParams {
	if pageParams.OrderBy == "k8sNodeName" {
		if enableHostname {
			pageParams.OrderBy = "hostname"
		} else {
			pageParams.OrderBy = "vpcIP"
		}
	}

	// 默认按照 InstanceName 排序
	_, ok := models.InstanceFieldMap[string(pageParams.OrderBy)]
	if !ok {
		pageParams.OrderBy = ccesdk.InstanceOrderByInstanceName
	}

	// 自动改成大写，默认是升序
	pageParams.Order = ccesdk.Order(strings.ToUpper(pageParams.Order.String()))
	if pageParams.Order != ccesdk.OrderDESC {
		pageParams.Order = ccesdk.OrderASC
	}

	return pageParams
}

func (s *service) ListInstancesByInstancesDownloadRequest(ctx context.Context, req ccesdk.InstancesDownloadRequest, clusterID, accountID string) (*ccesdk.InstancePage, error) {
	if clusterID == "" || accountID == "" {
		return nil, fmt.Errorf("clusterID or accountID is empty")
	}
	if !req.ExportAll && len(req.CCEInstanceIDs) <= 0 {
		logger.Warnf(ctx, "req.ExportAll is false and len(req.CCEInstanceIDs) <= 0")
		// 这里兼容用户勾选条件没有选中实例的情况
		return nil, nil
	}

	cluster, err := s.models.GetCluster(ctx, clusterID, accountID)
	if err != nil {
		return nil, err
	}

	// 处理分页参数
	req.PageParams = handleInstancePageParams(req.PageParams, cluster.Spec.K8SCustomConfig.EnableHostname)
	if req.KeywordType == "k8sNodeName" {
		if cluster.Spec.K8SCustomConfig.EnableHostname {
			req.KeywordType = "hostname"
		} else {
			req.KeywordType = "vpcIP"
		}
	}

	// 默认按照 InstanceName 模糊查询
	keywordTypeDB, ok := models.InstanceFieldMap[req.KeywordType]
	if !ok {
		req.KeywordType = string(ccesdk.InstanceKeywordTypeInstanceName)
	}

	instanceOption := models.InstanceListOption{
		AccountID:       s.accountID,
		ClusterID:       cluster.Spec.ClusterID,
		ClusterRole:     ccetypes.ClusterRole(strings.TrimSpace(req.ClusterRole)),
		KeywordType:     keywordTypeDB,
		Keyword:         req.Keyword,
		OrderBy:         req.PageParams.OrderBy.String(),
		Order:           req.PageParams.Order.String(),
		PageNo:          req.PageParams.PageNo,
		PageSize:        req.PageParams.PageSize,
		BecRegion:       req.BecRegion,
		CCEInstanceIDs:  req.CCEInstanceIDs,
		InstanceGroupID: req.InstanceGroupID,
	}

	instancesDB, err := s.models.GetInstanceEx(ctx, instanceOption)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceEx failed: %v", err)
		return nil, err
	}

	return s.getInstancesResp(ctx, cluster, instancesDB, req)
}

// TODO 需要定义公共的 Request，列表和下载接口都可用
func (s *service) getInstancesResp(ctx context.Context, cluster *models.Cluster, instanceList *models.InstanceList, req ccesdk.InstancesDownloadRequest) (*ccesdk.InstancePage, error) {

	var (
		accountID         = s.accountID
		clusterID         = cluster.Spec.ClusterID
		targetInstanceIDs []string
		instances         = instanceList.Items
		isK8sNodeName     = req.KeywordType == "k8sNodeName"
	)
	for _, instance := range instanceList.Items {
		targetInstanceIDs = append(targetInstanceIDs, instance.Spec.CCEInstanceID)
	}

	// 获取crd和k8s node信息
	var wg sync.WaitGroup
	var mu sync.Mutex
	// nodeDetails key为cceInstanceID，value为k8s node详情
	nodeDetails := make(map[string]*node.NodeDetail)
	// nodePodsMap key为K8SNodeName
	nodePodsMap := make(map[string][]*corev1.Pod)

	k8sNodes := make(map[string]*corev1.Node)
	vkNodes := make(map[string]*corev1.Node)
	// 限制并发数量 默认限制100，防止大集群并发把service打挂
	maxConcurrency := 100
	semaphore := make(chan struct{}, maxConcurrency)
	for _, instanceID := range targetInstanceIDs {
		wg.Add(1)
		go func(instanceID string) {
			defer func() {
				// 释放信号量
				<-semaphore
				wg.Done()
			}()

			// 获取信号量
			semaphore <- struct{}{}

			// 兼容大规格集群，这里增加重试
			instanceCRD, err := s.clients.MetaK8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "get instance CRD err: %v", err)
				for i := 0; i < 2; i++ {
					instanceCRD, err = s.clients.MetaK8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, instanceID, &metav1.GetOptions{})
					if err != nil {
						logger.Errorf(ctx, "get instance CRD err: %v", err)
					} else {
						break
					}
				}
				if instanceCRD == nil || err != nil {
					logger.Errorf(ctx, "failed to get instance CRD with retry 3 number, err: %v", err)
					return
				}
			}
			// 获取 NodeName
			nodeName := utils.GetNodeName(ctx, instanceCRD.Status.Machine.VPCIP, instanceCRD.Status.Machine.Hostname, instanceCRD.Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)

			// 通过 APPService 获取 NodeDetail
			nodeDetail, err := s.clients.APPServiceClient.GetNodeByName(ctx, clusterID, nodeName, false, s.clients.STSClient.NewSignOption(ctx, accountID))
			if err != nil {
				logger.Errorf(ctx, "APPServiceClient.GetNodeByName failed: %s", err)
				// 失败直接返回，不保存当前值
				return
			}

			mu.Lock()
			nodeDetails[instanceID] = nodeDetail
			mu.Unlock()

		}(instanceID)
	}

	// 导出集群所有节点列表时，才需要展示 vk node
	clusterRole := ccetypes.ClusterRole(strings.TrimSpace(req.ClusterRole))
	showVkNode := len(req.CCEInstanceIDs) == 0 && len(req.InstanceGroupID) == 0 && (clusterRole == ccetypes.ClusterRoleNode || clusterRole == "")
	wg.Add(1)
	go func() {
		defer wg.Done()

		logger.Infof(ctx, "Get K8SNodes and vkNodes begin!")

		var err error
		k8sNodes, err = s.getK8SNodes(ctx, clusterID)
		if err != nil {
			logger.Errorf(ctx, "getK8SNodes failed: %v", err)
			return
		}
		if showVkNode {
			vkNodes, err = s.getVKNodes(ctx, k8sNodes)
			if err != nil {
				logger.Errorf(ctx, "getVKNodes failed: %v", err)
				return
			}
		}
		logger.Infof(ctx, "Get K8SNodes and VKNodes finished")
	}()

	if req.CalculateGPUCountRequested {
		wg.Add(1)
		go func() {
			defer wg.Done()
			logger.Infof(ctx, "Get node pods begin!")
			var err error
			nodePodsMap, err = s.getK8SPods(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "getK8SNodes failed: %v", err)
				return
			}
			logger.Infof(ctx, "Get node pods finished")
		}()
	}

	// 设置 Phase 则对 InstancePhases 过滤
	phasesMap, err := parseSDKInstancePhases(ctx, req.Phases)
	if err != nil {
		logger.Errorf(ctx, "parseSDKInstancePhases failed: %v", err)
		return nil, err
	}

	wg.Wait()
	// 关闭通道
	close(semaphore)

	// 构建返回结果
	resultInstanceList := make([]*ccesdk.Instance, 0)

	// 如果有 vk nodes，则先构建 vk instanceDSK，展示在最前面，方便用户查看
	// list 中需要展示：节点名称 K8SNodeName、状态 InstancePhase、IP 地址
	if len(vkNodes) > 0 && req.GPUType == "" {
		vkNodesRes := s.filterVKNodes(ctx, vkNodes, phasesMap, isK8sNodeName, req.KeywordType, req.Keyword, clusterID)
		resultInstanceList = append(resultInstanceList, vkNodesRes...)
	}

	logger.Infof(ctx, "TRACING: Finished FilterVKNodes - %s", time.Now().Format(time.RFC3339Nano))

	instancesSlice, err := s.generateInstancesSDKs(ctx, cluster, instances, nil, phasesMap, k8sNodes, nodePodsMap,
		false, false, false, false, req.GPUType, nil)
	if err != nil {
		logger.Errorf(ctx, "generateInstancesSDKs failed: %v", err)
		return nil, err
	}

	resultInstanceList = append(resultInstanceList, instancesSlice...)
	for _, temp := range resultInstanceList {
		k8sNode, ok := nodeDetails[temp.Spec.CCEInstanceID]
		if !ok {
			// TODO 目前没获取到K8sNode 打印错误日志，但是导出支持该节点部分信息不足。
			logger.Warnf(ctx, "k8s node not found for instanceID: %s", temp.Spec.CCEInstanceID)
		}
		temp.K8SNode = k8sNode
	}

	// 排序
	if req.OrderBy.String() == "gpuCountRemaining" {
		sort.Slice(resultInstanceList, func(i, j int) bool {
			// 空闲卡相同情况下，默认按照总卡数升序排序
			if resultInstanceList[i].Status.Resources.GPUCountRemaining == resultInstanceList[j].Status.Resources.GPUCountRemaining {
				return resultInstanceList[i].Spec.InstanceResource.GPUCount < resultInstanceList[j].Spec.InstanceResource.GPUCount
			}

			if req.Order == ccesdk.OrderDESC {
				logger.Infof(ctx, "order by gpuCountRemaining desc")
				return resultInstanceList[i].Status.Resources.GPUCountRemaining > resultInstanceList[j].Status.Resources.GPUCountRemaining
			} else {
				logger.Infof(ctx, "order by gpuCountRemaining asc")
				return resultInstanceList[i].Status.Resources.GPUCountRemaining < resultInstanceList[j].Status.Resources.GPUCountRemaining
			}
		})
	}

	return &ccesdk.InstancePage{
		InstanceList: resultInstanceList,
		TotalCount:   instanceList.TotalCount,
		PageParams:   req.PageParams,
	}, nil
}

func (s *service) ListInstancesByPage(ctx context.Context, clusterID, keywordType, keyword, orderBy, order, phases string,
	pageNo, pageSize int, enableInternalFields bool, clusterRole ccetypes.ClusterRole,
	becRegion string, enableUpgradeNodeFields bool, ipList []string, isK8sNodeName bool, gpuType string, calculateGPUCountRequested bool, chargingType *string) (*ccesdk.InstancePage, error) {
	// 参数校验
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	logger.Infof(ctx, "TRACING: Start ListInstancesByPage - %s", time.Now().Format(time.RFC3339Nano))

	// 默认按照 InstanceName 模糊查询
	keywordTypeDB, ok := models.InstanceFieldMap[keywordType]
	if !ok {
		keywordType = string(ccesdk.InstanceKeywordTypeInstanceName)
		keywordTypeDB = models.InstanceKeywordTypeDefault
		keyword = ""
	}

	// 默认按照 InstanceName 排序
	orderByRaw := orderBy
	orderByDB, ok := models.InstanceFieldMap[orderBy]
	if !ok {
		orderBy = string(ccesdk.InstanceKeywordTypeInstanceName)
		orderByDB = models.InstanceOrderByDefault
	}

	// 默认是升序
	if !strings.EqualFold(order, string(ccesdk.OrderDESC)) {
		order = string(ccesdk.OrderASC)
	}

	// 默认返回第一页
	if pageNo < 1 {
		pageNo = ccesdk.PageNoDefault
	}

	// 默认每页 10 个节点
	if pageSize < 1 {
		pageSize = ccesdk.PageSizeDefault
	}

	// 支持可升级 Node 列表按 IPList 查询, 构建 IPSet
	ipSet := map[string]interface{}{}
	if len(ipList) != 0 {
		for _, ip := range ipList {
			ipSet[ip] = nil
		}
	}

	logger.Infof(ctx, "TRACING: Finished Param Defaulter - %s", time.Now().Format(time.RFC3339Nano))

	// 查询集群
	cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "TRACING: Finished GetCluster - %s", time.Now().Format(time.RFC3339Nano))

	// 异步查询 K8s Nodes 状态
	var wg sync.WaitGroup
	var k8sNodes map[string]*corev1.Node
	var vkNodes map[string]*corev1.Node
	var nodePodsMap map[string][]*corev1.Pod
	if calculateGPUCountRequested {
		wg.Add(1)
		go func() {
			defer wg.Done()

			logger.Infof(ctx, "Get node pods begin!")

			nodePodsMap, err = s.getK8SPods(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "getK8SNodes failed: %v", err)
			}

			logger.Infof(ctx, "Get node pods finished")
		}()
	}
	if needGetK8SNodes(ctx, cluster.Status.ClusterPhase, cluster.Spec.ClusterType, phases) {
		wg.Add(1)
		go func() {
			defer wg.Done()

			logger.Infof(ctx, "Get K8SNodes and vkNodes begin!")

			k8sNodes, err = s.getK8SNodes(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "getK8SNodes failed: %v", err)
			}

			vkNodes, err = s.getVKNodes(ctx, k8sNodes)
			if err != nil {
				logger.Errorf(ctx, "getVKNodes failed: %v", err)
			}

			logger.Infof(ctx, "Get K8SNodes and VKNodes finished")
		}()
	}

	// 异步查询 ClusterCRD
	//  ClusterCRD 中设置 label kubernetes.io/cce.cluster.nodes-can-be-upgraded, 则所有 Node 可被升级
	allNodesCanBeUpgraded := false
	if enableUpgradeNodeFields == true {
		wg.Add(1)
		go func() {
			defer wg.Done()

			clusterCRD, err := s.clients.MetaK8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID,
				&metav1.GetOptions{ResourceVersion: "0"})
			if err != nil {
				logger.Errorf(ctx, "GetCluster failed: %s", err)
				return
			}

			if clusterCRD != nil && clusterCRD.Labels != nil {
				if _, ok := clusterCRD.Labels[ccev1.LabelClusterNodesCanBeUpgraded]; ok {
					logger.Infof(ctx, "Cluster set label %s", ccev1.LabelClusterNodesCanBeUpgraded)
					allNodesCanBeUpgraded = true
				}
			}
		}()
	}

	// 托管 和 Serverless 集群, 不返回 Master 节点信息
	clusterRole, empty, err := checkListInstanceClusterRole(ctx, cluster, clusterRole)
	if err != nil {
		logger.Errorf(ctx, "checkClusterRole failed: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "TRACING: Finished CheckListInstanceClusterRole - %s", time.Now().Format(time.RFC3339Nano))

	if empty {
		logger.Infof(ctx, "checkListInstanceClusterRole return empty=true")

		return &ccesdk.InstancePage{
			ClusterID:   clusterID,
			KeywordType: ccesdk.InstanceKeywordType(keywordType),
			Keyword:     keyword,
			PageParams: ccesdk.PageParams{
				OrderBy:  ccesdk.InstanceOrderBy(orderBy),
				Order:    ccesdk.Order(order),
				PageNo:   pageNo,
				PageSize: pageSize,
			},
			Phases:       phases,
			TotalCount:   0,
			InstanceList: []*ccesdk.Instance{},
		}, nil
	}

	// 查询 DB 节点列表 - 使用GetInstanceEx方法支持计费方式筛选
	option := models.InstanceListOption{
		AccountID:    s.accountID,
		ClusterID:    clusterID,
		KeywordType:  keywordTypeDB,
		Keyword:      keyword,
		OrderBy:      orderByDB,
		Order:        order,
		PageSize:     100000,
		PageNo:       1,
		ClusterRole:  clusterRole,
		BecRegion:    becRegion,
		ChargingType: chargingType, // 新增计费方式筛选
	}

	instanceListDB, err := s.models.GetInstanceEx(ctx, option)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceEx failed: %v", err)
		return nil, err
	}
	instancesDB := instanceListDB.Items

	logger.Infof(ctx, "TRACING: Finished LoadInstances From DB - %s", time.Now().Format(time.RFC3339Nano))

	// 设置 Phase 则对 InstancePhases 过滤
	phasesMap, err := parseSDKInstancePhases(ctx, phases)
	if err != nil {
		logger.Errorf(ctx, "parseSDKInstancePhases failed: %v", err)
		return nil, err
	}
	instanceCrdMap := make(map[string]*ccev1.Instance)
	wg.Add(1)
	go func() {
		defer wg.Done()
		instanceCrdList, err := s.clients.MetaK8SClient.ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
			LabelSelector: fmt.Sprintf("%s=%s", ccetypes.ClusterIDLabelKey, clusterID)})
		if err != nil {
			logger.WithValues("cluster", clusterID).Errorf(ctx, "failed to list instance belong to cluster, err: %v", err)
			return
		}
		for _, item := range instanceCrdList.Items {
			node := item
			instanceCrdMap[node.Status.Machine.VPCIP] = &node
		}
	}()

	// 等待 Get K8S Nodes 返回
	wg.Wait()

	logger.Infof(ctx, "TRACING: Finished GetNodes From K8S - %s", time.Now().Format(time.RFC3339Nano))

	// 构建返回结果
	instancesSDK := make([]*ccesdk.Instance, 0)

	// 如果有 vk nodes，则先构建 vk instanceDSK，展示在最前面，方便用户查看
	// list 中需要展示：节点名称 K8SNodeName、状态 InstancePhase、IP 地址
	// 按 gpuType 筛选时，不展示 vk node
	if len(vkNodes) > 0 && (clusterRole == ccetypes.ClusterRoleNode || clusterRole == "") && !enableUpgradeNodeFields && gpuType == "" {
		vkNodesRes := s.filterVKNodes(ctx, vkNodes, phasesMap, isK8sNodeName, keywordType, keyword, clusterID)
		instancesSDK = append(instancesSDK, vkNodesRes...)
	}

	logger.Infof(ctx, "TRACING: Finished FilterVKNodes - %s", time.Now().Format(time.RFC3339Nano))

	instancesSlice, err := s.generateInstancesSDKs(ctx, cluster, instancesDB, ipSet, phasesMap, k8sNodes, nodePodsMap,
		enableInternalFields, enableUpgradeNodeFields, allNodesCanBeUpgraded, false, gpuType, instanceCrdMap)
	if err != nil {
		logger.Errorf(ctx, "generateInstancesSDKs failed: %v", err)
		return nil, err
	}

	instancesSDK = append(instancesSDK, instancesSlice...)

	if orderByRaw == "gpuCountRemaining" {
		sort.Slice(instancesSDK, func(i, j int) bool {
			// 空闲卡相同情况下，默认按照总卡数升序排序
			if instancesSDK[i].Status.Resources.GPUCountRemaining == instancesSDK[j].Status.Resources.GPUCountRemaining {
				return instancesSDK[i].Spec.InstanceResource.GPUCount < instancesSDK[j].Spec.InstanceResource.GPUCount
			}

			if order == "desc" {
				logger.Infof(ctx, "order by gpuCountRemaining desc")
				return instancesSDK[i].Status.Resources.GPUCountRemaining > instancesSDK[j].Status.Resources.GPUCountRemaining
			} else {
				logger.Infof(ctx, "order by gpuCountRemaining asc")
				return instancesSDK[i].Status.Resources.GPUCountRemaining < instancesSDK[j].Status.Resources.GPUCountRemaining
			}
		})
	}

	// 开启 enableUpgradeNodeFields, 优先展示 CanBeSelected = true
	if enableUpgradeNodeFields == true {
		instancesSDK = sortInstancesByCanBeSelected(ctx, instancesSDK)
	}

	// 分页
	total := len(instancesSDK)
	start := (pageNo - 1) * pageSize
	end := int(math.Min(float64(pageNo*pageSize), float64(total)))

	instanceList := make([]*ccesdk.Instance, 0)
	if start < end {
		instanceList = instancesSDK[start:end]
	}

	logger.Infof(ctx, "TRACING: Finished Paging - %s", time.Now().Format(time.RFC3339Nano))

	return &ccesdk.InstancePage{
		ClusterID:   clusterID,
		KeywordType: ccesdk.InstanceKeywordType(keywordType),
		Keyword:     keyword,
		PageParams: ccesdk.PageParams{
			PageNo:   pageNo,
			PageSize: pageSize,
			OrderBy:  ccesdk.InstanceOrderBy(orderBy),
			Order:    ccesdk.Order(order),
		},
		Phases:       phases,
		TotalCount:   total,
		InstanceList: instanceList,
	}, nil
}

func (s *service) generateInstancesSDKs(ctx context.Context,
	cluster *models.Cluster,
	instanceDBSlice []*models.Instance, ipSet map[string]interface{},
	phasesMap map[ccetypes.InstancePhase]bool,
	k8sNodes map[string]*corev1.Node,
	nodePodsMap map[string][]*corev1.Pod,
	enableInternalFields, enableUpgradeNodeFields, allNodesCanBeUpgraded, enableInstanceGroupComponent bool,
	gpuType string, instanceCrdMap map[string]*ccev1.Instance,
) ([]*ccesdk.Instance, error) {

	instancesSDKMap := make(map[string]*ccesdk.Instance)
	instanceSDKChan := make(chan *ccesdk.Instance)
	instanceSDKDoneChan := make(chan struct{})

	errs := make([]error, 0)
	errChan := make(chan error)
	errDoneChan := make(chan struct{})

	var wg sync.WaitGroup
	for _, instanceDB := range instanceDBSlice {
		k8sNodeIP := instanceDB.Status.Machine.VPCIP

		// 按 IPList 过滤
		if len(ipSet) != 0 {
			if _, ok := ipSet[k8sNodeIP]; !ok {
				logger.Infof(ctx, "Node %s not in ipList, skip", k8sNodeIP)
				continue
			}
		}

		wg.Add(1)
		// 并发处理每个 Instance
		go func(instanceDB *models.Instance, instanceSDKChan chan<- *ccesdk.Instance, errChan chan<- error) {
			defer func() {
				wg.Done()
			}()
			// 转成对外返回格式
			instanceSDK, err := InstanceModelToSDK(ctx, cluster, instanceDB, enableInternalFields, enableUpgradeNodeFields)
			if err != nil {
				logger.Errorf(ctx, "copy instance %s failed: %v", instanceDB.Spec.CCEInstanceID, err)
				errChan <- err
				return
			}

			// 缩容保护字段复制
			if instanceCrdMap != nil {
				instanceCrd := instanceCrdMap[instanceDB.Status.Machine.VPCIP]
				scaleDown := false
				if instanceCrd != nil {
					scaleDown = instanceCrd.Spec.ScaleDownDisabled
					instanceSDK.Spec.ScaleDownDisabled = &scaleDown
				} else {
					instanceSDK.Spec.ScaleDownDisabled = &scaleDown
				}
			}

			masterType := cluster.Spec.MasterConfig.MasterType
			// 如果 K8S Node 存在, 则 InstancePhase 展示 K8SNode 状态
			if len(k8sNodes) > 0 &&
				(instanceSDK.Spec.ClusterRole == ccetypes.ClusterRoleNode ||
					(instanceSDK.Spec.ClusterRole == ccetypes.ClusterRoleMaster && masterType == ccetypes.MasterTypeContainerizedCustom)) &&
				instanceSDK.Status.InstancePhase == ccetypes.InstancePhaseRunning {

				instanceSDK.Status.InstancePhase = instancePhaseByK8SNodeStatus(ctx, k8sNodeIP, k8sNodes)
				node, found := k8sNodes[k8sNodeIP]
				if !found {
					logger.Warnf(ctx, "instance exists in meta cluster, but node is not found in k8s cluster, instance: %s", instanceSDK.Spec.CCEInstanceID)
				} else {
					instanceSDK.Spec.Labels = node.Labels
					instanceSDK.Spec.Annotations = node.Annotations
					instanceSDK.Spec.Taints = node.Spec.Taints
					if instanceSDK.Spec.InstanceResource.GPUType != "" && len(nodePodsMap) > 0 {
						gpuCountRequested, err := s.calculateNodeAllocatedGPUResources(ctx, nodePodsMap[instanceSDK.Status.Machine.K8SNodeName])
						if err != nil {
							logger.Errorf(ctx, "calculateNodeAllocatedGPUResources failed: %v", err)
						}
						instanceSDK.Status.Resources.GPUCountRequested = int(gpuCountRequested)
						instanceSDK.Status.Resources.GPUCountRemaining = instanceSDK.Spec.InstanceResource.GPUCount - int(gpuCountRequested)
					}
				}
			}

			//如果是二进制部署，通过机器状态反馈instancePhase
			if masterType == ccetypes.MasterTypeCustom {
				switch instanceDB.Status.Machine.MachineStatus {
				case logicbcc.ServerStatusError, logicbcc.ServerStatusStopped, logicbcc.ServerStatusReboot:
					instanceSDK.Status.InstancePhase = phaseNotReady
				}
			}

			// 增加 UpgradeNodeField 信息
			if enableUpgradeNodeFields == true {
				upgradeNodeFields, err := getUpgradeNodeFields(ctx, cluster, k8sNodeIP, k8sNodes)
				if err != nil {
					logger.Errorf(ctx, "getUpgradeNodeFields failed: %v", err)
					errChan <- err
					return
				}

				// 所有 Node 都可以升级
				if allNodesCanBeUpgraded == true {
					upgradeNodeFields.CanBeSelected = true
				}

				instanceSDK.UpgradeNodeFields = upgradeNodeFields
			}

			// 适配节点组滚动更新逻辑
			if len(k8sNodes) > 0 && len(instanceCrdMap) > 0 && enableInstanceGroupComponent {
				node, found := k8sNodes[k8sNodeIP]
				if found {
					if instanceSDK.UpgradeNodeFields == nil {
						instanceSDK.UpgradeNodeFields = &ccesdk.UpgradeNodeFields{}
					}
					instanceCrd := instanceCrdMap[instanceDB.Status.Machine.VPCIP]
					// 获取toolkit版本
					if instanceSDK.Spec.InstanceType == bcc.InstanceTypeG1 || instanceSDK.Spec.InstanceType == bcc.InstanceTypeBBCGPU ||
						instanceSDK.Spec.InstanceType == bcc.InstanceTypeHPAS || instanceCrd.Spec.NeedGPU {

						// oos有限额，不通过oos去获取版本。直接返回instanceCRD版本 或者 默认安装版本
						version := instanceCrd.Spec.NvidiaContainerToolkitVersion
						if version == "" {
							version = utils.GetNvidiaToolkitVersion(instanceCrd, cluster.Spec.K8SVersion)
						}
						instanceSDK.UpgradeNodeFields.NvidiaContainerToolkitVersion = version
					}

					// 获取XPU Container Toolkit版本 - 针对昆仑芯节点（InstanceType=25）
					if instanceSDK.Spec.InstanceType == bcc.InstanceTypeKunlun {
						xpuVersion := instanceCrd.Spec.XPUContainerToolkitVersion
						if xpuVersion == "" {
							// 根据节点创建时间返回默认值
							xpuVersion = getDefaultXPUVersionByCreateTime(instanceCrd.CreationTimestamp)
						}
						instanceSDK.UpgradeNodeFields.XPUContainerToolkitVersion = xpuVersion
					}
					// runtime版本显示
					instanceSDK.UpgradeNodeFields.ContainerRuntimeVersion = ccetypes.RuntimeVersion(node.Status.NodeInfo.ContainerRuntimeVersion)
					// 这里用k8sVersion表示当前节点kubelet的版本信息。
					instanceSDK.UpgradeNodeFields.K8SVersion = kubeletK8SVersionToCCEK8SVersion(ctx, node.Status.NodeInfo.KubeletVersion)
					// 判断是否能选择，目前默认返回true
					instanceSDK.UpgradeNodeFields.CanBeSelected = true
				}
			}

			// 按 gpuType 过滤
			if gpuType != "" && instanceSDK.Spec.InstanceResource.GPUType != bcc.GPUType(gpuType) {
				logger.Infof(ctx, "instance %s gpuType not match: %s != %s", instanceSDK.Spec.InstanceName, instanceSDK.Spec.InstanceResource.GPUType, gpuType)
				return
			}

			// phasesMap 为空, 无需过滤
			if len(phasesMap) == 0 {
				instanceSDKChan <- instanceSDK
				return
			}

			// 按 phases 过滤
			if exist, _ := phasesMap[instanceSDK.Status.InstancePhase]; exist {
				instanceSDKChan <- instanceSDK
				return
			}
		}(instanceDB, instanceSDKChan, errChan)
	}

	go func(doneChan chan<- struct{}, errChan <-chan error) {
		for err := range errChan {
			errs = append(errs, err)
		}
		doneChan <- struct{}{}
	}(errDoneChan, errChan)

	go func(doneChan chan<- struct{}, instanceSDKChan <-chan *ccesdk.Instance) {
		for instanceSDK := range instanceSDKChan {
			newInstance := instanceSDK
			instancesSDKMap[instanceSDK.Spec.CCEInstanceID] = newInstance
		}
		doneChan <- struct{}{}
	}(instanceSDKDoneChan, instanceSDKChan)

	wg.Wait()
	close(errChan)
	close(instanceSDKChan)
	<-errDoneChan
	<-instanceSDKDoneChan

	if len(errs) != 0 {
		return nil, errs[0]
	}

	// 为 instanceSDKSlice 重新排序，和 instanceDBSlice 一致
	var instancesSDKSlice []*ccesdk.Instance
	for _, instanceDB := range instanceDBSlice {
		if instanceSDK, ok := instancesSDKMap[instanceDB.Spec.CCEInstanceID]; ok {
			instancesSDKSlice = append(instancesSDKSlice, instanceSDK)
		}
	}
	return instancesSDKSlice, nil
}

func (s *service) ListGPUTypes(ctx context.Context, clusterID, instanceGroupID, accountID string) ([]string, error) {
	logger.Infof(ctx, "list gpu types for cluster %s", clusterID)
	// 参数校验
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}
	if accountID == "" {
		return nil, fmt.Errorf("accountID is empty")
	}

	var instancesList []*models.Instance
	var err error
	if instanceGroupID != "" {
		instancesFromInstanceGroup, err := s.models.GetInstancesByInstanceGroupID(ctx, accountID, clusterID, instanceGroupID,
			"", "", "", "", 100000, 1)
		if err != nil {
			logger.Errorf(ctx, "getInstances from ig failed: %v", err)
		}
		if instancesFromInstanceGroup != nil {
			instancesList = instancesFromInstanceGroup.Items
		}
	} else {
		instancesList, err = s.models.GetInstances(ctx, accountID, clusterID)
		if err != nil {
			logger.Errorf(ctx, "getInstances failed: %v", err)
		}
	}

	if len(instancesList) == 0 {
		return []string{}, nil
	}

	var res []string
	seen := make(map[string]bool)

	for _, instance := range instancesList {
		logger.Infof(ctx, "instance: %s", instance.Spec.InstanceName)
		if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
			continue
		}

		gpuType := string(instance.Spec.InstanceResource.GPUType)
		if gpuType != "" && !seen[gpuType] {
			seen[gpuType] = true
			res = append(res, gpuType)
		}
	}

	return res, nil
}

func (s *service) getGPUNameType(ctx context.Context, node *corev1.Node) (string, string, string) {
	if !isGPUNode(ctx, node) {
		logger.Infof(ctx, "%s is not gpu node", node.Name)
		return "", "", ""
	}
	gpuDisplayNameMap := getGpuDisplayNameMap()
	gpuTypeMap := getGpuTypeMap()
	for resourceName := range node.Status.Capacity {
		rName := resourceName.String()
		if strings.HasPrefix(rName, CGPUPrefix) &&
			strings.HasSuffix(rName, CGPUSuffix) &&
			gpuDisplayNameMap[rName] != "" {
			logger.Infof(ctx, "%s is gpu node, gpuType: %s", node.Name, gpuTypeMap[rName])
			return gpuDisplayNameMap[rName], gpuTypeMap[rName], rName
		}

		if strings.HasSuffix(rName, KunkunResourceNameSuffix) && gpuDisplayNameMap[rName] != "" {
			logger.Infof(ctx, "%s is gpu node, gpuType: %s", node.Name, gpuTypeMap[rName])
			return gpuDisplayNameMap[rName], gpuTypeMap[rName], rName
		}

		if strings.HasPrefix(rName, AscendResourceNamePrefix) && gpuDisplayNameMap[rName] != "" {
			logger.Infof(ctx, "%s is gpu node, gpuType: %s", node.Name, gpuTypeMap[rName])
			return gpuDisplayNameMap[rName], gpuTypeMap[rName], rName
		}
	}

	logger.Infof(ctx, "%s is gpu node, default gpuType is nil", node.Name)
	return "NVIDIA GPU", "", ""
}

// 判断节点上有 Nvidia or Ascend 设备
func isGPUNode(ctx context.Context, node *corev1.Node) bool {
	// 判断节点是否是 Nvidia GPU 节点
	gpuLabelValue, isGpuExist := node.Labels["beta.kubernetes.io/instance-gpu"]

	// 判断节点是否是 Ascend 晟腾节点
	ascendLabelValue, isAscendExist := node.Labels["workerselector"]

	_, isKunlunExist := node.Labels["kunlun-type"]

	logger.Infof(ctx, "%s is gpu node: %v", node.Name, (isGpuExist && gpuLabelValue == "true") || (isAscendExist && ascendLabelValue == "dls-worker-node") || isKunlunExist)
	return (isGpuExist && gpuLabelValue == "true") || (isAscendExist && ascendLabelValue == "dls-worker-node") || isKunlunExist
}

func calculateNodeGPUCapacityAndAllocatable(ctx context.Context, node *corev1.Node) (GPUNumCapacity, GPUNumAllocatable int64) {
	var nvidiaGPUCapacity, nvidiaGPUAllocatable int64
	var cGPUCapacity, cGPUMemoryCapacity, cGPUAllocatable, cGPUMemoryAllocatable int64
	var ascendCapacity, ascendAllocatable int64
	var kunlunCapacity, kunlunAllocatable int64
	for res, value := range node.Status.Capacity {
		if res == NvidiaGPU {
			nvidiaGPUCapacity = value.Value()
		}
		if strings.HasPrefix(res.String(), CGPUPrefix) {
			if strings.HasSuffix(res.String(), CGPUSuffix) {
				cGPUCapacity = value.Value()
			}
			if strings.HasSuffix(res.String(), CGPUMemorySuffix) {
				cGPUMemoryCapacity = value.Value()
			}
		}
		if strings.HasPrefix(res.String(), AscendResourceNamePrefix) {
			ascendCapacity = value.Value()
		}
		if strings.HasSuffix(res.String(), KunkunResourceNameSuffix) {
			kunlunCapacity = value.Value()
		}
	}
	for res, value := range node.Status.Allocatable {
		if res == NvidiaGPU {
			nvidiaGPUAllocatable = value.Value()
		}
		if strings.HasPrefix(res.String(), CGPUPrefix) {
			if strings.HasSuffix(res.String(), CGPUSuffix) {
				cGPUAllocatable = value.Value()
			}
			if strings.HasSuffix(res.String(), CGPUMemorySuffix) {
				cGPUMemoryAllocatable = value.Value()
			}
		}
		if strings.HasPrefix(res.String(), AscendResourceNamePrefix) {
			ascendAllocatable = value.Value()
		}
		if strings.HasSuffix(res.String(), KunkunResourceNameSuffix) {
			kunlunAllocatable = value.Value()
		}
	}
	logger.Infof(ctx, "[NodeResource] node capacity %s nvidia.com/gpu %d,cgpu %d, cgpu_memory %d,ascend %d", node.Name, nvidiaGPUCapacity, cGPUCapacity, cGPUMemoryCapacity, ascendCapacity)

	if nvidiaGPUCapacity != 0 {
		GPUNumCapacity = nvidiaGPUCapacity
	}
	if GPUNumCapacity == 0 {
		GPUNumCapacity = cGPUCapacity
	}
	if GPUNumCapacity == 0 {
		GPUNumCapacity = ascendCapacity
	}
	if GPUNumCapacity == 0 {
		GPUNumCapacity = kunlunCapacity
	}
	//GPUMemoryCapacity = cGPUMemoryCapacity

	logger.Infof(ctx, "[NodeResource] node allocatable %s nvidia.com/gpu %d,cgpu %d, cgpu_memory %d,ascend %d", node.Name, nvidiaGPUAllocatable, cGPUAllocatable, cGPUMemoryAllocatable, ascendAllocatable)
	if nvidiaGPUAllocatable != 0 {
		GPUNumAllocatable = nvidiaGPUAllocatable
	}
	if GPUNumAllocatable == 0 {
		GPUNumAllocatable = cGPUAllocatable
	}

	if GPUNumAllocatable == 0 {
		GPUNumAllocatable = ascendAllocatable
	}
	if GPUNumAllocatable == 0 {
		GPUNumAllocatable = kunlunAllocatable
	}
	//GPUMemoryAllocatable = cGPUMemoryAllocatable

	return GPUNumCapacity, GPUNumAllocatable
}

func (s *service) calculateNodeAllocatedGPUResources(ctx context.Context, pods []*corev1.Pod) (int64, error) {
	if len(pods) == 0 {
		return 0, nil
	}

	// request 里计算 gpu 资源
	reqs := map[corev1.ResourceName]resource.Quantity{}
	for _, pod := range pods {
		podReqs, _, err := s.podRequestsAndLimits(pod)
		if err != nil {
			logger.Errorf(ctx, "failed to get pod requests and limits: %v", err)
			return 0, nil
		}
		for podReqName, podReqValue := range podReqs {
			if value, ok := reqs[podReqName]; !ok {
				reqs[podReqName] = podReqValue.DeepCopy()
			} else {
				value.Add(podReqValue)
				reqs[podReqName] = value
			}
		}
		//for podLimitName, podLimitValue := range podLimits {
		//	if value, ok := limits[podLimitName]; !ok {
		//		limits[podLimitName] = podLimitValue.DeepCopy()
		//	} else {
		//		value.Add(podLimitValue)
		//		limits[podLimitName] = value
		//	}
		//}
	}

	var nvidiaGPUAllocated, cGPUAllocated, ascendAllocated, kunlunAllocated int64
	for res, value := range reqs {
		if res == NvidiaGPU {
			nvidiaGPUAllocated += value.Value()
		}

		if strings.HasPrefix(res.String(), CGPUPrefix) {
			if strings.HasSuffix(res.String(), CGPUSuffix) {
				cGPUAllocated += value.Value()
			}
		}
		if strings.HasPrefix(res.String(), AscendResourceNamePrefix) {
			ascendAllocated += value.Value()
		}
		if strings.HasSuffix(res.String(), KunkunResourceNameSuffix) {
			kunlunAllocated += value.Value()
		}
	}
	logger.Infof(ctx, "node allocated nvidia.com/gpu %d, cgpu %d, ascend %d, kunlun %d",
		nvidiaGPUAllocated, cGPUAllocated, ascendAllocated, kunlunAllocated)
	return nvidiaGPUAllocated + cGPUAllocated + ascendAllocated + kunlunAllocated, nil
}

func (s *service) podRequestsAndLimits(pod *corev1.Pod) (reqs, limits corev1.ResourceList, err error) {
	reqs, limits = corev1.ResourceList{}, corev1.ResourceList{}
	for _, container := range pod.Spec.Containers {
		s.addResourceList(reqs, container.Resources.Requests)
		s.addResourceList(limits, container.Resources.Limits)
	}
	// init containers define the minimum of any resource
	for _, container := range pod.Spec.InitContainers {
		s.maxResourceList(reqs, container.Resources.Requests)
		s.maxResourceList(limits, container.Resources.Limits)
	}

	return
}

// maxResourceList sets list to the greater of list/newList for every resource
// either list
func (s *service) maxResourceList(list, new corev1.ResourceList) {
	for name, quantity := range new {
		if value, ok := list[name]; !ok {
			list[name] = quantity.DeepCopy()
			continue
		} else {
			if quantity.Cmp(value) > 0 {
				list[name] = quantity.DeepCopy()
			}
		}
	}
}

// addResourceList adds the resources in newList to list
func (s *service) addResourceList(list, new corev1.ResourceList) {
	for name, quantity := range new {
		if value, ok := list[name]; !ok {
			list[name] = quantity.DeepCopy()
		} else {
			value.Add(quantity)
			list[name] = value
		}
	}
}

func (s *service) getNodeRunningPods(node *corev1.Node, podsList []corev1.Pod) []corev1.Pod {
	result := []corev1.Pod{}
	for _, pod := range podsList {
		if pod.Spec.NodeName == node.Name &&
			pod.Status.Phase != corev1.PodSucceeded &&
			pod.Status.Phase != corev1.PodFailed {
			result = append(result, pod)
		}
	}
	return result
}

func (s *service) filterVKNodes(ctx context.Context, vkNodes map[string]*corev1.Node, phasesMap map[ccetypes.InstancePhase]bool, isK8sNodeName bool, keywordType, keyword, clusterID string) []*ccesdk.Instance {
	instancesSDK := make([]*ccesdk.Instance, 0)
	keywordSet := make(map[string]struct{})
	var supportWildCard bool

	if keyword != "" {
		for _, k := range strings.Split(keyword, ",") {
			keywordSet[strings.TrimSpace(k)] = struct{}{}
		}
	}

	// 模糊搜索仅支持查询一个 keyword
	if len(keywordSet) == 1 {
		supportWildCard = true
	}
	logger.Infof(ctx, "get keywordType=%s, keyword=%s, supportFuzzy=%v", keywordType, keyword, supportWildCard)

	for _, vkNode := range vkNodes {
		// vk 当前仅支持 vpcID 和 instanceName 筛选
		var hitKey bool
		if isK8sNodeName || keywordType == string(ccesdk.InstanceKeywordTypeK8sNodeName) {
			logger.Infof(ctx, "get keywordType=%s, keyword=%s", string(ccesdk.InstanceKeywordTypeK8sNodeName), keyword)
			hitKey = s.filterVKNodesByK8sNodeName(ctx, keywordType, keyword, vkNode, keywordSet, supportWildCard)
		} else if keywordType == string(ccesdk.InstanceKeywordTypeVPCIP) {
			logger.Infof(ctx, "get keywordType=%s, keyword=%s", keywordType, keyword)
			hitKey = s.filterVKNodesByVPCIP(ctx, keywordType, keyword, vkNode, keywordSet, supportWildCard)
		} else {
			logger.Infof(ctx, "get keywordType=%s, keyword=%s", keywordType, keyword)
			if keyword != "" {
				continue
			}
		}

		if len(keywordSet) > 0 && !hitKey {
			continue
		}

		// phasesMap 为空, 无需过滤
		if len(phasesMap) == 0 || phasesMap[instancePhaseByK8SNodeStatus(ctx, K8sNodeInternalIP(ctx, vkNode), vkNodes)] {
			instancesSDK = append(instancesSDK, &ccesdk.Instance{
				Spec: &ccesdk.InstanceSpec{
					CCEInstanceID: vkNode.Name,
					ClusterID:     clusterID,
					ClusterRole:   ccetypes.ClusterRoleNode,
				},
				Status: &ccesdk.InstanceStatus{
					Machine: ccesdk.Machine{
						K8SNodeName: vkNode.Name,
						VPCIP:       K8sNodeInternalIP(ctx, vkNode),
					},
					InstancePhase: instancePhaseByK8SNodeStatus(ctx, K8sNodeInternalIP(ctx, vkNode), vkNodes),
				},
				IsVKNode:  true,
				CreatedAt: vkNode.CreationTimestamp.Time,
			})
		}
	}
	return instancesSDK
}

func (s *service) filterVKNodesByK8sNodeName(ctx context.Context, keywordType, keyword string, vkNode *corev1.Node, keywordSet map[string]struct{}, supportWildCard bool) bool {
	var hitKey bool
	if supportWildCard {
		if strings.Index(vkNode.Name, strings.TrimSpace(keyword)) != -1 {
			hitKey = true
		}
	} else {
		_, hitKey = keywordSet[vkNode.Name]
	}

	return hitKey
}
func (s *service) filterVKNodesByVPCIP(ctx context.Context, keywordType, keyword string, vkNode *corev1.Node, keywordSet map[string]struct{}, supportWildCard bool) bool {
	var hitKey bool
	if supportWildCard {
		if strings.Index(K8sNodeInternalIP(ctx, vkNode), strings.TrimSpace(keyword)) != -1 {
			hitKey = true
		}
	} else {
		_, hitKey = keywordSet[K8sNodeInternalIP(ctx, vkNode)]
	}

	return hitKey
}

func (s *service) ListInstancesByInstanceGroup(ctx context.Context, options ListInstanceOptions) (*ccesdk.ListInstancesByInstanceGroupIDPage, error) {
	var (
		clusterID                  = options.ClusterID
		instanceGroupID            = options.InstanceGroupID
		keywordType                = options.KeywordType
		keyword                    = options.Keyword
		pageParams                 = options.PageParams
		orderByRaw                 = options.OrderBy
		phases                     = options.Phases
		enableInternalFields       = options.EnableInternalFields
		enableUpgradeNodeFields    = options.EnableUpgradeNodeFields
		gpuType                    = options.GPUType
		calculateGPUCountRequested = options.CalculateGPUCountRequested
	)
	// 参数校验
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}
	if instanceGroupID == "" {
		return nil, fmt.Errorf("instanceGroupID is empty")
	}

	// 默认返回第一页
	if pageParams.PageNo < 1 {
		pageParams.PageNo = ccesdk.PageNoDefault
	}

	// 默认每页 10 个节点
	if pageParams.PageSize < 1 {
		pageParams.PageSize = ccesdk.PageSizeDefault
	}

	// 查询集群
	cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		return nil, err
	}

	// 处理order字段
	pageParams = handleInstancePageParams(pageParams, cluster.Spec.K8SCustomConfig.EnableHostname)

	// TODO 目前节点组滚动更新复用了enableUpgradeNodeFields字段，为了与实例列表区分开来，这里新定义一个bool，后续二期前后端一起修改。
	enableInstanceGroupComponent := enableUpgradeNodeFields
	// 异步查询 K8S Node
	var wg sync.WaitGroup
	var nodes map[string]*corev1.Node
	instanceCrdMap := make(map[string]*ccev1.Instance)
	var nodePodsMap map[string][]*corev1.Pod
	if calculateGPUCountRequested {
		wg.Add(1)
		go func() {
			defer wg.Done()

			logger.Infof(ctx, "Get node pods begin!")

			nodePodsMap, err = s.getK8SPods(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "getK8SNodes failed: %v", err)
			}

			logger.Infof(ctx, "Get node pods finished")
		}()
	}

	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning &&
		cluster.Spec.ClusterType != ccetypes.ClusterTypeServerless {

		wg.Add(1)
		go func() {
			defer wg.Done()

			nodes, err = s.getK8SNodes(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "getK8SNodes failed: %v", err)
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			// 全量查询InstanceCRD，获取不保存数据库的字段。目前nvidia-container-toolkit版本、缩容保护等字段不会落库，只保存在crd中
			instanceCrdList, err := s.clients.MetaK8SClient.ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
				LabelSelector: fmt.Sprintf("%s=%s,%s=%s", ccetypes.ClusterIDLabelKey, clusterID,
					ccetypes.InstanceGroupIDLabelKey, instanceGroupID)})
			if err != nil {
				logger.WithValues("instanceGroupID", instanceGroupID).Errorf(ctx, "failed to list instance belong to instanceGroup, err: %v", err)
			}
			for _, item := range instanceCrdList.Items {
				node := item
				instanceCrdMap[node.Status.Machine.VPCIP] = &node
			}
		}()
	}

	// 数据库查询全量 Instance
	instanceOptions := models.InstanceListOption{
		AccountID:       s.accountID,
		ClusterID:       clusterID,
		InstanceGroupID: instanceGroupID,
		KeywordType:     keywordType,
		Keyword:         keyword,
		OrderBy:         pageParams.OrderBy.String(),
		Order:           pageParams.Order.String(),
		PageNo:          1,
		PageSize:        100000,
	}
	list, err := s.models.GetInstanceEx(ctx, instanceOptions)
	if err != nil {
		logger.Errorf(ctx, "GetInstancesByBatchQuery failed: %v", err)
		return nil, err
	}

	wg.Wait()

	// 控制台的状态粒度比后台的节点状态粒度更粗 这里返回[请求期望的控制台状态]到[后台节点状态集]的映射
	phasesMap, err := parseSDKInstancePhases(ctx, phases)
	if err != nil {
		logger.Errorf(ctx, "parseSDKInstancePhases failed: %v", err)
		return nil, err
	}

	selectedList, err := s.generateInstancesSDKs(ctx, cluster, list.Items, nil, phasesMap, nodes, nodePodsMap,
		enableInternalFields, false, false, enableInstanceGroupComponent, gpuType, instanceCrdMap)
	if err != nil {
		logger.Errorf(ctx, "GenerateSDKs failed: %v", err)
		return nil, err
	}

	if orderByRaw == "gpuCountRemaining" {
		sort.Slice(selectedList, func(i, j int) bool {
			// 空闲卡相同情况下，默认按照总卡数升序排序
			if selectedList[i].Status.Resources.GPUCountRemaining == selectedList[j].Status.Resources.GPUCountRemaining {
				return selectedList[i].Spec.InstanceResource.GPUCount < selectedList[j].Spec.InstanceResource.GPUCount
			}
			if pageParams.Order == ccesdk.OrderDESC {
				// logger.Infof(ctx, "order by gpuCountRemaining desc")
				return selectedList[i].Status.Resources.GPUCountRemaining > selectedList[j].Status.Resources.GPUCountRemaining
			} else {
				// logger.Infof(ctx, "order by gpuCountRemaining asc")
				return selectedList[i].Status.Resources.GPUCountRemaining < selectedList[j].Status.Resources.GPUCountRemaining
			}
		})
	}

	// 分页
	total := len(selectedList)
	start := (pageParams.PageNo - 1) * pageParams.PageSize
	end := int(math.Min(float64(pageParams.PageNo*pageParams.PageSize), float64(total)))

	instanceList := make([]*ccesdk.Instance, 0)
	if start < end {
		instanceList = selectedList[start:end]
	}

	page := ccesdk.ListInstancesByInstanceGroupIDPage{
		PageNo:     pageParams.PageNo,
		PageSize:   pageParams.PageSize,
		TotalCount: total,
		List:       instanceList,
	}

	return &page, nil
}

// UpdateInstanceScaleDownDisabled 节点缩容保护开关
// 1、支持批量操作多个节点组节点。
// 2、不支持游离节点开启，如果instanceIDs中有游离节点，则直接接口报错（与前端逻辑保持一致）。
// 3、节点状态只有是可用或者不可用时，能变更节点缩容状态，其余不支持变更，接口直接报错，不做变更（与前端逻辑保持一致）。
// 4、开启时：node上添加annotation——cluster-autoscaler.kubernetes.io/scale-down-disabled：true来让CA跳过开启了缩容保护的节点。
// 4·1、关闭时：删掉node上对应的annotation
func (s *service) UpdateInstanceScaleDownDisabled(ctx context.Context, clusterID string,
	req ccesdk.UpdateNodeScaleDownRequest) ([]ccesdk.FailedInstance, error) {
	// 获取所有instance信息，筛选是否是游离节点
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	// 获取集群信息
	cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
	if err != nil {
		return nil, err
	}

	// 这里不能根据传入instanceIDs去查找所有节点，否则无法识别instanceID是不是当前集群的，或者是不是无效的instanceIDs
	allInstances, err := s.models.GetInstances(ctx, s.accountID, clusterID)
	//allInstances, err := s.models.GetInstancesByCCEIDs(ctx, clusterID, req.InstanceIDs, s.accountID)
	if err != nil {
		return nil, err
	}

	// 检查是否存在非节点组节点
	// 检查节点状态是否处于可变更的状态，只有可用或者不可用状态的节点可以变更
	if len(allInstances) == 0 {
		logger.Warnf(ctx, "UpdateInstanceScaleDownDisabled GetInstances is empty, clusterID: %s", utils.ToJSON(clusterID))
		return nil, nil
	}

	existInstanceIDs := make(map[string]*models.Instance, 0)
	for _, instance := range allInstances {
		existInstanceIDs[instance.Spec.CCEInstanceID] = instance
	}

	// key为数据库中的instance_id，是否需要instanceCRD待定。
	k8sNodes := make(map[string]*corev1.Node)

	k8sNodes, err = s.getK8SNodesByInstanceID(ctx, clusterID)
	if err != nil {
		return nil, err
	}

	actualInstances := make([]*models.Instance, 0)
	for _, instanceID := range req.InstanceIDs {
		// 存在不在当前集群内的节点报错
		instance, ok := existInstanceIDs[instanceID]
		if !ok || instance == nil {
			return []ccesdk.FailedInstance{
				{
					InstanceID: instanceID,
					Reason:     fmt.Sprintf("instance %s is not belong to cluster %s ", instanceID, clusterID),
				},
			}, errors.New(fmt.Sprintf("instance %s is not belong to cluster %s ", instanceID, clusterID))
		}
		// 如果存在非节点组节点，则直接报错
		if instance.Spec.InstanceGroupID == "" {
			return []ccesdk.FailedInstance{
				{
					InstanceID: instanceID,
					Reason:     fmt.Sprintf("instance %s is not support set scale-down-Disabled", instance.Spec.InstanceName),
				},
			}, errors.New(fmt.Sprintf("instance %s is not support set scale-down-Disabled", instance.Spec.InstanceName))
		}

		instancePhase := instancePhaseByK8SNodeStatus(ctx, instance.Status.Machine.InstanceID, k8sNodes)
		// 节点可用对应running状态，节点不可用对应 phaseNotReady 或者 phaseNotReadySchedulingDisabled
		if instancePhase != ccetypes.InstancePhaseRunning && instancePhase != phaseReadySchedulingDisabled &&
			instancePhase != phaseReady && instancePhase != phaseNotReady && instancePhase != phaseNotReadySchedulingDisabled {
			return []ccesdk.FailedInstance{
					{
						InstanceID: instanceID,
						Reason:     fmt.Sprintf("instance %s status %s is not support set scale-down-Disabled", instance.Spec.InstanceName, instancePhase),
					},
				}, errors.New(fmt.Sprintf("instance %s status %s is not support set scale-down-Disabled",
					instance.Spec.InstanceName, instancePhase))
		}
		actualInstances = append(actualInstances, instance)
	}

	// 添加node上annotation标识
	wg := sync.WaitGroup{}
	failedInstances := make([]ccesdk.FailedInstance, 0)
	// 控制并发数100
	concurrent := make(chan struct{}, 100)
	mu := sync.Mutex{}

	// 公共错误处理函数，避免在每个goroutine中重复定义
	addFailedInstance := func(instanceID, reason string) {
		mu.Lock()
		failedInstances = append(failedInstances, ccesdk.FailedInstance{
			InstanceID: instanceID,
			Reason:     reason,
		})
		mu.Unlock()
	}
	for _, instance := range actualInstances {
		wg.Add(1)
		// 修改instance crd
		go func(cceInstanceID string, enableHostname, scaleDownDisabled bool) {
			concurrent <- struct{}{}
			defer func() {
				wg.Done()
				<-concurrent
			}()
			// TODO 重试逻辑移动到GetInstance方法内
			instanceCRD, err := s.clients.MetaK8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, cceInstanceID, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "get instance CRD err: %v", err)
				for i := 0; i < 2; i++ {
					instanceCRD, err = s.clients.MetaK8SClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, cceInstanceID, &metav1.GetOptions{})
					if err != nil {
						logger.Errorf(ctx, "get instance CRD err: %v", err)
					} else {
						break
					}
				}
				if instanceCRD == nil || err != nil {
					logger.Errorf(ctx, "failed to get instance CRD with retry 3 number, err: %v", err)
					addFailedInstance(cceInstanceID, fmt.Sprintf("failed to get instance CRD: %v", err))
					return
				}
			}
			// 缩容保护标识不一致，则更新instance CRD
			if scaleDownDisabled != instanceCRD.Spec.ScaleDownDisabled {
				updateIns := instanceCRD.DeepCopy()
				updateIns.Spec.ScaleDownDisabled = scaleDownDisabled
				// 更新instance CRD 逻辑内部已有重试
				err := s.clients.MetaK8SClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, updateIns.Name, updateIns)
				if err != nil {
					logger.Errorf(ctx, "update instance CRD err: %v", err)
					addFailedInstance(cceInstanceID, fmt.Sprintf("failed to update instance CRD: %v", err))
					return
				}
			}

			// 获取 NodeName
			nodeName := utils.GetNodeName(ctx, instanceCRD.Status.Machine.VPCIP, instanceCRD.Status.Machine.Hostname, instanceCRD.Spec.InstanceName, enableHostname)
			// 通过 instance-id获取对应的node信息
			nodeDetail := k8sNodes[instanceCRD.Status.Machine.InstanceID]
			if nodeDetail.ObjectMeta.Name != nodeName {
				logger.Errorf(ctx, "nodeName %s is not equal with instanceID %s", nodeName, instanceCRD.Status.Machine.InstanceID)
				addFailedInstance(cceInstanceID, fmt.Sprintf("nodeName %s mismatch with instanceID %s", nodeName, instanceCRD.Status.Machine.InstanceID))
				return
			}

			// 缩容保护开启
			if scaleDownDisabled {
				if nodeDetail.ObjectMeta.Annotations == nil || len(nodeDetail.ObjectMeta.Annotations) == 0 {
					annotation := make(map[string]string)
					annotation[ccetypes.AnnotationKeyScaleDownDisabled] = ccetypes.ScaleDownDisabledValueTrue
					nodeDetail.ObjectMeta.Annotations = annotation
				} else {
					nodeDetail.ObjectMeta.Annotations[ccetypes.AnnotationKeyScaleDownDisabled] = ccetypes.ScaleDownDisabledValueTrue
				}
			} else { // 关闭缩容保护
				if nodeDetail.ObjectMeta.Annotations != nil && len(nodeDetail.ObjectMeta.Annotations) > 0 {
					if _, ok := nodeDetail.ObjectMeta.Annotations[ccetypes.AnnotationKeyScaleDownDisabled]; ok {
						// 存在的话，就删除annotation
						delete(nodeDetail.ObjectMeta.Annotations, ccetypes.AnnotationKeyScaleDownDisabled)
					}
				}
			}
			// 更新node信息
			_, err = s.k8sClient.CoreV1().Nodes().Update(ctx, nodeDetail, metav1.UpdateOptions{})
			if err != nil {
				logger.Errorf(ctx, "Update node %s failed", nodeName)
				addFailedInstance(cceInstanceID, fmt.Sprintf("failed to update node %s: %v", nodeName, err))
				return
			}

		}(instance.Spec.CCEInstanceID, cluster.Spec.K8SCustomConfig.EnableHostname, req.ScaleDownDisabled)
	}

	wg.Wait()
	close(concurrent)

	// 只要成功一个实例就可以
	if len(failedInstances) < len(actualInstances) {
		return failedInstances, nil
	}
	return failedInstances, errors.New("update all instances failed")
}

func (s *service) getNvidiaContainerToolkitVersionByOOS(ctx context.Context, instanceCrd *ccev1.Instance) (version string, err error) {
	config := &types.Config{
		STSEndpoint:     s.config.ClientConfig.STSEndpoint,
		IAMEndpoint:     s.config.ClientConfig.IAMEndpoint,
		ServiceRoleName: s.config.ClientConfig.ServiceRoleName,
		ServiceName:     s.config.ClientConfig.ServiceName,
		ServicePassword: s.config.ClientConfig.ServicePassword,
		Timeout:         3 * time.Second,
	}

	password, err := deployer.DecryptAdminPassword(instanceCrd.Spec.AdminPassword)
	if err != nil {
		logger.Errorf(ctx, "AESCFBDecrypt failed: %s", err)
		return version, err
	}

	sshIP := instanceCrd.Status.Machine.FloatingIP
	sshPort := 22
	serverlessRelayIP, serverlessRelayPassword := "", ""
	if config.Region != "edge" && instanceCrd.Spec.MachineType == ccetypes.MachineTypeBEC {
		sshIP = instanceCrd.Status.Machine.VPCIP
		serverlessRelayIP = instanceCrd.Spec.AIInfraOption.ServerLessRelayIP
		serverlessRelayPassword, err = deployer.DecryptAdminPassword(instanceCrd.Spec.AIInfraOption.ServerLessRelayPW)
		if err != nil {
			logger.Errorf(ctx, "AESCFBDecrypt serverlessRelayPassword failed: %s", err)
			return version, err
		}
	}
	if sshIP == "" && instanceCrd.Status.Machine.BAEndpoint.IP != "" && instanceCrd.Status.Machine.BAEndpoint.Port != "" {
		sshIP = instanceCrd.Status.Machine.BAEndpoint.IP
		sshPort, err = strconv.Atoi(instanceCrd.Status.Machine.BAEndpoint.Port)
		if err != nil {
			return version, err
		}
	}

	instance := &types.Instance{
		// 适配oos字段
		DeployType:    ccetypes.DeployInstanceByOOS,
		Machinetype:   instanceCrd.Spec.MachineType,
		Local:         false,
		InstanceUUID:  instanceCrd.Status.Machine.InstanceUUID,
		MachineID:     instanceCrd.Status.Machine.InstanceID,
		CCEInstanceID: instanceCrd.Spec.CCEInstanceID,
		Region:        s.config.ClientConfig.Region,
		AccountID:     instanceCrd.Spec.AccountID,

		// 适配ssh字段
		SSHIP:                   sshIP,
		SSHPort:                 sshPort,
		RunUser:                 "root",
		Password:                password,
		ServerlessRelayPassword: serverlessRelayPassword,
		ServerlessRelayIP:       serverlessRelayIP,
	}

	oosClient, _, err := exec.NewExecClient(ctx, instance, config)
	if err != nil {
		return version, err
	}
	oosClient.SetRetry(5, 500*time.Millisecond)
	// 获取版本
	stdout, stderr, err := oosClient.Exec(ctx, &exectypes.Command{
		Command: "nvidia-container-cli --version",
	})
	if err != nil {
		logger.Errorf(ctx, "exec nvidia-container-cli --version failed, err: %v, stderr: %v", err, stderr)
		return version, err
	}

	// 定义正则表达式，匹配 'cli-version: ' 或 'version: ' 后的版本号
	re := regexp.MustCompile(`(?m)^(?:cli-)?version:\s*([^\s]+)`)
	matches := re.FindStringSubmatch(stdout)

	if len(matches) < 2 {
		return "", fmt.Errorf("未能在输出中找到版本号")
	}
	return matches[1], nil
}

func (s *service) getK8SPods(ctx context.Context, clusterID string) (map[string][]*corev1.Pod, error) {
	if s.k8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return nil, fmt.Errorf("k8sClient not initialized")
	}

	var (
		podList *corev1.PodList
		err     error
	)

	if s.clusterResourceCache != nil {
		logger.Infof(ctx, "resourceCache Initialized")
		val, found := s.clusterResourceCache.Get(fmt.Sprintf(PodCacheKeyTemplate, clusterID))
		if found {
			if pods, ok := val.(*corev1.PodList); ok {
				podList = pods
				logger.Infof(ctx, "get k8s pods from cache, nodeCount: %d", len(pods.Items))
			}
		}
	}

	if podList == nil {
		listOpts := metav1.ListOptions{
			FieldSelector: fields.AndSelectors(
				fields.OneTermNotEqualSelector("status.phase", "Succeeded"),
				fields.OneTermNotEqualSelector("status.phase", "Failed"),
			).String(),
			ResourceVersion: "0",
		}
		podList, err = s.k8sClient.CoreV1().Pods(metav1.NamespaceAll).List(ctx, listOpts)
		if err != nil {
			logger.Warnf(ctx, "list k8s pods from apiserver failed: %v", err)
			return nil, nil
		}
		logger.Infof(ctx, "list k8s pods from apiserver")
		if s.clusterResourceCache != nil {
			s.clusterResourceCache.Set(fmt.Sprintf(PodCacheKeyTemplate, clusterID), podList, ResourceCacheDefaultExpiration)
			logger.Infof(ctx, "stored k8s pods in resourceCache")
		}
	}

	result := make(map[string][]*corev1.Pod)
	for _, item := range podList.Items {
		pod := item
		result[pod.Spec.NodeName] = append(result[pod.Spec.NodeName], &pod)
	}

	return result, nil
}

func (s *service) getK8SNodes(ctx context.Context, clusterID string) (map[string]*corev1.Node, error) {
	if s.k8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return nil, fmt.Errorf("k8sClient not initialized")
	}

	nodeList, err := s.k8sClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{ResourceVersion: "0"})
	if err != nil {
		logger.Warnf(ctx, "list k8s nodes from apiserver failed: %v", err)
		return nil, nil
	}
	logger.Infof(ctx, "list k8s nodes from apiserver")

	result := make(map[string]*corev1.Node)
	for _, item := range nodeList.Items {
		node := item
		result[K8sNodeInternalIP(ctx, &node)] = &node
	}

	return result, nil
}

// 使用机器短ID作为key
func (s *service) getK8SNodesByInstanceID(ctx context.Context, clusterID string) (map[string]*corev1.Node, error) {
	if s.k8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return nil, fmt.Errorf("k8sClient not initialized")
	}

	nodeList, err := s.k8sClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{ResourceVersion: "0"})
	if err != nil {
		logger.Warnf(ctx, "list k8s nodes from apiserver failed: %v", err)
		return nil, nil
	}
	logger.Infof(ctx, "list k8s nodes from apiserver")

	result := make(map[string]*corev1.Node)
	for _, item := range nodeList.Items {
		node := item
		providerID := node.Spec.ProviderID
		instanceID := strings.Replace(providerID, "cce://", "", 1)
		result[instanceID] = &node
	}

	return result, nil
}

func (s *service) getVKNodes(ctx context.Context, k8sNodes map[string]*corev1.Node) (map[string]*corev1.Node, error) {
	result := make(map[string]*corev1.Node)
	for key, node := range k8sNodes {
		if node.GetLabels() == nil {
			continue
		}
		value, ok := node.GetLabels()["type"]
		if !ok {
			continue
		}
		if value != "virtual-kubelet" {
			continue
		}
		item := k8sNodes[key]
		result[key] = item
	}
	return result, nil
}

func K8sNodeInternalIP(ctx context.Context, node *corev1.Node) string {
	for _, address := range node.Status.Addresses {
		if address.Type == corev1.NodeInternalIP {
			return address.Address
		}
	}

	return ""
}

func GetK8sNodeStatus(ctx context.Context, instance *ccesdk.Instance, enableHostname bool, k8sClient kubernetes.Interface) (ccetypes.InstancePhase, error) {
	if instance == nil {
		return phaseNotReady, fmt.Errorf("instance is nil")
	}

	if k8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return instance.Status.InstancePhase, fmt.Errorf("k8sClient not initialized")
	}

	nodeName := utils.GetNodeName(ctx, instance.Status.Machine.VPCIP, instance.Status.Machine.Hostname, instance.Spec.InstanceName, enableHostname)
	node, err := k8sClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "K8SClient GetNodes failed: %s", err)
		return instance.Status.InstancePhase, err
	}

	return K8sNodeStatus(ctx, node), nil
}

func GetVKNodeStatus(ctx context.Context, instance *ccesdk.Instance, nodeName string, k8sClient kubernetes.Interface) (ccetypes.InstancePhase, error) {
	if instance == nil {
		return phaseNotReady, fmt.Errorf("instance is nil")
	}

	if k8sClient == nil {
		logger.Errorf(ctx, "k8sClient not initialized")
		return instance.Status.InstancePhase, fmt.Errorf("k8sClient not initialized")
	}

	node, err := k8sClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "K8SClient GetNodes failed: %s", err)
		return instance.Status.InstancePhase, err
	}

	return K8sNodeStatus(ctx, node), nil
}

func K8sNodeStatus(ctx context.Context, node *corev1.Node) ccetypes.InstancePhase {
	if nodeInternalIP := K8sNodeInternalIP(ctx, node); nodeInternalIP == "" {
		return phaseNotReady
	}

	nodeReady := false
	networkUnavailable := true
	nodeSchedulingDisabled := false

	if node.Spec.Unschedulable {
		nodeSchedulingDisabled = true
	}

	for _, condition := range node.Status.Conditions {
		// Node Ready
		if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
			nodeReady = true
		}

		// NodeNetworkUnavailable
		if condition.Type == corev1.NodeNetworkUnavailable && condition.Status == corev1.ConditionFalse {
			networkUnavailable = false
		}
	}

	if networkUnavailable {
		if nodeSchedulingDisabled {
			return phaseNotReadySchedulingDisabled
		}
		return phaseNotReady
	}

	// 和 DB/CRD 中使用 running 代表 K8s node ready 保持一致
	if nodeReady {
		if nodeSchedulingDisabled {
			return phaseReadySchedulingDisabled
		}
		return ccetypes.InstancePhaseRunning
	}

	if nodeSchedulingDisabled {
		return phaseNotReadySchedulingDisabled
	}
	return phaseNotReady
}

// checkListInstanceClusterRole - 插件 ListInstance 中 ClusterRole 参数是否合法
func checkListInstanceClusterRole(ctx context.Context, cluster *models.Cluster, clusterRole ccetypes.ClusterRole) (ccetypes.ClusterRole, bool, error) {
	if cluster == nil {
		return clusterRole, false, fmt.Errorf("cluster is nil")
	}

	// ClusterRole == Node: 正常返回
	if clusterRole == ccetypes.ClusterRoleNode {
		return ccetypes.ClusterRoleNode, false, nil
	}

	// ClusterRole == Master: 托管 和 Serverless 不能返回
	if clusterRole == ccetypes.ClusterRoleMaster {
		switch cluster.Spec.MasterConfig.MasterType {
		case ccetypes.MasterTypeManaged, ccetypes.MasterTypeContainerizedManaged, ccetypes.MasterTypeServerless:
			logger.Infof(ctx, "Managed & Serverless Cluster cannot list master, return empty")
			return clusterRole, true, nil
		}

		return ccetypes.ClusterRoleMaster, false, nil
	}

	// ClusterRole == All: 托管 和 Serverless 不能返回 Master
	if clusterRole == "" {
		switch cluster.Spec.MasterConfig.MasterType {
		case ccetypes.MasterTypeManaged, ccetypes.MasterTypeContainerizedManaged, ccetypes.MasterTypeServerless:
			logger.Infof(ctx, "Managed & Serverless Cluster cannot list master, return empty")
			return ccetypes.ClusterRoleNode, false, nil
		}

		return ccetypes.ClusterRole(""), false, nil
	}

	return clusterRole, false, fmt.Errorf("unsupported clusterRole %s", clusterRole)
}

// instancePhaseByK8SNodeStatus - 根据 K8SNode 状态替换 InstancePhase
//
// PARAMS:
//   - ctx: The context to trace request
//   - k8sNodeIP: string
//   - k8sNodes: map[string]*corev1.Node
//
// RETURNS:
//
//	string: 转化后的 InstancePhase
//	error: nil if succeed, error if fail
func instancePhaseByK8SNodeStatus(ctx context.Context, k8sNodeIP string, k8sNodes map[string]*corev1.Node) ccetypes.InstancePhase {
	if k8sNodeIP == "" || len(k8sNodes) == 0 {
		return phaseNotReady
	}

	// K8S Node 不存在, 返回 phaseNotReady
	k8sNode, ok := k8sNodes[k8sNodeIP]
	if !ok {
		return phaseNotReady
	}

	// K8S Node 存在, 根据情况返回
	return K8sNodeStatus(ctx, k8sNode)
}

// getUpgradeNodeFields - 生成 UpgradeNodeFields
//
// PARAMS:
//   - ctx: The context to trace request
//   - cluster: *models.Cluster
//   - k8sNodeIP: string
//   - k8sNodes: map[string]*corev1.Node
//
// RETURNS:
//
//	*ccesdk.UpgradeNodeFields
//	error: nil if succeed, error if fail
func getUpgradeNodeFields(ctx context.Context, cluster *models.Cluster, k8sNodeIP string,
	k8sNodes map[string]*corev1.Node) (*ccesdk.UpgradeNodeFields, error) {
	if cluster == nil {
		return nil, fmt.Errorf("cluster is nil")
	}

	// 异常情况
	if k8sNodeIP == "" || len(k8sNodes) == 0 {
		return &ccesdk.UpgradeNodeFields{
			K8SVersion:    ccetypes.K8SVersion("unknown"),
			CanBeSelected: false,
			Reason:        "Get K8S Nodes return empty",
		}, nil
	}

	// K8SNode 不存在
	k8sNode, ok := k8sNodes[k8sNodeIP]
	if !ok {
		return &ccesdk.UpgradeNodeFields{
			K8SVersion:    ccetypes.K8SVersion("unknown"),
			CanBeSelected: false,
			Reason:        fmt.Sprintf("%s not exists in K8S, try [kubectl get nodes] to confirm", k8sNodeIP),
		}, nil
	}

	// K8SNode 存在
	canBeSelected, reason := canKubeletBeUpgraded(ctx, cluster, k8sNode)

	return &ccesdk.UpgradeNodeFields{
		K8SVersion:    kubeletK8SVersionToCCEK8SVersion(ctx, k8sNode.Status.NodeInfo.KubeletVersion),
		CanBeSelected: canBeSelected,
		Reason:        reason,
	}, nil
}

// canKubeletBeUpgraded - 对比 Cluster 和 kubelet 的 K8SVersion, 判断 kubelet 是否可以升级
func canKubeletBeUpgraded(ctx context.Context, cluster *models.Cluster, k8sNode *corev1.Node) (bool, string) {
	clusterK8SVersion := cluster.Spec.K8SVersion
	kubeletK8SVersion := kubeletK8SVersionToCCEK8SVersion(ctx, k8sNode.Status.NodeInfo.KubeletVersion)

	// Kubelet 和 Cluster 版本一致, 无需升级
	if kubeletK8SVersion == clusterK8SVersion {
		return false, fmt.Sprintf("Kubelet version %s = cluster version %s", kubeletK8SVersion, clusterK8SVersion)
	}

	// 获取 kubelet 允许升级版本
	supportVersions := kubeletK8SVersion.CanBeUpgradedTo()
	if len(supportVersions) == 0 {
		logger.Errorf(ctx, "Kubelet version %s not supported by CCE", kubeletK8SVersion)
		return false, fmt.Sprintf("Kubelet version %s not supported by CCE", kubeletK8SVersion)
	}

	// 检查 Cluster 版本是否符合预期
	_, ok := supportVersions[clusterK8SVersion]
	if !ok {
		logger.Errorf(ctx, "cluster version is %s, but expect to be %v", clusterK8SVersion, supportVersions)
		return false, fmt.Sprintf("cluster version is %s, but expect to be %v", clusterK8SVersion, supportVersions)
	}

	return true, ""
}

// kubeletK8SVersionToCCEK8SVersion - v1.16.8 -> ccetypes.K8S1_16_8
func kubeletK8SVersionToCCEK8SVersion(ctx context.Context, k8sVersion string) ccetypes.K8SVersion {
	return ccetypes.K8SVersion(strings.Replace(k8sVersion, "v", "", -1))
}

// needGetK8SNodes - 是否需要查询 K8S Nodes
func needGetK8SNodes(ctx context.Context, clusterPhase ccetypes.ClusterPhase, clusterType ccetypes.ClusterType, phase string) bool {
	if clusterType == ccetypes.ClusterTypeServerless {
		return false
	}

	if phase == listInstancesPhasesRaw {
		return false
	}

	if clusterPhase == ccetypes.ClusterPhaseRunning || clusterPhase == ccetypes.ClusterPhaseUpgrading || clusterPhase == ccetypes.ClusterPhaseUpgradeFailed {
		return true
	}

	return false
}

// sortInstancesByCanBeSelected - 对 instancesSDK 排序, canBeSelected 排在前面
func sortInstancesByCanBeSelected(ctx context.Context, instancesSDK []*ccesdk.Instance) []*ccesdk.Instance {
	result := []*ccesdk.Instance{}
	canBeSelected := []*ccesdk.Instance{}
	canNotBeSelected := []*ccesdk.Instance{}

	for _, instance := range instancesSDK {
		if instance.UpgradeNodeFields.CanBeSelected {
			canBeSelected = append(canBeSelected, instance)
		} else {
			canNotBeSelected = append(canNotBeSelected, instance)
		}
	}

	result = append(result, canBeSelected...)
	result = append(result, canNotBeSelected...)

	return result
}

func CheckCCENodeNumLimit(ctx context.Context, clusterID, accountID string, addNum int, config *configuration.Config,
	models models.Interface) (bool, error) {

	// 获取 Cluster
	cluster, err := models.GetCluster(ctx, clusterID, accountID)
	if err != nil {
		logger.Errorf(ctx, "models.GetCluster failed: %s", err)
		return false, err
	}

	// 仅对托管集群且为规格集群进行检查节点数限制
	if cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeManagedPro {
		return true, nil
	}

	masterFlavor := cluster.Spec.MasterConfig.MasterFlavor
	FlavorDeployConfig, ok := config.PluginConfig.FlavorConfig.FlavorConfigMap[string(masterFlavor)]
	if !ok {
		// 不在规格里面的不阻塞
		logger.Infof(ctx, "pluginConfig FlavorConfigMap not contain %s ", string(masterFlavor))
		return true, nil
	}

	existedInstances, err := models.GetInstances(ctx, cluster.Spec.AccountID, cluster.Spec.ClusterID)
	if err != nil {
		logger.Errorf(ctx, "get instances failed : %v", err)
		return false, err
	}

	existedInstancesNum := len(existedInstances)
	nodeLimitNums := FlavorDeployConfig.ResourceLimit.NodeNum

	logger.Infof(ctx, "nodeLimitNums:%d , existedInstancesNum:%d, addNum:%d", nodeLimitNums, existedInstancesNum, addNum)

	// 集群规格节点的上限  >= 已有节点数 + 要添加节点数
	//return nodeLimitNums >= existedInstancesNum+addNum, nil

	// 先放开规格集群节点数限制
	return true, nil
}

// getDefaultXPUVersionByCreateTime 根据节点创建时间返回默认的XPU Container Toolkit版本
// 根据需求：
// - 2025-04-24~2025-06-14（北京时间）安装的1.0.4版本
// - 2025-04-24之前安装1.0.2版本
// - 2025-06-14之后默认安装最新版本1.0.5
func getDefaultXPUVersionByCreateTime(createTime metav1.Time) string {
	// 定义时间边界（北京时间）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Errorf(context.Background(), "failed to load Beijing timezone: %v", err)
		// 如果时区加载失败，返回最新版本
		return ccetypes.XPUToolkit_1_0_5
	}

	// 2025-04-24 00:00:00 北京时间
	startTime := time.Date(2025, 4, 24, 0, 0, 0, 0, beijingLocation)
	// 2025-06-14 23:59:59 北京时间
	endTime := time.Date(2025, 6, 14, 23, 59, 59, 0, beijingLocation)

	// 将节点创建时间转换为北京时间
	nodeCreateTime := createTime.Time.In(beijingLocation)

	if nodeCreateTime.Before(startTime) {
		// 2025-04-24之前创建的节点，返回1.0.2版本
		return ccetypes.XPUToolkit_1_0_2
	} else if nodeCreateTime.After(endTime) {
		// 2025-06-14之后创建的节点，返回最新版本1.0.5
		return ccetypes.XPUToolkit_1_0_5
	} else {
		// 2025-04-24~2025-06-14之间创建的节点，返回1.0.4版本
		return ccetypes.XPUToolkit_1_0_4
	}
}
