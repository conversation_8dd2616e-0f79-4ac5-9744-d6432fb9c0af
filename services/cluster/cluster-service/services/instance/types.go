package instance

import (
	"context"

	inmemCache "github.com/patrickmn/go-cache"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	ccecrd "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/image/retriever"
)

// 获取实例列表选项
type ListInstanceOptions struct {
	ClusterID       string
	InstanceGroupID string
	KeywordType     string
	Keyword         string
	// OrderBy                    string
	// Order                      string
	Phases string
	// PageNo                     int
	// PageSize                   int
	EnableInternalFields       bool
	ClusterRole                ccetypes.ClusterRole
	EnableUpgradeNodeFields    bool
	GPUType                    string
	CalculateGPUCountRequested bool
	ccesdk.PageParams
}

// 当前目录执行: mockgen -destination=mock/mock_client.go -source=./types.go -self_package=Interface -package=mock
// Interface -
// icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance
type Interface interface {
	ListInstancesByPage(ctx context.Context, clusterID, keywordType, keyword, orderBy, order, phases string,
		pageNo, pageSize int, enableInternalFields bool, clusterRole ccetypes.ClusterRole, becRegion string,
		enableUpgradeNodeFields bool, ipList []string, isK8sNodeName bool, gpuType string, calculateGPUCountRequested bool, chargingType *string) (*ccesdk.InstancePage, error)

	ListInstancesByInstanceGroup(ctx context.Context, options ListInstanceOptions) (*ccesdk.ListInstancesByInstanceGroupIDPage, error)

	// TODO 实现一个通用的列表接口
	// ListInstances(ctx context.Context, options ListInstanceOptions) (*ccesdk.InstancePage, error)

	ListInstancesByInstancesDownloadRequest(ctx context.Context, req ccesdk.InstancesDownloadRequest, clusterID, accountID string) (*ccesdk.InstancePage, error)

	ListGPUTypes(ctx context.Context, clusterID, instanceGroupID, accountID string) ([]string, error)

	// CRD
	GetInstanceCRD(ctx context.Context, cceInstanceID string) (*ccecrd.Instance, error)
	UpdateInstanceCRD(ctx context.Context, instance *ccecrd.Instance) error
	RebuildInstance(ctx context.Context, cceInstanceID, imageID string, needToRebuildOS bool) error
	UpdateInstanceScaleDownDisabled(ctx context.Context, clusterID string, req ccesdk.UpdateNodeScaleDownRequest) ([]ccesdk.FailedInstance, error)

	// cordon
	CordonNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, cordon bool) error
	GPUShareNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, gpuSharing bool, gpuHybrid bool, userID string) error

	// drain
	DrainNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, drainNodeConfig *utils.DrainNodeConfig) error

	// WithResourceCache 注入一个用于缓存集群中资源列表的缓存（node/pod cache），以减少对 APIServer 的请求，同时提升接口的响应时间
	WithResourceCache(resourceCache *inmemCache.Cache) Interface
	WithImageRetriever(imageRetriever retriever.Interface) Interface
}
