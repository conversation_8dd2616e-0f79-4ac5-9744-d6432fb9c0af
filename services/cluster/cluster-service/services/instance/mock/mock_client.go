// Code generated by MockGen. DO NOT EDIT.
// Source: ./types.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	cache "github.com/patrickmn/go-cache"
	ccev2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	utils "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	retriever "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/image/retriever"
	instance "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CordonNodes mocks base method.
func (m *MockInterface) CordonNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, cordon bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CordonNodes", ctx, clusterID, cceInstanceIDs, cordon)
	ret0, _ := ret[0].(error)
	return ret0
}

// CordonNodes indicates an expected call of CordonNodes.
func (mr *MockInterfaceMockRecorder) CordonNodes(ctx, clusterID, cceInstanceIDs, cordon interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CordonNodes", reflect.TypeOf((*MockInterface)(nil).CordonNodes), ctx, clusterID, cceInstanceIDs, cordon)
}

// DrainNodes mocks base method.
func (m *MockInterface) DrainNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, drainNodeConfig *utils.DrainNodeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DrainNodes", ctx, clusterID, cceInstanceIDs, drainNodeConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// DrainNodes indicates an expected call of DrainNodes.
func (mr *MockInterfaceMockRecorder) DrainNodes(ctx, clusterID, cceInstanceIDs, drainNodeConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DrainNodes", reflect.TypeOf((*MockInterface)(nil).DrainNodes), ctx, clusterID, cceInstanceIDs, drainNodeConfig)
}

// GPUShareNodes mocks base method.
func (m *MockInterface) GPUShareNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, gpuSharing, gpuHybrid bool, userID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GPUShareNodes", ctx, clusterID, cceInstanceIDs, gpuSharing, gpuHybrid, userID)
	ret0, _ := ret[0].(error)
	return ret0
}

// GPUShareNodes indicates an expected call of GPUShareNodes.
func (mr *MockInterfaceMockRecorder) GPUShareNodes(ctx, clusterID, cceInstanceIDs, gpuSharing, gpuHybrid, userID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GPUShareNodes", reflect.TypeOf((*MockInterface)(nil).GPUShareNodes), ctx, clusterID, cceInstanceIDs, gpuSharing, gpuHybrid, userID)
}

// GetInstanceCRD mocks base method.
func (m *MockInterface) GetInstanceCRD(ctx context.Context, cceInstanceID string) (*v1.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceCRD", ctx, cceInstanceID)
	ret0, _ := ret[0].(*v1.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceCRD indicates an expected call of GetInstanceCRD.
func (mr *MockInterfaceMockRecorder) GetInstanceCRD(ctx, cceInstanceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceCRD", reflect.TypeOf((*MockInterface)(nil).GetInstanceCRD), ctx, cceInstanceID)
}

// ListGPUTypes mocks base method.
func (m *MockInterface) ListGPUTypes(ctx context.Context, clusterID, instanceGroupID, accountID string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGPUTypes", ctx, clusterID, instanceGroupID, accountID)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListGPUTypes indicates an expected call of ListGPUTypes.
func (mr *MockInterfaceMockRecorder) ListGPUTypes(ctx, clusterID, instanceGroupID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGPUTypes", reflect.TypeOf((*MockInterface)(nil).ListGPUTypes), ctx, clusterID, instanceGroupID, accountID)
}

// ListInstancesByInstanceGroup mocks base method.
func (m *MockInterface) ListInstancesByInstanceGroup(ctx context.Context, options instance.ListInstanceOptions) (*ccev2.ListInstancesByInstanceGroupIDPage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByInstanceGroup", ctx, options)
	ret0, _ := ret[0].(*ccev2.ListInstancesByInstanceGroupIDPage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByInstanceGroup indicates an expected call of ListInstancesByInstanceGroup.
func (mr *MockInterfaceMockRecorder) ListInstancesByInstanceGroup(ctx, options interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByInstanceGroup", reflect.TypeOf((*MockInterface)(nil).ListInstancesByInstanceGroup), ctx, options)
}

// ListInstancesByInstancesDownloadRequest mocks base method.
func (m *MockInterface) ListInstancesByInstancesDownloadRequest(ctx context.Context, req ccev2.InstancesDownloadRequest, clusterID, accountID string) (*ccev2.InstancePage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByInstancesDownloadRequest", ctx, req, clusterID, accountID)
	ret0, _ := ret[0].(*ccev2.InstancePage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByInstancesDownloadRequest indicates an expected call of ListInstancesByInstancesDownloadRequest.
func (mr *MockInterfaceMockRecorder) ListInstancesByInstancesDownloadRequest(ctx, req, clusterID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByInstancesDownloadRequest", reflect.TypeOf((*MockInterface)(nil).ListInstancesByInstancesDownloadRequest), ctx, req, clusterID, accountID)
}

// ListInstancesByPage mocks base method.
func (m *MockInterface) ListInstancesByPage(ctx context.Context, clusterID, keywordType, keyword, orderBy, order, phases string, pageNo, pageSize int, enableInternalFields bool, clusterRole ccetypes.ClusterRole, becRegion string, enableUpgradeNodeFields bool, ipList []string, isK8sNodeName bool, gpuType string, calculateGPUCountRequested bool, chargingType *string) (*ccev2.InstancePage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstancesByPage", ctx, clusterID, keywordType, keyword, orderBy, order, phases, pageNo, pageSize, enableInternalFields, clusterRole, becRegion, enableUpgradeNodeFields, ipList, isK8sNodeName, gpuType, calculateGPUCountRequested, chargingType)
	ret0, _ := ret[0].(*ccev2.InstancePage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstancesByPage indicates an expected call of ListInstancesByPage.
func (mr *MockInterfaceMockRecorder) ListInstancesByPage(ctx, clusterID, keywordType, keyword, orderBy, order, phases, pageNo, pageSize, enableInternalFields, clusterRole, becRegion, enableUpgradeNodeFields, ipList, isK8sNodeName, gpuType, calculateGPUCountRequested, chargingType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstancesByPage", reflect.TypeOf((*MockInterface)(nil).ListInstancesByPage), ctx, clusterID, keywordType, keyword, orderBy, order, phases, pageNo, pageSize, enableInternalFields, clusterRole, becRegion, enableUpgradeNodeFields, ipList, isK8sNodeName, gpuType, calculateGPUCountRequested, chargingType)
}

// RebuildInstance mocks base method.
func (m *MockInterface) RebuildInstance(ctx context.Context, cceInstanceID, imageID string, needToRebuildImage bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RebuildInstance", ctx, cceInstanceID, imageID, false)
	ret0, _ := ret[0].(error)
	return ret0
}

// RebuildInstance indicates an expected call of RebuildInstance.
func (mr *MockInterfaceMockRecorder) RebuildInstance(ctx, cceInstanceID, imageID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildInstance", reflect.TypeOf((*MockInterface)(nil).RebuildInstance), ctx, cceInstanceID, imageID)
}

// UpdateInstanceCRD mocks base method.
func (m *MockInterface) UpdateInstanceCRD(ctx context.Context, instance *v1.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceCRD", ctx, instance)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceCRD indicates an expected call of UpdateInstanceCRD.
func (mr *MockInterfaceMockRecorder) UpdateInstanceCRD(ctx, instance interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceCRD", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceCRD), ctx, instance)
}

// UpdateInstanceScaleDownDisabled mocks base method.
func (m *MockInterface) UpdateInstanceScaleDownDisabled(ctx context.Context, clusterID string, req ccev2.UpdateNodeScaleDownRequest) ([]ccev2.FailedInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceScaleDownDisabled", ctx, clusterID, req)
	ret0, _ := ret[0].([]ccev2.FailedInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateInstanceScaleDownDisabled indicates an expected call of UpdateInstanceScaleDownDisabled.
func (mr *MockInterfaceMockRecorder) UpdateInstanceScaleDownDisabled(ctx, clusterID, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceScaleDownDisabled", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceScaleDownDisabled), ctx, clusterID, req)
}

// WithImageRetriever mocks base method.
func (m *MockInterface) WithImageRetriever(imageRetriever retriever.Interface) instance.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithImageRetriever", imageRetriever)
	ret0, _ := ret[0].(instance.Interface)
	return ret0
}

// WithImageRetriever indicates an expected call of WithImageRetriever.
func (mr *MockInterfaceMockRecorder) WithImageRetriever(imageRetriever interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithImageRetriever", reflect.TypeOf((*MockInterface)(nil).WithImageRetriever), imageRetriever)
}

// WithResourceCache mocks base method.
func (m *MockInterface) WithResourceCache(resourceCache *cache.Cache) instance.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithResourceCache", resourceCache)
	ret0, _ := ret[0].(instance.Interface)
	return ret0
}

// WithResourceCache indicates an expected call of WithResourceCache.
func (mr *MockInterfaceMockRecorder) WithResourceCache(resourceCache interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithResourceCache", reflect.TypeOf((*MockInterface)(nil).WithResourceCache), resourceCache)
}
