package addons

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	crdv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/addon"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/helm/helm-service/clients/helmclient"
	corev1 "k8s.io/api/core/v1"
	sigyaml "sigs.k8s.io/yaml"
)

func init() {
	RegisterNewAddOn(CCECSIRapidFSAddOnName,
		&AddonRecord{
			Name:             CCECSIRapidFSAddOnName,
			HelmChartName:    CCECSIRapidFSAddOnHelmChartName,
			ShortDescription: "支持在Pod中以PV/PVC方式挂载百度云RapidFS存储，并支持动态创建PV",
			Type:             ccesdk.TypeStorage,
			AppliedK8sVersions: []AppliedK8sVersion{
				{
					compareOperator: greaterOrEqual,
					targetVersion:   ccetypes.K8S_1_18_9,
				},
			},
			IsSupportArm64: false,
		})
}

const (
	CCECSIRapidFSAddOnName          = "cce-csi-rapidfs-plugin"
	CCECSIRapidFSInstallNamespace   = "kube-system"
	CCECSIRapidFSAddOnHelmChartName = "cce-csi-rapidfs-plugin"

	CCECSIRapidFSInstanceName = "cce-csi-rapidfs-plugin"
)

type ResourceRequirements struct {
	Requests map[string]string `yaml:"requests,omitempty" json:"requests,omitempty"`
	Limits   map[string]string `yaml:"limits,omitempty" json:"limits,omitempty"`
}

type NodeConfig struct {
	KubeletRootPath         string `yaml:"kubeletRootPath" json:"kubeletRootPath" validate:"required"`
	KubeletRootPathAffinity bool   `yaml:"kubeletRootPathAffinity,omitempty" json:"kubeletRootPathAffinity,omitempty"`
}

type ClusterConfig struct {
	Nodes []NodeConfig `yaml:"nodes" json:"nodes" validate:"required,min=1,dive"`
}
type RapidFSPluginParams struct {
	Cluster              ClusterConfig `yaml:"cluster" json:"cluster" validate:"required"`
	RapidfsWorkspaceRoot string        `yaml:"rapidfsWorkspaceRoot" json:"rapidfsWorkspaceRoot" validate:"required"`
	// 这里不使用原生的corev1包，因为当前的helm包中的values的100Mi等资源参数无法解析为corev1包格式。
	ControllerServerResources   *ResourceRequirements `yaml:"controllerServerResources,omitempty" json:"controllerServerResources,omitempty"`
	NodeServerResources         *ResourceRequirements `yaml:"nodeServerResources,omitempty" json:"nodeServerResources,omitempty"`
	ControllerServerTolerations []corev1.Toleration   `yaml:"controllerServerTolerations,omitempty" json:"controllerServerTolerations,omitempty"`
	// 这里不使用原生的corev1.NodeSelector, 因为这个是代表requiredDuringSchedulingIgnoredDuringExecution，而不是简单的kv的nodeselector结构。
	ControllerServerSelector map[string]string   `yaml:"controllerServerSelector,omitempty" json:"controllerServerSelector,omitempty"`
	NodeServerTolerations    []corev1.Toleration `yaml:"nodeServerTolerations,omitempty" json:"nodeServerTolerations,omitempty"`
	NodeSelector             map[string]string   `yaml:"nodeSelector,omitempty" json:"nodeSelector,omitempty"`
}

// convertYAMLToJSON converts YAML string to JSON string
// This is used to convert Helm's internal YAML values to JSON format for API responses
func convertYAMLToJSON(yamlStr string) (string, error) {
	if yamlStr == "" {
		return "", nil
	}

	// 直接解析为RapidFSPluginParams结构体
	var params RapidFSPluginParams
	// 这里需要换用sigyaml, 因为yamlv2/v3对字符串的处理只会匹配yaml标签，然后匹配全小写的字符，遇到"tolerationSeconds"的驼峰格式无法识别，而
	// k8s官方库的sigyaml可以正确处理。
	if err := sigyaml.Unmarshal([]byte(yamlStr), &params); err != nil {
		return "", fmt.Errorf("failed to parse YAML as RapidFSPluginParams: %v", err)
	}

	// 转换为JSON
	jsonBytes, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("failed to marshal to JSON: %v", err)
	}

	return string(jsonBytes), nil
}

// validateRapidFSParams validates RapidFS plugin parameters
func validateRapidFSParams(params *RapidFSPluginParams) error {
	if params == nil {
		return errors.New("params cannot be nil")
	}

	if len(params.Cluster.Nodes) == 0 {
		return errors.New("cluster.nodes cannot be empty")
	}

	for i, node := range params.Cluster.Nodes {
		if node.KubeletRootPath == "" {
			return fmt.Errorf("cluster.nodes[%d].kubeletRootPath is required", i)
		}
	}

	if params.RapidfsWorkspaceRoot == "" {
		return errors.New("rapidfsWorkspaceRoot is required")
	}

	return nil
}

// parseParamsFromJSON parses JSON string to RapidFSPluginParams
func parseParamsFromJSON(jsonStr string) (*RapidFSPluginParams, error) {
	if jsonStr == "" {
		return nil, nil
	}

	var params RapidFSPluginParams
	if err := json.Unmarshal([]byte(jsonStr), &params); err != nil {
		return nil, fmt.Errorf("failed to parse JSON params: %v", err)
	}

	if err := validateRapidFSParams(&params); err != nil {
		return nil, fmt.Errorf("invalid params: %v", err)
	}

	return &params, nil
}

type AddOnCSIRapidFSPlugin struct {
	cluster    *crdv1.Cluster
	helmClient helmclient.ClientItf

	repoURL string

	VirtualAddon // 虚拟插件，用于减少冗余代码
}

func NewAddOnCSIRapidFSPlugin(cluster *crdv1.Cluster, helmClient helmclient.ClientItf, repoURL string) (addon.AddOn, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}
	if helmClient == nil {
		return nil, errors.New("helm client is nil")
	}

	addonCSIRapidFSPlugin := &AddOnCSIRapidFSPlugin{
		cluster:    cluster,
		helmClient: helmClient,
		repoURL:    repoURL,
	}

	return addonCSIRapidFSPlugin, nil
}

// Status 返回组件完整的元数据和状态
func (addonCSIRapidFSPlugin *AddOnCSIRapidFSPlugin) Status(ctx context.Context, accountID string, userID string) (*ccesdk.AddOnInfo, error) {
	// Part 1 获取组件元信息
	metaInfo, err := GetAddonMetaWitchCache(ctx, addonCSIRapidFSPlugin.MetaCache, addonCSIRapidFSPlugin.helmClient,
		addonCSIRapidFSPlugin.repoURL, "", CCECSIRapidFSAddOnName)
	if err != nil {
		logger.Errorf(ctx, "Fail to get add on meta: %s", err)
		return nil, err
	}

	// 将defaultParams从YAML转换为JSON
	if metaInfo.DefaultParams != "" {
		jsonParams, err := convertYAMLToJSON(metaInfo.DefaultParams)
		if err != nil {
			logger.Errorf(ctx, "Failed to convert defaultParams from YAML to JSON: %s", err)
			return nil, err
		}
		metaInfo.DefaultParams = jsonParams
	}
	// 获取组件能否在集群中安装的信息
	metaInfo.InstallInfo = ccesdk.InstallInfo{
		AllowInstall: true,
	}

	allowInstall, _, message, err := checkBasicInstallInfo(ctx, addonCSIRapidFSPlugin.cluster, "", CCECSIRapidFSAddOnName)
	if err != nil {
		return nil, err
	}
	if !allowInstall {
		metaInfo.InstallInfo = ccesdk.InstallInfo{
			AllowInstall: false,
			Message:      message,
		}
	}

	logger.Infof(ctx, "Get addon meta info: %s", metaInfo)

	// 如果集群还没有创建出来 检查到此为止 直接返回
	if addonCSIRapidFSPlugin.cluster.Status.ClusterPhase == "" {
		addonInfo := &ccesdk.AddOnInfo{
			Meta: *metaInfo,
		}
		return addonInfo, nil
	}

	// Part 2 搜集和整理组件部署信息
	args := ListReleaseDetailArgs{
		// 防止用户通过helm安装过组件，因此不限制FilterStr==CCECSIRapidFSInstanceName,TargetReleaseName: CCECSIRapidFSInstanceName和Namespace==CCECSIRapidFSInstallNamespace
		TargetChartName: CCECSIRapidFSAddOnHelmChartName,
	}
	releaseDetail, err := GetAddonHelmReleaseSingleton(ctx, addonCSIRapidFSPlugin.helmClient, args)
	if err != nil {
		logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
		return nil, err
	}

	var addonInstance *ccesdk.AddOnInstance
	if releaseDetail != nil {
		status, err := ConvertHelmPhaseToAddonPhase(ctx, releaseDetail)
		if err != nil {
			return nil, err
		}

		// 将instance params从YAML转换为JSON格式
		var instanceParamsJSON string
		if releaseDetail.Values != "" {
			// 将YAML转换为JSON
			jsonParams, err := convertYAMLToJSON(releaseDetail.Values)
			if err != nil {
				logger.Errorf(ctx, "Failed to convert instance params from YAML to JSON: %s", err)
				return nil, err
			}
			instanceParamsJSON = jsonParams
		}

		addonInstance = &ccesdk.AddOnInstance{
			AddOnInstanceName: releaseDetail.ListRelease.Name,
			InstalledVersion:  releaseDetail.ChartVersion,
			Params:            instanceParamsJSON,

			Status: status,
			UninstallInfo: ccesdk.UninstallInfo{
				AllowUninstall: true,
			},
			UpdateInfo: ccesdk.UpdateInfo{
				AllowUpdate: true,
			},
		}
		// 设置升级信息
		if releaseDetail.ChartVersion != metaInfo.LatestVersion {
			addonInstance.UpgradeInfo = ccesdk.UpgradeInfo{
				AllowUpgrade: true,
				NextVersion:  metaInfo.LatestVersion,
			}
		} else {
			addonInstance.UpgradeInfo = ccesdk.UpgradeInfo{
				AllowUpgrade: false,
				Message:      "已经是最新版本",
			}
		}
	}

	// 整理数据并返回
	addonInfo := &ccesdk.AddOnInfo{
		Meta:     *metaInfo,
		Instance: addonInstance,
	}

	logger.Infof(ctx, "Addon Info: %s", addonInfo)
	return addonInfo, nil
}

// Install 安装组件
func (addonCSIRapidFSPlugin *AddOnCSIRapidFSPlugin) Install(ctx context.Context, installConfig *addon.InstallConfig, accountID string, userID string) error {
	if installConfig == nil {
		return errors.New("install config is nil")
	}

	version := installConfig.Version
	userParams := installConfig.Params

	// 1. 解析和验证用户参数
	if userParams != "" {
		_, err := parseParamsFromJSON(userParams)
		if err != nil {
			logger.Errorf(ctx, "Failed to parse install params: %s", err)
			return fmt.Errorf("invalid install params: %s", err)
		}
	}

	// 2. 整理安装参数并调用 Helm 安装
	installOption := &helmclient.InstallOption{
		Name:        CCECSIRapidFSInstanceName,
		Namespace:   CCECSIRapidFSInstallNamespace,
		Description: "deployed by cce",
		RepoURL:     addonCSIRapidFSPlugin.repoURL,
		Values:      userParams, // 直接使用JSON格式参数
	}
	if len(version) != 0 {
		logger.Infof(ctx, "Install version set by user is %s", version)
		installOption.ChartVersion = version
	}

	logger.Infof(ctx, "Install Params: %+v", installConfig)
	if _, err := addonCSIRapidFSPlugin.helmClient.Install(ctx, CCECSIRapidFSAddOnHelmChartName, installOption); err != nil {
		logger.Errorf(ctx, "Fail to install: %s", err)
		return err
	}

	return nil
}

// Uninstall 卸载组件
func (addonCSIRapidFSPlugin *AddOnCSIRapidFSPlugin) Uninstall(ctx context.Context, uninstallConfig *addon.UninstallConfig, accountID string, userID string) error {
	// 卸载指定的组件实例
	args := ListReleaseDetailArgs{
		FilterStr:         CCECSIRapidFSInstanceName,
		TargetReleaseName: CCECSIRapidFSInstanceName,
		TargetChartName:   CCECSIRapidFSAddOnHelmChartName,
		Namespace:         CCECSIRapidFSInstallNamespace,
	}
	releaseDetail, err := GetAddonHelmReleaseSingleton(ctx, addonCSIRapidFSPlugin.helmClient, args)
	if err != nil {
		logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
		return err
	}

	if releaseDetail == nil {
		// 扩大搜索范围 防止用户通过helm安装过组件
		args = ListReleaseDetailArgs{
			TargetChartName: CCECSIRapidFSAddOnHelmChartName,
		}
		releaseDetail, err = GetAddonHelmReleaseSingletonByChartName(ctx, addonCSIRapidFSPlugin.helmClient, args)
		if err != nil {
			logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
			return err
		}
		if releaseDetail == nil {
			logger.Errorf(ctx, "No release found")
			return errors.New("no %s release found, no need to delete")
		}
	}

	if releaseDetail.ListRelease.Name == CCECSIRapidFSInstanceName {
		// 卸载指定的组件实例
		if err := addonCSIRapidFSPlugin.helmClient.Delete(ctx, CCECSIRapidFSInstanceName, &helmclient.DeleteOption{
			Namespace: releaseDetail.ListRelease.Namespace,
			Purge:     true, // 不保留安装记录 以防用户二次安装产生冲突
		}); err != nil {
			logger.Errorf(ctx, "Failed uninstall %s at namespace %s ", CCECSIRapidFSInstanceName, releaseDetail.ListRelease.Namespace)
			return err
		}
		logger.Infof(ctx, "Uninstall success")
	} else if strings.Contains(releaseDetail.ListRelease.Chart, CCECSIRapidFSInstanceName) {
		// 卸载指定的组件实例
		if err := addonCSIRapidFSPlugin.helmClient.Delete(ctx, releaseDetail.ListRelease.Name, &helmclient.DeleteOption{
			Namespace: releaseDetail.ListRelease.Namespace,
			Purge:     true, // 不保留安装记录 以防用户二次安装产生冲突
		}); err != nil {
			logger.Errorf(ctx, "Failed uninstall %s at namespace %s ", releaseDetail.ListRelease.Name, releaseDetail.ListRelease.Namespace)
			return err
		}
		logger.Infof(ctx, "Uninstall success")
	}

	return nil
}

// Upgrade 升级组件
func (addonCSIRapidFSPlugin *AddOnCSIRapidFSPlugin) Upgrade(ctx context.Context, upgradeConfig *addon.UpgradeConfig, accountID string, userID string) error {
	if upgradeConfig == nil {
		return errors.New("install config is nil")
	}

	// 1. 获取已经安装的实例
	args := ListReleaseDetailArgs{
		// 防止用户通过helm安装过组件，因此不限制FilterStr==CCECSIRapidFSInstanceName,TargetReleaseName: CCECSIRapidFSInstanceName和Namespace==CCECSIRapidFSInstallNamespace
		TargetChartName: CCECSIRapidFSAddOnHelmChartName,
	}
	releaseDetail, err := GetAddonHelmReleaseSingleton(ctx, addonCSIRapidFSPlugin.helmClient, args)
	if err != nil {
		logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
		return err
	}

	// 如果没有找到release，直接返回
	if releaseDetail == nil {
		logger.Infof(ctx, "No release found for upgrade")
		return nil
	}

	// 2. 解析和验证用户参数
	if upgradeConfig.Params != "" {
		_, err := parseParamsFromJSON(upgradeConfig.Params)
		if err != nil {
			logger.Errorf(ctx, "Failed to parse upgrade params: %s", err)
			return fmt.Errorf("invalid upgrade params: %s", err)
		}
	}

	// 3. 整理更新参数并调用 Helm 更新
	targetVersion := "" // Version 为空值时，helm 默认升级到最新版本
	if upgradeConfig.TargetVersion != "" {
		targetVersion = upgradeConfig.TargetVersion
	}

	upgradeOption := &helmclient.UpgradeOption{
		Namespace:    CCECSIRapidFSInstallNamespace,
		RepoURL:      addonCSIRapidFSPlugin.repoURL,
		ChartVersion: targetVersion,
		Values:       upgradeConfig.Params, // 直接使用JSON格式参数
	}

	if releaseDetail.ListRelease.Name == CCECSIRapidFSInstanceName {
		logger.Infof(ctx, "Current Release: %+v", releaseDetail)
		logger.Infof(ctx, "Upgrade Params: %+v", upgradeOption)
		if err := addonCSIRapidFSPlugin.helmClient.Upgrade(ctx, CCECSIRapidFSInstanceName, CCECSIRapidFSAddOnHelmChartName, upgradeOption); err != nil {
			logger.Errorf(ctx, "Fail to install: %s", err)
			return err
		}
	} else if strings.Contains(releaseDetail.ListRelease.Chart, CCECSIRapidFSInstanceName) {
		// 先卸载旧版本，再安装新版本
		if err := addonCSIRapidFSPlugin.helmClient.Delete(ctx, releaseDetail.ListRelease.Name, &helmclient.DeleteOption{
			Namespace: releaseDetail.ListRelease.Namespace,
			Purge:     true, // 不保留安装记录 以防用户二次安装产生冲突
		}); err != nil {
			logger.Errorf(ctx, "Failed uninstall %s at namespace %s ", releaseDetail.ListRelease.Name, releaseDetail.ListRelease.Namespace)
			return err
		}

		logger.Infof(ctx, "Current Release: %+v", releaseDetail)
		// 重新安装
		installOption := &helmclient.InstallOption{
			Name:        CCECSIRapidFSInstanceName,
			Namespace:   CCECSIRapidFSInstallNamespace,
			Description: "deployed by cce",
			RepoURL:     addonCSIRapidFSPlugin.repoURL,
			Values:      upgradeOption.Values,
		}
		logger.Infof(ctx, "Upgrade ReInstall Params: %+v", installOption)

		if _, err := addonCSIRapidFSPlugin.helmClient.Install(ctx, CCECSIRapidFSAddOnHelmChartName, installOption); err != nil {
			logger.Errorf(ctx, "Fail to install: %s", err)
			return err
		}

	}

	return nil
}

// Update 更新组件参数
func (addonCSIRapidFSPlugin *AddOnCSIRapidFSPlugin) Update(ctx context.Context, updateConfig *addon.UpdateConfig, accountID string, userID string) error {
	if updateConfig == nil {
		return errors.New("install config is nil")
	}

	// 1. 获取已经安装的实例
	args := ListReleaseDetailArgs{
		FilterStr:         CCECSIRapidFSInstanceName,
		TargetReleaseName: CCECSIRapidFSInstanceName,
		TargetChartName:   CCECSIRapidFSAddOnHelmChartName,
		Namespace:         CCECSIRapidFSInstallNamespace,
	}
	releaseDetail, err := GetAddonHelmReleaseSingleton(ctx, addonCSIRapidFSPlugin.helmClient, args)
	if err != nil {
		logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
		return err
	}

	if releaseDetail == nil {
		logger.Warnf(ctx, "No release found")
		// 查看是否有旧版本
		args = ListReleaseDetailArgs{
			TargetChartName: CCECSIRapidFSInstanceName,
		}
		releaseDetail, err = GetAddonHelmReleaseSingletonByChartName(ctx, addonCSIRapidFSPlugin.helmClient, args)
		if err != nil {
			logger.Errorf(ctx, "Fail to GetAddonHelmRelease: %s", err)
			return err
		}
		if releaseDetail == nil {
			logger.Errorf(ctx, "No release found")
			return errors.New("no %s release found, no need to update")
		}
	}

	// 2. 解析和验证用户参数
	if updateConfig.Params != "" {
		_, err := parseParamsFromJSON(updateConfig.Params)
		if err != nil {
			logger.Errorf(ctx, "Failed to parse update params: %s", err)
			return fmt.Errorf("invalid update params: %s", err)
		}
	}

	// 3. 整理更新参数并调用 Helm 更新
	if releaseDetail.ListRelease.Name == CCECSIRapidFSInstanceName {
		upgradeOption := &helmclient.UpgradeOption{
			Namespace:    CCECSIRapidFSInstallNamespace,
			RepoURL:      addonCSIRapidFSPlugin.repoURL,
			Values:       updateConfig.Params, // 直接使用JSON格式参数
			ChartVersion: releaseDetail.ChartVersion,
		}

		logger.Infof(ctx, "Current Release: %+v", releaseDetail)
		logger.Infof(ctx, "Update Params: %+v", upgradeOption)
		if err := addonCSIRapidFSPlugin.helmClient.Upgrade(ctx, CCECSIRapidFSInstanceName, CCECSIRapidFSAddOnHelmChartName, upgradeOption); err != nil {
			logger.Errorf(ctx, "Fail to install: %s", err)
			return err
		}
	} else if strings.Contains(releaseDetail.ListRelease.Chart, CCECSIRapidFSInstanceName) {
		upgradeOption := &helmclient.UpgradeOption{
			Namespace:    releaseDetail.ListRelease.Namespace,
			RepoURL:      addonCSIRapidFSPlugin.repoURL,
			Values:       updateConfig.Params, // 直接使用JSON格式参数
			ChartVersion: releaseDetail.ChartVersion,
		}

		logger.Infof(ctx, "Current Release: %+v", releaseDetail)
		logger.Infof(ctx, "Update Params: %+v", upgradeOption)

		if releaseDetail.ChartName != "" {
			if err = addonCSIRapidFSPlugin.helmClient.Upgrade(ctx, releaseDetail.ListRelease.Name, releaseDetail.ChartName, upgradeOption); err != nil {
				logger.Errorf(ctx, "Fail to install: %s", err)
				return err
			}
		}
	}

	return nil
}
