package controllers

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/constant"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	inmemCache "github.com/patrickmn/go-cache"
	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	uerrors "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/securitygroup"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	appnode "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/app/app-service/resource/node"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/image/retriever"
	instancep "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/userscript"
	clusterserviceutils "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/utils"
	nautil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/nodeagent/util"
)

const (
	taintKeyNodeUnschedulable = "node.kubernetes.io/unschedulable"
)

var (
	specCache     *inmemCache.Cache // artifact service machine spec缓存
	resourceCache *inmemCache.Cache
	once          sync.Once
)

func init() {
	once.Do(func() {
		if resourceCache == nil {
			resourceCache = inmemCache.New(
				instancep.ResourceCacheDefaultExpiration,
				instancep.ResourceCacheDefaultPurgeInterval)
		}
		if specCache == nil {
			// 12小时过期，过期的key每24小时清理一次
			specCache = inmemCache.New(12*time.Hour, 24*time.Hour)
		}
	})
}

// InstanceController instance controller
type InstanceController struct {
	BaseController

	instanceGroupService instancegroup.Interface
}

// Prepare run before HandlerFunc
func (c *InstanceController) Prepare() {
	c.BaseController.Prepare()
	service, err := instancegroup.NewService(c.ctx, c.accountID, c.userID, c.clients, c.models, c.config, c.clientSet)
	if err != nil {
		logger.Errorf(c.ctx, "failed to create instanceGroup instanceGroupService, err: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	c.instanceGroupService = service
}

// GetInstanceByNodeName - 通过 NodeName 获取 Instance
func (c *InstanceController) GetInstanceByNodeName() {
	ctx := c.ctx

	// 获取集群 ID
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "ClusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
	}

	// 获取节点名称
	nodeName := c.Ctx.Input.Param(":nodeName")
	if clusterID == "" {
		logger.Errorf(ctx, "NodeName is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "nodeName is empty")
	}
	logger.Infof(ctx, "GetInstanceByNodeName clusterID %s NodeName %s", clusterID, nodeName)

	// 确定集群的名称模式 IP 或 Hostname
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewClusterNotFound(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}
	isHostNameMode := cluster.Spec.K8SCustomConfig.EnableHostname
	logger.Infof(ctx, "GetInstanceByNodeName cluster EnableHostname %s", isHostNameMode)

	// Node name 模式为 HostName 或 IP 时使用不同的策略进行查询
	var instance *models.Instance
	if isHostNameMode {
		instance, err = c.models.GetInstanceByClusterIDAndHostname(ctx, clusterID, nodeName)
		if err != nil && !models.IsNotExist(err) {
			logger.Errorf(ctx, "GetInstance failed by hostname: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
		if (err != nil && models.IsNotExist(err)) || cluster == nil {
			logger.Infof(ctx, "Fail to get instance of node name %s by instance hostname, will try instance name", nodeName)
			instance, err = c.models.GetInstanceByClusterIDAndInstanceName(ctx, clusterID, nodeName)
			if err != nil {
				logger.Errorf(ctx, "GetInstance failed by instance name: %v", err)
				c.errorHandlerV2(errorcode.NewInstanceNotFound(err.Error()), errorcode.LevelByAdmin, err.Error())
			}
		}
	} else {
		instance, err = c.models.GetInstanceByClusterIDAndVPCIP(ctx, clusterID, nodeName)
		if err != nil {
			logger.Errorf(ctx, "GetInstance failed: %v", err)
			c.errorHandlerV2(errorcode.NewInstanceNotFound(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}
	logger.Infof(ctx, "models.Instance: %s", utils.ToJSON(instance))

	// TODO: 从参数传入
	enableInternalFields := false
	result, err := instancep.InstanceModelToSDK(ctx, cluster, instance, enableInternalFields, false)
	if err != nil {
		logger.Errorf(ctx, "instanceModelToSDK failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}
	logger.Infof(ctx, "Instance: %s", utils.ToJSON(result))

	// Add Properties Not in DB
	err = c.addInstancePropertiesNotInDB(ctx, cluster, result)
	if err != nil {
		logger.Errorf(ctx, "addInstancePropertiesNotInDB failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if err := c.loadUserScript(ctx, result); err != nil {
		logger.Errorf(ctx, "loadUserScript failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	// 返回结果
	c.Data["json"] = ccesdk.GetInstanceByNodeNameResponse{
		Instance:  result,
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// addInstancePropertiesNotInDB Add properties in instance which not write to DB
func (c *InstanceController) addInstancePropertiesNotInDB(ctx context.Context, cluster *models.Cluster, instance *ccesdk.Instance) error {
	if instance == nil {
		return errors.New("instance is nil")
	}
	if cluster == nil {
		return errors.New("instance is nil")
	}

	instanceInMeta, err := c.clientSet.MetaClient.GetInstance(ctx, "default", instance.Spec.CCEInstanceID, &metav1.GetOptions{})
	if err != nil {
		logger.Infof(ctx, "Failed to get instance for meta-cluster: %s", instanceInMeta)
		return err
	}

	instance.Spec.NeedGPU = &instanceInMeta.Spec.NeedGPU

	logger.Infof(ctx, "Got instance properties not in db instance: instance.Spec.NeedGPU = %s", strconv.FormatBool(*instance.Spec.NeedGPU))

	instance.Spec.IsOpenHostnameDomain = instanceInMeta.Spec.IsOpenHostnameDomain
	instance.Spec.VPCConfig.SecurityGroup = ccesdk.SecurityGroup{
		EnableCCERequiredSecurityGroup: instanceInMeta.Spec.SecurityGroup.EnableCCERequiredSecurityGroup,
		EnableCCEOptionalSecurityGroup: instanceInMeta.Spec.SecurityGroup.EnableCCEOptionalSecurityGroup,
		CustomSecurityGroupIDs:         instanceInMeta.Spec.SecurityGroup.CustomSecurityGroupIDs,
	}
	instance.Spec.SecurityGroupType = string(instanceInMeta.Spec.SecurityGroupType)
	instance.Spec.VPCConfig.SecurityGroups = instanceInMeta.Spec.SecurityGroups
	instance.Spec.UserData = instanceInMeta.Spec.UserData
	// ehc 集群信息
	instance.Spec.EhcClusterID = instanceInMeta.Spec.EhcClusterID
	// ai-infra 信息
	instance.Spec.AIInfraOption = &ccetypes.AIInfraOption{
		TemplateID: instanceInMeta.Spec.AIInfraOption.TemplateID,

		// infra cluster id: c-xxx
		ClusterName: cluster.Spec.ClusterName,
	}
	instance.Spec.NvidiaContainerToolkitVersion = instanceInMeta.Spec.NvidiaContainerToolkitVersion
	// 更新xpuContainerToolkitVersion信息
	instance.Spec.XPUContainerToolkitVersion = instanceInMeta.Spec.XPUContainerToolkitVersion
	scaleDown := instanceInMeta.Spec.ScaleDownDisabled
	instance.Spec.ScaleDownDisabled = &scaleDown
	logger.Infof(ctx, "Got instance properties not in db instance: instance.Spec.IsOpenHostnameDomain = %s", strconv.FormatBool(instance.Spec.IsOpenHostnameDomain))

	return nil
}

func (c *InstanceController) VerifyInstancesDependency(ctx context.Context, clusterID string, nodeSpecs []*ccesdk.InstanceSet) (int, error) {
	var cnt int = 0
	for _, nodeSpec := range nodeSpecs {
		var instinfo *bcc.Instance
		var ENIName string
		var err error

		if nodeSpec.InstanceSpec.MachineType != ccetypes.MachineTypeBCC && nodeSpec.InstanceSpec.MachineType != ccetypes.MachineTypeEBC {
			// It's an invalid input only for this func, no need to cancel entire process
			logger.Warnf(ctx, "Invalid nodeSpec:[%+v]", nodeSpec)
			continue
		}

		if nodeSpec.InstanceSpec.Existed != true || len(nodeSpec.InstanceSpec.ExistedOption.ExistedInstanceID) <= 0 {
			// It's an invalid input only for this func, no need to cancel entire process
			logger.Warnf(ctx, "Invalid nodeSpec:[%+v]", nodeSpec)
			continue
		}

		instinfo, err = c.clients.BCCClient.DescribeInstance(ctx, nodeSpec.InstanceSpec.ExistedOption.ExistedInstanceID, c.clients.STSClient.NewSignOption(ctx, c.userID))
		if err != nil {
			logger.Warnf(ctx, "ENIName verify. DescribeInstance failed. InstanceID:[%v] err:[%+v]",
				nodeSpec.InstanceSpec.ExistedOption.ExistedInstanceID, err)
			continue
		}

		ENIName = nautil.CreateNameForENI(clusterID, instinfo.InstanceID, instinfo.Hostname)

		if len(ENIName) > eni.ENINameLengthMax {
			logger.Warnf(ctx, "ENIName verify. ENI Name length exceed. ENI Name:[%+v]", ENIName)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser,
				fmt.Sprintf("CreateENI validate failed: ENI Name exceed length limit(%d). ENI name consist by HostName and Suffix, Try shorten HostName to make ENI Name length valid. Current ENI Name:[%s]", eni.ENINameLengthMax, ENIName))
		}
		//		c.clients.BCCClient.DescribeInstance(ctx, nodeSpec.InstanceSpec.ExistedOption.ExistedInstanceID,
		// STSClient.NewSignOption(ctx, c.UserID)
		cnt++
	}

	return cnt, nil
}

// verifyEhcStockAvailable 依赖白名单，校验 ehc 集群 & 机型是否售罄，true 未售罄，false 售罄, 返回校验不通过的 clusterID，及对应的库存
func verifyEhcStockAvailable(ctx context.Context, bccClient bcc.Interface, stsClient sts.Interface, nodeSpecs ...*ccesdk.InstanceSet) (bool, string, *bccapi.BccStock, error) {
	// 统计 某个机型所需的在不同 zone 所需的套餐，机型:{ehcClusterID:{{可用区:所需数量}}
	specZoneCount := make(map[string]map[string]map[internalvpc.AvailableZone]int)
	var accountID string
	for _, spec := range nodeSpecs {
		if spec.InstanceSpec.EhcClusterID == "" {
			continue
		}
		accountID = spec.InstanceSpec.AccountID
		specID := spec.InstanceSpec.MachineSpec
		ehcClusterID := spec.InstanceSpec.EhcClusterID
		zone := spec.InstanceSpec.AvailableZone
		if _, ok := specZoneCount[specID]; !ok {
			specZoneCount[specID] = make(map[string]map[internalvpc.AvailableZone]int)
		}
		if _, exists := specZoneCount[specID][ehcClusterID]; !exists {
			specZoneCount[specID][ehcClusterID] = make(map[internalvpc.AvailableZone]int)
		}
		specZoneCount[specID][ehcClusterID][zone] += spec.Count
	}
	// 没有 待校验的 ehc 集群
	if len(specZoneCount) == 0 {
		return true, "", nil, nil
	}
	for spec, clusterAndZoneCount := range specZoneCount {
		for ehcClusterID, zoneAndCount := range clusterAndZoneCount {
			stockInfo, err := bccClient.GetStockBySpecAndEhcClusterID(ctx, spec, ehcClusterID, stsClient.NewSignOption(ctx, accountID))
			if err != nil {
				return false, ehcClusterID, nil, err
			}
			if check, stock, err := checkEhcClusterStock(ctx, stockInfo, zoneAndCount); err != nil {
				return false, ehcClusterID, stock, err
			} else if !check {
				logger.Infof(ctx, "ehc cluster %s stock spec %s not enough, stock %v, needed: %v", spec, ehcClusterID, stock, zoneAndCount)
				return false, ehcClusterID, stock, err
			}
		}
	}
	return true, "", nil, nil
}

// CheckEhcClusterStock 校验 ehc 集群库存是否充足
func checkEhcClusterStock(ctx context.Context, stockResults *bccapi.GetStockWithSpecResults, zoneAndCount map[internalvpc.AvailableZone]int) (bool, *bccapi.BccStock, error) {
	if len(stockResults.BccStocks) == 0 {
		return false, nil, nil
	}
	for zone, count := range zoneAndCount {
		// 是否校验过的标识
		var flag bool
		for _, stock := range stockResults.BccStocks {
			availableZone, err := ccetypes.TransZoneNameToAvailableZone(ctx, stock.ZoneName)
			if err != nil {
				return false, nil, err
			}
			if availableZone == zone {
				flag = true
				if count > stock.InventoryQuantity {
					return false, &stock, nil
				}
				break
			}
		}
		// 如果未找到该可用区，返回 false
		if !flag {
			return false, nil, nil
		}
	}
	return true, nil, nil
}

// ListInstancesWithVolumes 批量获取实例的磁盘信息
func (c *InstanceController) ListInstancesWithVolumes() {
	requestID := logger.GetRequestID(c.ctx)
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*58)
	prepareCancel := c.cancel

	c.cancel = func() {
		if prepareCancel != nil {
			prepareCancel()
		}
		cancel()
	}
	ctx = logger.WithRequestID(ctx, requestID)

	// 获取集群 ID
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "ClusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
		return
	}
	var instanceIDList map[string]string
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &instanceIDList); err != nil {
		logger.Errorf(ctx, "Unmarshal instances failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}
	logger.Infof(ctx, "Add node begin: %v", utils.ToJSON(instanceIDList))

	if len(instanceIDList) == 0 {
		logger.Infof(ctx, "len(instanceIDList) == 0, skip")

		c.Data["json"] = ccesdk.ListVolumeFromInstancesResponse{
			VolumesWithInstances: make(map[string]*bcc.ListVolumesResponse, 0),
			RequestID:            logger.GetRequestID(ctx),
		}
		c.ServeJSON()
		return
	}
	// 当前限制批量能获取的最大实例数为50，前后端一致
	if len(instanceIDList) > 50 {
		logger.Errorf(ctx, "instanceIDList count is over than 50, instanceIDList: %v", instanceIDList)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "instance list should be less 50")
		return
	}
	volumes := make(map[string]*bcc.ListVolumesResponse, 0)
	wg := sync.WaitGroup{}
	errors := []error{}
	for instanceID, zoneName := range instanceIDList {
		wg.Add(1)
		go func(zoneName, instanceID string) {
			defer wg.Done()
			logger.Infof(ctx, "instanceID: %s, zoneName: %s", instanceID, zoneName)
			zoneNameForBCC := ccetypes.TransAvailableZoneToZoneName(ctx, "cn", "bj", zoneName)
			listVolume, err := c.clients.BCCClient.ListVolumes(ctx, instanceID, zoneNameForBCC, c.clients.STSClient.NewSignOption(ctx, c.userID))
			if err != nil {
				logger.Errorf(ctx, "ListVolumes failed: %v", err)
				errors = append(errors, err)
			} else {
				logger.Infof(ctx, "listVolume resp success: %v", listVolume)
				if _, ok := volumes[instanceID]; !ok {
					volumes[instanceID] = listVolume
				}
			}
		}(zoneName, instanceID)
	}
	wg.Wait()

	if len(errors) > 0 {
		c.errorHandlerV2(errorcode.NewListVolumesFromBCCFailed(), errorcode.LevelByAdmin, "ListVolumes failed")
		return
	}

	c.Data["json"] = ccesdk.ListVolumeFromInstancesResponse{
		VolumesWithInstances: volumes,
		RequestID:            logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// CreateInstances 创建 K8S Nodes
func (c *InstanceController) CreateInstances() {
	start := time.Now()
	requestID := logger.GetRequestID(c.ctx)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*88)
	prepareCancel := c.cancel

	c.cancel = func() {
		if prepareCancel != nil {
			prepareCancel()
		}
		cancel()
	}

	ctx = logger.WithRequestID(ctx, requestID)

	// 获取集群 ID
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "ClusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
		return
	}

	// 获取 NodeSpecs
	var nodeSpecs []*ccesdk.InstanceSet
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &nodeSpecs); err != nil {
		logger.Errorf(ctx, "Unmarshal instances failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	logger.Infof(ctx, "Add node begin: %v", utils.ToJSON(nodeSpecs))

	if len(nodeSpecs) == 0 {
		logger.Infof(ctx, "len(nodes) == 0, skip")

		c.Data["json"] = ccesdk.CreateInstancesResponse{
			CCEInstanceIDs: []string{},
			RequestID:      logger.GetRequestID(ctx),
		}
		c.ServeJSON()
		return
	}

	// 节点配置预检查
	for _, spec := range nodeSpecs {
		err := c.UpdateInstanceSpecPreCheck(spec.InstanceSpec)
		if err != nil {
			if models.ErrTaintsInvalid.Is(err) {
				c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, err.Error())
				return
			}
			c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByAdmin, err.Error())
			return
		}
	}

	// 限制批量操作 Node 数量, 避免过多导致请求超时
	if err := checkInstancesCountBelowLimit(ctx, nodeSpecs, c.config.BatchProcessInstanceCountLimit); err != nil {
		logger.Errorf(ctx, "instancesCountBeyondLimit failed : %v", err)
		c.errorHandlerV2(errorcode.NewInstancesCountBeyondLimit(), errorcode.LevelByUser,
			"InstanceCount should be less than %d, use InstanceGroup instead", c.config.BatchProcessInstanceCountLimit)
		return
	}

	// 获取 Cluster
	cluster, err := c.clients.K8SClient.GetCluster(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed : %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin,
			"GetCluster %s error", clusterID)
		return
	}

	if cluster == nil {
		logger.Errorf(ctx, "GetCluster return nil")
		c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser,
			"Cluster %s not exists", clusterID)
		return
	}

	logger.Infof(ctx, "GetCluster success: %v", utils.ToJSON(cluster))

	chechSgTime := time.Now()
	if failedErr, limitErr := c.checkInstanceSecurityGroupCountLimit(ctx, nodeSpecs, cluster); failedErr != nil || limitErr != nil {
		if limitErr != nil {
			logger.Errorf(ctx, "checkInstanceSecurityGroupCountLimit failed : %v", limitErr)
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, limitErr.Error())
			return
		}
		logger.Errorf(ctx, "checkInstanceSecurityGroupCountLimit failed : %v", failedErr)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, failedErr.Error())
		return
	}
	logger.Infof(ctx, "finished checkInstanceSecurityGroupCountLimit: %.1fs", time.Since(chechSgTime).Seconds())

	// 校验用户节点扩容数量限制  仅校验托管
	if cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeManagedPro {
		check, err := instancep.CheckCCENodeNumLimit(ctx, clusterID, c.accountID, checkInstancesCount(nodeSpecs), c.config, c.models)
		if err != nil {
			logger.Errorf(ctx, "CheckCCENodeNumLimit failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
			return
		}
		if !check && err == nil {
			logger.Errorf(ctx, "Exceeding the number of nodes by the cluster flavor limits.")
			c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(), errorcode.LevelByAdmin, "Exceeding the number of"+
				" nodes by the cluster flavor limits.")
			return
		}
	}

	// 检查创建节点是否自定义节点名
	var IsCustomizeNodeName bool
	for _, nodeSpec := range nodeSpecs {
		if nodeSpec.InstanceSpec.InstanceName != "" {
			IsCustomizeNodeName = true
		}
	}

	// 如果集群网络是 eni，校验所选机型是否支持 eni
	logger.Infof(ctx, "start ValidateSupportEni: %v", time.Since(start))
	if skip, ok := cluster.Annotations[ccev1.AnnotationSkipValidateEni]; !ok && skip != ccev1.AnnotationTrue && cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCENI {
		if failedErr, notSupportErr := c.service.ValidateInstanceSetsSupportEni(ctx, ccetypes.ClusterRoleNode, &cluster.Spec, nodeSpecs); failedErr != nil {
			logger.Errorf(ctx, "check exist eni quota failed: %v", failedErr)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, failedErr.Error())
			return
		} else if notSupportErr != nil {
			logger.Errorf(ctx, "check exist eni quota failed, instance not support eni: %v", notSupportErr)
			c.errorHandlerV2(errorcode.NewNotSupportEni(), errorcode.LevelByAdmin, notSupportErr.Error())
			return
		}
	}
	logger.Infof(ctx, "finished ValidateSupportEni: %v", time.Since(start))

	// 补全 NodeSpecs 字段
	logger.Infof(ctx, "start EnsureInstanceSets elapsed_from_start: %v", time.Since(start))
	if err := c.service.EnsureInstanceSets(ctx, ccetypes.ClusterRoleNode,
		&(cluster.Spec), nodeSpecs); err != nil {
		logger.Errorf(ctx, "EnsureNodeSpec failed : %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
		return
	}
	logger.Infof(ctx, "finished EnsureInstanceSets elapsed_from_start: %v", time.Since(start))

	logger.Infof(ctx, "EnsureInstanceGroupListSpec success: %v", utils.ToJSON(nodeSpecs))

	// 如果需要加入 ehc 集群，需要判断，ehc 集群库存是否充足
	//if ok, ehcClusterID, stock, err := verifyEhcStockAvailable(ctx, c.clients.BCCClient, c.clients.STSClient, nodeSpecs...); err != nil {
	//	logger.Errorf(ctx, "validate ehc stock failed : %v", err)
	//	c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin,
	//		"Verify ehc stock failed, error: %v", err)
	//} else if !ok {
	//	logger.Errorf(ctx, "ehc cluster %s stock not enough", ehcClusterID)
	//	c.errorHandlerV2(errorcode.NewEhcClusterNoStock(), errorcode.LevelByAdmin,
	//		"EHC集群 【%s】 当前最多可购买 %d 台实例，如有问题请提工单", ehcClusterID, stock.InventoryQuantity)
	//}

	//	// 第三方依赖合法性校验: ENI名字长度
	//	_, err = c.VerifyInstancesDependency(ctx, clusterID, nodeSpecs)

	userScriptService := userscript.NewUserScriptService(c.models)

	var machineSpecs []string
	// 更新work节点, 创建user script
	for _, nodeSpec := range nodeSpecs {
		// 收集所需的所有套餐，只对 ebc 和 bcc 进行校验
		if nodeSpec.InstanceSpec.MachineType == ccetypes.MachineTypeEBC || nodeSpec.InstanceSpec.MachineType == ccetypes.MachineTypeBCC {
			machineSpecs = append(machineSpecs, nodeSpec.InstanceSpec.MachineSpec)
		}

		// 兼顾PreUserScript和PostUserScript的创建，其中一个失败则报错。脚本有效性校验在UpdateInstanceSpecPreCheck已处理
		err := userscript.CreateUserScripts(c.ctx, userScriptService, &nodeSpec.InstanceSpec)
		if err != nil {
			logger.Errorf(ctx, "CreateUserScripts failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
			return
		}
	}

	// 校验套餐是否已经全部适配, 添加 ccev1.AnnotationSkipCheckSpec 可跳过校验
	if v, ok := cluster.Annotations[ccev1.AnnotationSkipCheckSpec]; !ok && v != ccev1.AnnotationTrue {
		// 校验套餐是否已经全部适配
		err = c.CheckMachineSpecs(ctx, machineSpecs)
		if err != nil {
			logger.Errorf(c.ctx, "CheckMachineSpecs(%v) failed: %v", machineSpecs, err)
			c.errorHandlerV2(errorcode.NewInvalidMachineSpec(), errorcode.LevelByUser, err.Error())
			return
		}
	}
	for _, nodeSpec := range nodeSpecs {
		// 校验kubelet 和容器运行时目录
		// 校验 DockerDataRoot
		// 适配接口调用，不传kubelet数据目录
		err = fillspec.SetAndCheckDataDir(nodeSpec.InstanceSpec.DeployCustomConfig, nodeSpec.InstanceSpec.RuntimeType)
		if err != nil {
			logger.Errorf(c.ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
			c.errorHandlerV2(errorcode.NewInvalidDataRootDir(), errorcode.LevelByUser, "invalid data Dir, err is: %s", err.Error())
			return
		}
	}
	// NodeSpecs 转成 Models
	var nodes []*models.Instance
	nodes, err = c.service.InstanceSetsToModel(ctx, nodeSpecs)
	if err != nil {
		logger.Errorf(ctx, "InstanceGroupSpecListToModel failed : %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
	}

	isHostNameMode := cluster.Spec.K8SCustomConfig.EnableHostname
	logger.Infof(ctx, "CreateInstances cluster EnableHostname %s", isHostNameMode)

	// Node name 模式为 HostName且用户自定义节点名时，检查节点名是否重复
	if isHostNameMode && IsCustomizeNodeName {
		instances, err := c.models.GetInstances(ctx, c.accountID, clusterID)
		if err != nil {
			logger.Errorf(ctx, "GetInstance failed by hostname: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
		hostnameMap := make(map[string]bool)
		for _, instance := range instances {
			if instance.Status.Machine.Hostname != "" {
				hostnameMap[instance.Status.Machine.Hostname] = true
			} else if instance.Spec.CCEInstanceID != "" {
				hostnameMap[instance.Spec.InstanceName] = true
			}
		}
		for _, node := range nodes {
			if hostnameMap[node.Spec.InstanceName] {
				c.errorHandlerV2(errorcode.NewInstanceAlreadyExists(), errorcode.LevelByUser, "集群内节点名 %s 已存在", node.Spec.InstanceName)
			}
		}
	}

	logger.Infof(ctx, "InstanceGroupSpecToModel success: %v", utils.ToJSON(nodes))

	// 将 Instances 信息入库
	var nodeUUIDs []string
	logger.Infof(ctx, "start models.CreateInstances elapsed_from_start: %v", time.Now().Sub(start))

	nodeUUIDs, err = c.models.CreateInstances(ctx, clusterID, nodes)
	if err != nil && !models.IsAlreadyExists(err) && !models.IsExistedInstanceIDDuplicated(err) {
		logger.Errorf(ctx, "CreateInstance failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	logger.Infof(ctx, "finished models.CreateInstances elapsed_from_start: %v", time.Now().Sub(start))

	if models.IsAlreadyExists(err) {
		logger.Errorf(ctx, "CreateInstance failed: %s", err)
		c.errorHandlerV2(errorcode.NewInstanceAlreadyExists(), errorcode.LevelByUser, err.Error())
	}

	if models.IsExistedInstanceIDDuplicated(err) {
		logger.Errorf(ctx, "CreateInstance failed: %s", err)
		c.errorHandlerV2(errorcode.NewExistedInstanceIDDuplicated(), errorcode.LevelByUser, err.Error())
	}

	logger.Infof(ctx, "CreateInstance success: %v", utils.ToJSON(nodeUUIDs))

	logger.Infof(ctx, "start to ReconcileInstance elapsed_from_start: %v", time.Now().Sub(start))
	// 创建 []InstanceCRD, 失败不影响返回结果
	for _, node := range nodes {
		if _, err := c.clients.K8SClient.ReconcileInstance(ctx, node, c.accountID); err != nil {
			logger.Errorf(ctx, "ReconcileInstance failed: %v", err)
			continue
		} else {
			logger.Infof(ctx, "ReconcileInstance success: %v", node.Spec.CCEInstanceID)
		}
	}

	logger.Infof(ctx, "finished to ReconcileInstance elapsed_from_start: %v", time.Now().Sub(start))

	c.Data["json"] = ccesdk.CreateInstancesResponse{
		CCEInstanceIDs: nodeUUIDs,
		RequestID:      logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

func (c *InstanceController) checkInstanceSecurityGroupCountLimit(ctx context.Context, instanceSets []*ccesdk.InstanceSet, cluster *ccev1.Cluster) (error, error) {
	sgProvider := securitygroup.NewProvider(ctx, c.clients.STSClient, c.clients.BCCClient, c.clients.BBCClient, c.clients.HPASCClient)
	// 并发处理 Instance fillspec
	var failedErrs, limitErrs []error
	wg := sync.WaitGroup{}
	concurrence := c.config.InstanceSGLimitConcurrence
	if concurrence == 0 {
		concurrence = 20
	}
	logger.Infof(ctx, "InstanceSGLimitConcurrences: %v", concurrence)

	concurrenceChan := make(chan int, concurrence) // 控制并发度

	for _, instanceSet := range instanceSets {
		instance := instanceSet.InstanceSpec
		if !instance.Existed || instance.ExistedOption.ExistedInstanceID == "" {
			continue
		}
		if instance.ClusterRole != ccetypes.ClusterRoleNode && instance.ClusterRole != "" {
			continue
		}

		// 如果实例属于节点组, 默认会使用节点组级别的安全组配置，节点组级别可配置的安全组数量不会超过 10 个，所以此时无需校验安全组数量。
		if len(instance.InstanceGroupID) != 0 {
			continue
		}

		concurrenceChan <- 1
		wg.Add(1)

		go func(ctx context.Context, instanceSet ccetypes.InstanceSpec) {
			defer func() {
				<-concurrenceChan
				wg.Done()
			}()

			appendSgCount := securitygroup.GetSgCountOfInstance(&instanceSet)

			nSgs, eSgs, err := sgProvider.GetSecurityGroups(ctx, cluster.Spec.VPCID, instanceSet.ExistedOption.ExistedInstanceID, cluster.Spec.AccountID)
			if err != nil {
				logger.Errorf(ctx, "GetSecurityGroups failed: %v", err)
				failedErrs = append(failedErrs, err)
				return
			}
			if appendSgCount+len(nSgs.SecurityGroups)+len(eSgs.EnterpriseSecurityGroups) > 10 {
				limitErrs = append(limitErrs, securitygroup.NewSgCountLimitError())
			}
		}(ctx, instance)
	}

	wg.Wait()

	if len(failedErrs) != 0 {
		return fmt.Errorf("%v", failedErrs), nil
	} else if len(limitErrs) != 0 {
		return nil, fmt.Errorf("%v", limitErrs)
	}
	return nil, nil
}

// GetInstance to return details of instance
func (c *InstanceController) GetInstance() {
	ctx := c.ctx

	instanceID := c.Ctx.Input.Param(":instanceID")
	clusterID := c.Ctx.Input.Param(":clusterID")

	includeK8SNode, err := c.GetBool("includeK8SNode")
	if err != nil {
		logger.Errorf(ctx, "Param includeK8SNode not set: %s", err)
		includeK8SNode = false
	}

	isVKNode, _ := c.GetBool("isVKNode")

	// vk node 无需数据库查询信息
	if isVKNode {
		logger.Infof(ctx, "isVKNode is: %v, get vkNodeDetail begin!", isVKNode)
		res, err := c.getVKNodeResp(ctx, clusterID, instanceID)
		if err != nil {
			logger.Errorf(ctx, "GetVKNodeResp failed: %v", err)
			c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Get VK Node error: ", err)
		}

		logger.Infof(ctx, "GetK8sNodeStatus success: %v", utils.ToJSON(res))

		// 返回结果
		c.Data["json"] = ccesdk.GetInstanceResponse{
			Instance:  res,
			RequestID: logger.GetRequestID(ctx),
		}
		c.ServeJSON()
	}

	// 获取 Instance
	var instance *models.Instance

	// 根据CCE InstanceID或BCC InstanceID(兼容二者)获取Instance DB Model
	if strings.Contains(instanceID, ccetypes.CCEPrefix) {
		logger.Infof(ctx, "GetInstanceByCCEID")
		instance, err = c.models.GetInstanceByCCEID(ctx, instanceID, c.accountID)
	} else {
		logger.Infof(ctx, "GetInstanceByBCCID")
		instance, err = c.models.GetInstanceByBCCID(ctx, instanceID, c.accountID)
	}

	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetInstance failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if models.IsNotExist(err) || instance == nil {
		logger.Errorf(ctx, "Instance %s not exists", instanceID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not exists", instanceID)
	}

	logger.Infof(ctx, "models.Instance: %s", utils.ToJSON(instance))

	// 获取 cluster
	cluster, err := c.models.GetCluster(ctx, instance.Spec.ClusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	// 将 Instance DB Model 转为 SDK Struct 中
	result, err := instancep.InstanceModelToSDK(ctx, cluster, instance, false, false)
	if err != nil {
		logger.Errorf(ctx, "instanceModelToSDK failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}
	logger.Infof(ctx, "Instance: %s", utils.ToJSON(result))

	// 增加 K8S NodeDetail 信息
	if includeK8SNode == true && (result.Spec.ClusterRole == ccetypes.ClusterRoleNode ||
		(result.Spec.ClusterRole == ccetypes.ClusterRoleMaster && cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeContainerizedCustom)) &&
		result.Status.InstancePhase == ccetypes.InstancePhaseRunning {
		logger.Infof(ctx, "Instance %s is node and instancePhase is running, add node detail", instanceID)

		nodeDetail, err := c.getK8SNodeDetail(ctx, result)
		if err != nil {
			logger.Errorf(ctx, "getK8SNodeDetail failed: %s", err)
		}

		if err == nil {
			logger.Infof(ctx, "GetK8SNodeDetail success: %s", utils.ToJSON(nodeDetail))
			result.K8SNode = nodeDetail
		}

		var k8sClient kubernetes.Interface
		if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
			cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
			cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

			logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

			k8sClient, err = getClientSet(ctx, cluster.Spec.ClusterID, c.models)
			if err != nil {
				logger.Errorf(ctx, "get k8s client failed: %v", err)
			}
		}

		// 与列表查询的可用状态保持一致
		result.Status.InstancePhase, err = instancep.GetK8sNodeStatus(ctx, result, cluster.Spec.K8SCustomConfig.EnableHostname, k8sClient)
		if err != nil {
			logger.Errorf(ctx, "GetK8sNodeStatus failed: %v", err)
		}
	}

	// 二进制部署通过机器状态反馈到instancePhase
	if cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeCustom {
		switch instance.Status.Machine.MachineStatus {
		case logicbcc.ServerStatusError, logicbcc.ServerStatusStopped, logicbcc.ServerStatusReboot:
			result.Status.InstancePhase = "not_ready"
		}
	}

	// Add Properties Not in DB
	err = c.addInstancePropertiesNotInDB(ctx, cluster, result)
	if err != nil {
		logger.Errorf(ctx, "addInstancePropertiesNotInDB failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if err := c.loadUserScript(ctx, result); err != nil {
		logger.Errorf(ctx, "loadUserScript failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	// 返回结果
	c.Data["json"] = ccesdk.GetInstanceResponse{
		Instance:  result,
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

func (c *InstanceController) GetInstanceByInstanceID() {
	ctx := c.ctx

	instanceID := c.Ctx.Input.Param(":instanceID")

	// 获取 Instance
	var instance *models.Instance

	var err error
	// 根据CCE InstanceID或BCC InstanceID(兼容二者)获取Instance DB Model
	if strings.Contains(instanceID, ccetypes.CCEPrefix) {
		logger.Infof(ctx, "GetInstanceByCCEID")
		instance, err = c.models.GetInstanceByCCEID(ctx, instanceID, c.accountID)
	} else {
		logger.Infof(ctx, "GetInstanceByBCCID")
		instance, err = c.models.GetInstanceByBCCID(ctx, instanceID, c.accountID)
	}

	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetInstance failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if models.IsNotExist(err) {
		logger.Errorf(ctx, "Instance %s not exists", instanceID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not exists", instanceID)
	}

	if instance == nil {
		logger.Errorf(ctx, "Instance %s not exists", instanceID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not exists", instanceID)
	}

	logger.Infof(ctx, "models.Instance: %s", utils.ToJSON(instance))

	c.Data["json"] = map[string]string{
		"requestID":  logger.GetRequestID(ctx),
		"clusterID":  instance.Spec.ClusterID,
		"instanceID": instance.Spec.CCEInstanceID,
	}

	c.ServeJSON()
}

func (c *InstanceController) loadUserScript(ctx context.Context, instance *ccesdk.Instance) error {
	userScriptService := userscript.NewUserScriptService(c.models)
	if userscript.IsValidUserScriptID(instance.Spec.DeployCustomConfig.PreUserScript) {
		preScript, err := userScriptService.Get(ctx, instance.Spec.DeployCustomConfig.PreUserScript)
		if err != nil {
			logger.Errorf(ctx, "get user preScript failed: %v", err)
			return err
		}
		instance.Spec.DeployCustomConfig.PreUserScript = preScript.ScriptContent
	}

	if userscript.IsValidUserScriptID(instance.Spec.DeployCustomConfig.PostUserScript) {
		postScript, err := userScriptService.Get(ctx, instance.Spec.DeployCustomConfig.PostUserScript)
		if err != nil {
			logger.Errorf(ctx, "get user postScript failed: %v", err)
			return err
		}
		instance.Spec.DeployCustomConfig.PostUserScript = postScript.ScriptContent
	}

	return nil
}

// UpdateInstance to update instance
func (c *InstanceController) UpdateInstance() {
	ctx := c.ctx
	instanceID := c.GetPathParams(":instanceID")

	var spec ccesdk.InstanceSpec
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &spec); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}
	logger.Infof(ctx, "UpdateInstance begin, instanceID: %v, spec: %s", instanceID, utils.ToJSON(spec))

	var err error
	// 节点配置预检查
	err = c.UpdateNodeLabelsPreCheck(spec.Labels)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByAdmin, err.Error())
		return
	}
	var instanceModel *models.Instance

	accountID := c.accountID

	// 1.根据CCE InstanceID或BCC InstanceID(兼容二者)获取Instance DB Model
	if strings.Contains(instanceID, ccetypes.CCEPrefix) {
		logger.Infof(ctx, "GetInstanceByCCEID")
		instanceModel, err = c.models.GetInstanceByCCEID(ctx, instanceID, accountID)
	} else {
		logger.Infof(ctx, "GetInstanceByBCCID")
		instanceModel, err = c.models.GetInstanceByBCCID(ctx, instanceID, accountID)
	}
	if err != nil {
		logger.Errorf(c.ctx, "get instance %s from db failed: %v", instanceID, err)
		if models.IsNotExist(err) {
			c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not exists", instanceID)
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
		return
	}

	// 以下各接口统一使用CCEInstanceID
	cceInstanceID := instanceModel.Spec.CCEInstanceID

	// 2.写入节点字段变更 TODO: spec里面很多字段是不支持update的，所以目前只update这些字段，后面有需要再加
	instanceModel.Spec.InstanceGroupID = spec.InstanceGroupID
	instanceModel.Spec.InstanceGroupName = spec.InstanceGroupName
	instanceModel.Spec.Tags = spec.Tags
	instanceModel.Spec.Taints = spec.Taints

	instanceModel.Spec.Labels = spec.Labels
	if instanceModel.Spec.Labels == nil {
		instanceModel.Spec.Labels = make(map[string]string)
	}
	instanceModel.Spec.Labels["cluster-id"] = instanceModel.Spec.ClusterID
	instanceModel.Spec.Labels["cluster-role"] = string(instanceModel.Spec.ClusterRole)

	instanceModel.Spec.Annotations = spec.Annotations
	if instanceModel.Spec.Annotations == nil {
		instanceModel.Spec.Annotations = make(map[string]string)
	}
	// 检查是否无效的label或annotation
	err = constant.CheckIllegalLabelORAnnotation(*instanceModel.Spec)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "err is %s", err.Error())
		return
	}

	instanceModel.Spec.CCEInstancePriority = spec.CCEInstancePriority
	instanceModel.Spec.EhcClusterID = spec.EhcClusterID

	instanceCRD, err := c.clients.K8SClient.GetInstance(c.ctx, cceInstanceID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance %s/%s failed: %v", "default", cceInstanceID, err)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "")
		return
	}

	// 删除节点组前update 节点组deleteOption，同步update instance deleteOption
	instanceModel.Spec.DeleteOption = spec.DeleteOption
	if err := c.models.UpdatePartInstanceSpec(c.ctx, accountID, cceInstanceID, instanceModel.Spec); err != nil {
		logger.Errorf(c.ctx, "update instance spec to db failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
		return
	}

	// TODO: 竞价实例参数没有入库, 防止覆盖
	instanceModel.Spec.Bid = spec.Bid
	instanceModel.Spec.BidOption = spec.BidOption

	// 安全组未入库，防止覆盖。
	instanceModel.Spec.SecurityGroup = ccetypes.SecurityGroup{
		EnableCCERequiredSecurityGroup: instanceCRD.Spec.SecurityGroup.EnableCCERequiredSecurityGroup,
		EnableCCEOptionalSecurityGroup: instanceCRD.Spec.SecurityGroup.EnableCCEOptionalSecurityGroup,
		CustomSecurityGroupIDs:         instanceCRD.Spec.SecurityGroup.CustomSecurityGroupIDs,
	}

	// 未入库，防止覆盖
	instanceModel.Spec.RelationTag = instanceCRD.Spec.RelationTag
	instanceModel.Spec.SecurityGroupType = instanceCRD.Spec.SecurityGroupType
	instanceModel.Spec.OptionalSubnetIDs = instanceCRD.Spec.OptionalSubnetIDs
	instanceModel.Spec.EphemeralDiskList = instanceCRD.Spec.EphemeralDiskList
	instanceModel.Spec.SecurityGroups = instanceCRD.Spec.SecurityGroups
	instanceModel.Spec.EhcClusterID = spec.EhcClusterID
	instanceModel.Spec.DeploySetID = instanceCRD.Spec.DeploySetID
	instanceModel.Spec.UserData = instanceCRD.Spec.UserData
	instanceModel.Spec.BBCOption = instanceCRD.Spec.BBCOption
	instanceModel.Spec.BECOption = instanceCRD.Spec.BECOption
	instanceModel.Spec.BidOption = instanceCRD.Spec.BidOption
	instanceModel.Spec.DeploySetIDs = instanceCRD.Spec.DeploySetIDs
	instanceModel.Spec.IAMRole = spec.IAMRole
	instanceModel.Spec.InstanceResource = instanceCRD.Spec.InstanceResource
	// 如果请求中包含了缩容保护字段，name应该以为传入的请求参数为准
	if spec.ScaleDownDisabled != nil {
		instanceModel.Spec.ScaleDownDisabled = *spec.ScaleDownDisabled
	} else {
		instanceModel.Spec.ScaleDownDisabled = instanceCRD.Spec.ScaleDownDisabled
	}
	if spec.XPUContainerToolkitVersion != "" {
		instanceModel.Spec.XPUContainerToolkitVersion = spec.XPUContainerToolkitVersion
	} else {
		instanceModel.Spec.XPUContainerToolkitVersion = instanceCRD.Spec.XPUContainerToolkitVersion
	}

	if spec.NeedGPU != nil {
		instanceModel.Spec.NeedGPU = *spec.NeedGPU
	}
	instanceModel.Spec.AIInfraOption = instanceCRD.Spec.AIInfraOption
	instanceModel.Spec.HPASOption = instanceCRD.Spec.HPASOption
	logger.Infof(ctx, "instance models is %s", utils.ToJSON(instanceModel))
	if _, err := c.clients.K8SClient.ReconcileInstance(c.ctx, instanceModel, accountID); err != nil {
		logger.Errorf(c.ctx, "reconcile instance to meta cluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
		return
	}

	// 3.更新结束后重新查询，并返回重新查询结果
	c.GetInstance()
}

func (c *InstanceController) RemoveInstances() {
	ctx := c.ctx

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "clusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
	}

	// 读取参数
	var req ccesdk.DeleteInstancesRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	logger.Infof(ctx, "DeleteInstances begin: %s", utils.ToJSON(req))

	instanceIDs := req.InstanceIDs
	if len(instanceIDs) == 0 {
		logger.Errorf(ctx, "InstanceIDs is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "instanceIDs is empty")
	}

	// 使用自带的deleteOption，会因为是批量操作，可能导致一个接口的实例处理方式不一致。这里还是报错最好
	if req.DeleteOption == nil {
		logger.Infof(ctx, "DeleteOption is nil, using instance.Spec.DeleteOption instead")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "delete option is empty")
	}

	// 兼容虚机ID和cceID, 拆分，分别处理
	var cceIDs []string // cceInstanceIDs
	var bccIDs []string // iaas resource id : bcc id 、 bbc id .
	for _, id := range instanceIDs {
		if err := utils.ValidateInstanceID(ctx, id); err != nil {
			logger.Errorf(ctx, "instance %s not valid, skip delete", id)
			continue
		}

		if strings.Contains(id, ccetypes.CCEPrefix) {
			cceIDs = append(cceIDs, id)
		} else {
			bccIDs = append(bccIDs, id)
		}
	}

	// 获取 Instance
	var instances []*models.Instance              // 要删除的实例中，游离节点列表
	instancesInGroup := make(map[string][]string) // 要删除实例中，非游离节点id，instanceGroupId=>instanceIDs
	var instanceTmp []*models.Instance
	var err error
	cceInstanceIDs := []string{}        // 要删除的实例中，游离节点id列表
	cceInstanceInGroupIDs := []string{} // 要删除的实例中，非游离节点id列表

	// 用于去除重复ID
	cceInstanceIdToInstanceGroupId := map[string]string{}

	if len(cceIDs) != 0 {
		instanceTmp, err = c.models.GetInstancesByCCEIDs(ctx, clusterID, cceIDs, c.accountID)
		for _, instance := range instanceTmp {
			cceInstanceID := instance.Spec.CCEInstanceID
			if _, ok := cceInstanceIdToInstanceGroupId[cceInstanceID]; ok {
				continue
			}
			instancePhase := ccetypes.InstancePhaseRunning
			if instance.Status != nil {
				instancePhase = instance.Status.InstancePhase
			}
			if instanceGroupId := instance.Spec.InstanceGroupID; instanceGroupId != "" &&
				req.ScaleDown != nil && *req.ScaleDown && instancePhase != ccetypes.InstancePhaseDeleteFailed {
				instancesInGroup[instanceGroupId] = append(instancesInGroup[instanceGroupId], cceInstanceID)
				cceInstanceInGroupIDs = append(cceInstanceInGroupIDs, cceInstanceID)
			} else {
				instances = append(instances, instance)
				cceInstanceIDs = append(cceInstanceIDs, cceInstanceID)
			}
			cceInstanceIdToInstanceGroupId[cceInstanceID] = instance.Spec.InstanceGroupID
		}
	}

	if len(bccIDs) != 0 {
		instanceTmp, err = c.models.GetInstancesByBCCIDs(ctx, clusterID, bccIDs, c.accountID)
		for _, instance := range instanceTmp {
			cceInstanceID := instance.Spec.CCEInstanceID
			if _, ok := cceInstanceIdToInstanceGroupId[cceInstanceID]; ok {
				continue
			}
			instancePhase := ccetypes.InstancePhaseRunning
			if instance.Status != nil {
				instancePhase = instance.Status.InstancePhase
			}
			if instanceGroupId := instance.Spec.InstanceGroupID; instanceGroupId != "" &&
				req.ScaleDown != nil && *req.ScaleDown && instancePhase != ccetypes.InstancePhaseDeleteFailed {
				instancesInGroup[instanceGroupId] = append(instancesInGroup[instanceGroupId], cceInstanceID)
				cceInstanceInGroupIDs = append(cceInstanceInGroupIDs, cceInstanceID)
			} else {
				instances = append(instances, instance)
				cceInstanceIDs = append(cceInstanceIDs, cceInstanceID)
			}
			cceInstanceIdToInstanceGroupId[cceInstanceID] = instance.Spec.InstanceGroupID
		}
	}

	if err != nil {
		logger.Errorf(ctx, "GetInstances from DB failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if len(cceInstanceIDs)+len(cceInstanceInGroupIDs) == 0 {
		logger.Warnf(ctx, "instances %v in %s not exist", instanceIDs, clusterID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "instances %v in %s not exist", instanceIDs, clusterID)
	}

	// 检查是否包含 master 节点
	for _, instance := range instances {
		if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
			logger.Warnf(ctx, "master %s cannot be deleted or removed", instance.Spec.InstanceName)
			c.errorHandlerV2(errorcode.NewMasterCannotDeletedOrRemoved(), errorcode.LevelByUser, "master %s cannot be deleted or removed", instance.Spec.InstanceName)
		}
	}

	logger.Infof(ctx, "Instance: %s %s", utils.ToJSON(instances), utils.ToJSON(instancesInGroup))

	err = c.exitConflictWorkflowByInstanceGroup(ctx, instancesInGroup)
	if err != nil {
		logger.Errorf(ctx, "exitConflictWorkflowByInstanceGroup, err: %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "exit conflict upgrade task in instanceGroup")
	}

	// 检查是否允许删除 Node, 托管 Master 集群有最小 Node 数限制
	minNodeNum, deleteAllowed, err := c.deleteNodeAllowed(ctx, clusterID, len(cceInstanceIDs)+len(cceInstanceInGroupIDs))
	if err != nil {
		logger.Errorf(ctx, "check deleteNodeAllowed failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	if deleteAllowed == false {
		msg := fmt.Sprintf("workers number should be no less than %d when masters in managed mode", minNodeNum)
		logger.Errorf(ctx, msg)
		c.errorHandlerV2(errorcode.NewManagedClusterMinNodeNumLimit(), errorcode.LevelByUser, msg)
	}

	failedInstanceIDs := []string{} // 同步节点删除失败的实例id

	// 冲突检测，节点组存在滚动升级时，不能删除当前节点组的节点
	conflictErr := c.exitConflictWorkflowByInstanceGroup(ctx, instancesInGroup)
	if conflictErr != nil {
		logger.Errorf(ctx, conflictErr.Error())
		c.errorHandlerV2(errorcode.NewManagedClusterMinNodeNumLimit(), errorcode.LevelByUser, conflictErr.Error())
	}

	// 禁止删除扩容中节点
	if errCode, logLevel, format := existConflictWithInstanceGroupScaling(ctx, c.clientSet.MetaClient, clusterID, cceInstanceIdToInstanceGroupId); errCode != nil {
		c.errorHandlerV2(errCode, logLevel, format)
	}

	// 删除 Instance CRD
	var wg sync.WaitGroup
	var errs []error
	wg.Add(len(cceInstanceIDs) + len(instancesInGroup))
	buffer := make(chan string, 50)

	if len(cceInstanceIDs) > 0 {
		/****** 删除游离节点 *****/
		// 更新 DB 中的 Instance DeleteOption, InstancePhase
		if err := c.models.UpdateInstancesDeleteOptionAndPhase(ctx, clusterID, cceInstanceIDs, c.accountID, req.DeleteOption); err != nil {
			logger.Errorf(ctx, "UpdateInstancesDeleteOptionAndPhase failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
		}

		for _, cceInstanceID := range cceInstanceIDs {
			buffer <- cceInstanceID
			go func(cceInstanceID string) {
				defer wg.Done()
				if err := deleteInstance(ctx, cceInstanceID, c.accountID, req.DeleteOption, c.models, c.clients.K8SClient); err != nil && !kerrors.IsNotFound(err) {
					logger.Errorf(ctx, "deleteInstance %s failed: %v", cceInstanceID, err)
					errs = append(errs, err)
					failedInstanceIDs = append(failedInstanceIDs, cceInstanceID)
				}
				<-buffer
			}(cceInstanceID)
		}
		/****** 删除游离节点 *****/
	}

	if len(instancesInGroup) > 0 {
		/****** 删除非游离节点 *****/
		for instanceGroupId, ids := range instancesInGroup {
			buffer <- instanceGroupId
			go func(instanceGroupId string, ids []string) {
				defer wg.Done()
				_, err := c.instanceGroupService.ScaleDown(ctx, c.accountID, instanceGroupId, ids, nil, instancegroup.ScaleDownOption{
					CleanPolicy:          ccetypes.DeleteCleanPolicy,
					InstanceDeleteOption: req.DeleteOption,
				})
				if err != nil {
					logger.Errorf(ctx, "deleteInstanceInGroup %s failed: %v", instanceGroupId, err)
					errs = append(errs, err)
					failedInstanceIDs = append(failedInstanceIDs, ids...)
				}
				<-buffer
			}(instanceGroupId, ids)
		}
		/****** 删除非游离节点 *****/
	}

	wg.Wait()

	if len(errs) > 0 {
		logger.Errorf(ctx, "delete instance failed instance id list: %v", failedInstanceIDs)
		// todo 等节点组状态上线后，细分报错信息，不能全按照同一种返回
		c.errorHandlerV2(errorcode.NewDeleteInstanceError(uerrors.NewAggregate(errs).Error()), errorcode.LevelByAdmin, uerrors.NewAggregate(errs).Error())
	}
	c.Data["json"] = ccesdk.DeleteInstancesResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// existConflictWithInstanceGroupScaling 待移除节点是否与节点组伸缩有冲突, 返回有冲突的实例id
func existConflictWithInstanceGroupScaling(ctx context.Context,
	metaClient meta.Interface, clusterId string, instanceIdToInstanceGroupId map[string]string,
) (code *errorcode.ErrorCode, level errorcode.Level, format string) {
	if len(instanceIdToInstanceGroupId) == 0 {
		return nil, "", ""
	}
	if metaClient == nil {
		err := fmt.Errorf("meta client is nil")
		return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
	}
	if clusterId == "" {
		err := fmt.Errorf("cluster id is empty")
		return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
	}
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			ccetypes.ClusterIDLabelKey: clusterId,
		},
	}
	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	taskList, err := metaClient.ListTask(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: labelSelectorStr,
	})
	if err != nil {
		logger.Errorf(ctx, "list task list failed, clusterId:%v err:%v", clusterId, err)
		return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
	}

	allInstanceGroupIds := map[string][]string{}
	for instanceId, instanceGroupId := range instanceIdToInstanceGroupId {
		allInstanceGroupIds[instanceGroupId] = append(allInstanceGroupIds[instanceGroupId], instanceId)
	}

	instanceIDsInScalingInstanceGroup := make([]string, 0)

	for _, task := range taskList.Items {
		if task.Status.Phase == ccetypes.TaskPhaseFinished || task.Status.Phase == ccetypes.TaskPhaseAborted {
			logger.Infof(ctx, "task %s, status is %s, skip check", task.Name, task.Status.Phase)
			continue
		}
		op, err := utils.ExtractInstanceGroupReplicasOperation(&task)
		if err != nil {
			return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
		}

		if op.Spec.OperationType == ccetypes.InstanceGroupOperationTypeScalingDown ||
			(op.Spec.OperationType == ccetypes.InstanceGroupOperationTypeRepair && op.Spec.Delta == 0 && len(op.Spec.InstancesToBeDeleted) > 0) {
			continue // 跳过缩容，否则缩容任务调用移除接口会被拦截
		}

		taskInstanceGroupId := task.Labels[ccetypes.InstanceGroupIDLabelKey]
		if instanceIds, ok := allInstanceGroupIds[taskInstanceGroupId]; ok {
			instanceIDsInScalingInstanceGroup = append(instanceIDsInScalingInstanceGroup, instanceIds...)
		}
	}
	if len(instanceIDsInScalingInstanceGroup) == 0 {
		return nil, "", ""
	}
	message := fmt.Sprintf("some instances conflict with instance group scaling: %v", instanceIDsInScalingInstanceGroup)
	return errorcode.NewInvalidParam(message), errorcode.LevelByUser, message
}

// exitConflictWorkflowByInstanceGroup 检查节点组是否存在正在运行中的workflow
func (c *InstanceController) exitConflictWorkflowByInstanceGroup(ctx context.Context, instancesInGroup map[string][]string) error {
	for key, value := range instancesInGroup {
		if len(value) <= 0 {
			continue
		}
		workflowList, err := c.clientSet.MetaClient.ListWorkflows(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
			LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, key),
		})
		if err != nil {
			logger.Errorf(ctx, "list workflows failed: %v", err)
			continue
		}
		for _, workflow := range workflowList.Items {
			// 校验是否存在冲突任务
			if workflow.Spec.WorkflowType != ccetypes.WorkflowTypeUpgradeNodes &&
				workflow.Spec.WorkflowType != ccetypes.WorkflowTypeUpgradeKubeletConfig {
				continue
			}

			// 处于暂停、升级中、pending、确认中和删除中这几个状态时，不允许删除节点组内节点
			if workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePaused || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseUpgrading ||
				workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePending || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseVerifying ||
				workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseDeleting {
				return fmt.Errorf("instanceGroup %s is upgrading, can't delete instances %v", key, utils.ToJSON(value))
			}
		}
	}
	return nil
}

// DeleteInstances - 删除 CCE Nodes
// 支持 BCCInstanceID 和 CCEInstanceID
func (c *InstanceController) DeleteInstances() {
	ctx := c.ctx

	// 获取 ClusterID
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "clusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
	}

	// 读取参数
	var req ccesdk.DeleteInstancesRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	logger.Infof(ctx, "DeleteInstances begin: %s", utils.ToJSON(req))

	instanceIDs := req.InstanceIDs
	if len(instanceIDs) == 0 {
		logger.Errorf(ctx, "InstanceIDs is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "instanceIDs is empty")
	}

	// 默认使用 instance 自带 deleteOption
	if req.DeleteOption == nil {
		logger.Infof(ctx, "DeleteOption is nil, using instance.Spec.DeleteOption instead")
	}

	// 兼容虚机ID和cceID, 拆分，分别处理
	var cceIDs []string // cceInstanceIDs
	var bccIDs []string // iaas resource id : bcc id 、 bbc id .
	for _, id := range instanceIDs {
		if err := utils.ValidateInstanceID(ctx, id); err != nil {
			logger.Errorf(ctx, "instance %s not valid, skip delete", id)
			continue
		}

		if strings.Contains(id, ccetypes.CCEPrefix) {
			cceIDs = append(cceIDs, id)
		} else {
			bccIDs = append(bccIDs, id)
		}
	}

	// 获取 Instance
	var instances []*models.Instance
	var instanceTmp []*models.Instance
	var err error

	if len(cceIDs) != 0 {
		instanceTmp, err = c.models.GetInstancesByCCEIDs(ctx, clusterID, cceIDs, c.accountID)
		instances = append(instances, instanceTmp...)
	}

	if len(bccIDs) != 0 {
		instanceTmp, err = c.models.GetInstancesByBCCIDs(ctx, clusterID, bccIDs, c.accountID)
		instances = append(instances, instanceTmp...)
	}

	if err != nil {
		logger.Errorf(ctx, "GetInstances from DB failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if len(instances) == 0 {
		logger.Warnf(ctx, "instances %v in %s not exist", instanceIDs, clusterID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "instances %v in %s not exist", instanceIDs, clusterID)
	}

	// 检查是否包含 master 节点
	for _, instance := range instances {
		if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
			logger.Warnf(ctx, "master %s cannot be deleted or removed", instance.Spec.InstanceName)
			c.errorHandlerV2(errorcode.NewMasterCannotDeletedOrRemoved(), errorcode.LevelByUser, "master %s cannot be deleted or removed", instance.Spec.InstanceName)
		}
	}

	logger.Infof(ctx, "Instance: %s", utils.ToJSON(instances))

	cceInstanceIDs := []string{}

	// 用于去除重复ID
	tmp := map[string]struct{}{}
	for _, instance := range instances {
		cceInstanceID := instance.Spec.CCEInstanceID
		if _, ok := tmp[cceInstanceID]; !ok {
			tmp[cceInstanceID] = struct{}{}
			cceInstanceIDs = append(cceInstanceIDs, cceInstanceID)
		}
	}

	// 检查是否允许删除 Node, 托管 Master 集群有最小 Node 数限制
	minNodeNum, deleteAllowed, err := c.deleteNodeAllowed(ctx, clusterID, len(cceInstanceIDs))
	if err != nil {
		logger.Errorf(ctx, "check deleteNodeAllowed failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	if deleteAllowed == false {
		msg := fmt.Sprintf("workers number should be no less than %d when masters in managed mode", minNodeNum)
		logger.Errorf(ctx, msg)
		c.errorHandlerV2(errorcode.NewManagedClusterMinNodeNumLimit(), errorcode.LevelByUser, msg)
	}

	// 更新 DB 中的 Instance DeleteOption, InstancePhase
	if err := c.models.UpdateInstancesDeleteOptionAndPhase(ctx, clusterID, cceInstanceIDs, c.accountID, req.DeleteOption); err != nil {
		logger.Errorf(ctx, "UpdateInstancesDeleteOptionAndPhase failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	//
	// 删除 Instance CRD
	var wg sync.WaitGroup
	var errs []error
	wg.Add(len(cceInstanceIDs))
	buffer := make(chan string, 50)

	for _, cceInstanceID := range cceInstanceIDs {
		buffer <- cceInstanceID
		go func(cceInstanceID string) {
			defer wg.Done()
			if err := deleteInstance(ctx, cceInstanceID, c.accountID, req.DeleteOption, c.models, c.clients.K8SClient); err != nil && !kerrors.IsNotFound(err) {
				logger.Errorf(ctx, "deleteInstance %s failed: %v", cceInstanceID, err)
				errs = append(errs, err)
			}
			<-buffer
		}(cceInstanceID)
	}

	wg.Wait()

	if len(errs) > 0 {
		c.errorHandlerV2(errorcode.NewInternalServerError(uerrors.NewAggregate(errs).Error()), errorcode.LevelByAdmin, uerrors.NewAggregate(errs).Error())
	}
	c.Data["json"] = ccesdk.DeleteInstancesResponse{
		RequestID: logger.GetRequestID(ctx),
	}

	c.ServeJSON()
}

// ListInstancesByPage - 返回 instance 列表，托管 master 不返回；支持模糊查询、排序、分页; 根据状态过滤
// List instances of cluster
func (c *InstanceController) ListInstancesByPage() {
	ctx := c.ctx

	clusterID := strings.TrimSpace(c.Ctx.Input.Param(":clusterID"))
	keywordType := strings.TrimSpace(c.GetString("keywordType"))
	keyword := strings.TrimSpace(c.GetString("keyword"))
	orderBy := strings.TrimSpace(c.GetString("orderBy"))
	order := strings.TrimSpace(c.GetString("order"))
	phases := strings.TrimSpace(c.GetString("phases"))
	becRegion := strings.TrimSpace(c.GetString("becRegion"))
	ips := strings.TrimSpace(c.GetString("ipList"))
	gpuType := strings.TrimSpace(c.GetString("gpuType"))
	calculateGPUCountRequested, err := c.GetBool("calculateGPUCountRequested", false)
	if err != nil {
		logger.Infof(ctx, "calculateGPUCountRequested not set")
		calculateGPUCountRequested = false
	}

	pageNo, err := c.GetInt("pageNo")
	if err != nil || pageNo < 1 {
		pageNo = ccesdk.PageNoDefault
	}

	pageSize, err := c.GetInt("pageSize")
	if err != nil || pageSize < 1 {
		pageSize = ccesdk.PageSizeDefault
	}

	// 支持按 IPList 查询
	var ipList []string
	if ips == "" {
		ipList = []string{}
	} else {
		ipList = strings.Split(ips, ",")
	}

	// 新增 clusterRole 用于列表页区分 master 和 worker
	// clusterRole = node 返回 node list
	// clusterRole = master 返回 master list
	// clusterRole = "" 返回 全部 list
	clusterRole := ccetypes.ClusterRole(strings.TrimSpace(c.GetString("clusterRole")))

	// 开启 enableInternalFields 附加 InstanceInternalFields 字段, 包含 FloatingIP
	enableInternalFields, err := c.GetBool("enableInternalFields")
	if err != nil {
		logger.Infof(ctx, "enableInternalFields not set")
		enableInternalFields = false
	}

	// 新增 enableUpgradeNodeFields 附加 UpgradeNodeFields 字段, 支持集群升级
	enableUpgradeNodeFields, err := c.GetBool("enableUpgradeNodeFields")
	if err != nil {
		logger.Infof(ctx, "enableUpgradeNodeFields not set")
		enableUpgradeNodeFields = false
	}

	// 新增：计费方式参数解析
	chargingType := strings.TrimSpace(c.GetString("chargingType"))
	var chargingTypePtr *string
	if chargingType != "" {
		// 参数校验
		if !isValidChargingType(chargingType) {
			c.errorHandlerV2(errorcode.NewInvalidParam("invalid chargingType"), errorcode.LevelByUser, "")
			return
		}
		chargingTypePtr = &chargingType
	}

	// 查询 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	// keywordType == k8sNodeName 转成 hostname, vpcIP 或 instanceName
	var isK8sNodeName bool
	if keywordType == "k8sNodeName" {
		isK8sNodeName = true
		if cluster.Spec.K8SCustomConfig.EnableHostname == true {
			keywordType = "hostname"
		} else {
			keywordType = "vpcIP"
		}
	}

	if orderBy == "k8sNodeName" {
		if cluster.Spec.K8SCustomConfig.EnableHostname == true {
			orderBy = "hostname"
		} else {
			orderBy = "vpcIP"
		}
	}

	logger.Infof(ctx, "ListInstancesByPage begin: clusterID=%v, accountID=%v, keywordType=%v, keyword=%v, orderBy=%v, order=%v, phases=%s, pageNo=%v, pageSize=%v, clusterRole=%v, bceRegion=%v, enableInternalFields=%v, enableUpgradeNodeFields=%v, ipList=%v, isK8sNodeName=%v, calculateGPUCountRequested=%v",
		clusterID, c.accountID, keywordType, keyword, orderBy, order, phases, pageNo, pageSize,
		clusterRole, becRegion, enableInternalFields, enableUpgradeNodeFields, ipList, isK8sNodeName, calculateGPUCountRequested)

	// 初始化用户集群 K8S Client, 仅支持这三种状态是因为其他状态集群可能访问 APIServer 失败
	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

		logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}

	// 初始化 service
	instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	instanceService = instanceService.WithResourceCache(resourceCache)

	// 查询 Instance 列表
	instancePage, err := instanceService.ListInstancesByPage(ctx, clusterID, keywordType, keyword,
		orderBy, order, phases, pageNo, pageSize, enableInternalFields, clusterRole, becRegion, enableUpgradeNodeFields, ipList, isK8sNodeName, gpuType, calculateGPUCountRequested, chargingTypePtr)
	if err != nil {
		if models.IsNotExist(err) {
			logger.Errorf(ctx, "cluster %s not exists", clusterID)
			c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, "cluster %s not exists", clusterID)
		}
		logger.Errorf(ctx, "ListInstancesByPage failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	c.Data["json"] = ccesdk.ListInstancesResponse{
		InstancePage: instancePage,
		RequestID:    logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// ListGPUTypes - 返回 GPUTypes 列表
// List GPUTypes of cluster
func (c *InstanceController) ListGPUTypes() {
	ctx := c.ctx

	clusterID := strings.TrimSpace(c.Ctx.Input.Param(":clusterID"))
	instanceGroupID := strings.TrimSpace(c.GetString("instanceGroupID"))

	// 查询 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	logger.Infof(ctx, "ListInstancesByPage begin: clusterID=%v, accountID=%v", clusterID, c.accountID)

	// 初始化用户集群 K8S Client, 仅支持这三种状态是因为其他状态集群可能访问 APIServer 失败
	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

		logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}

	// 初始化 service
	instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	// 查询 GPUTypes 列表
	logger.Infof(ctx, "ListGPUTypes begin: clusterID=%v, accountID=%v, instanceGroupID %v", clusterID, c.accountID, instanceGroupID)
	instanceService = instanceService.WithResourceCache(resourceCache)
	listGPUTypes, err := instanceService.ListGPUTypes(ctx, clusterID, instanceGroupID, c.accountID)
	if err != nil {
		if models.IsNotExist(err) {
			logger.Errorf(ctx, "cluster %s not exists", clusterID)
			c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, "cluster %s not exists", clusterID)
		}
		logger.Errorf(ctx, "ListGPUTypes failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	c.Data["json"] = ccesdk.ListGPUTypesResponse{
		GPUTypes:  listGPUTypes,
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// ListInstancesByInstanceGroupID - 返回节点组内 instance 列表；支持分页，支持批量
// List instances of instanceGroup
func (c *InstanceController) ListInstancesByInstanceGroupID() {
	ctx := c.ctx
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}
	instanceGroupID := c.GetPathParams(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
		return
	}

	keywordType := c.GetString("keywordType")
	keyword := c.GetString("keyword")
	orderBy := c.GetString("orderBy")
	order := c.GetString("order")
	phases := c.GetString("phases")
	gpuType := c.GetString("gpuType")
	calculateGPUCountRequested, err := c.GetBool("calculateGPUCountRequested", false)
	if err != nil {
		logger.Infof(ctx, "calculateGPUCountRequested not set")
	}

	pageNo, err := c.GetInt("pageNo", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid pageNo, %v", err).Error()), errorcode.LevelByUser, "")
		return
	}
	if pageNo < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid pageNo"), errorcode.LevelByUser, "")
		return
	}

	pageSize, err := c.GetInt("pageSize", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid pageSize, %v", err).Error()), errorcode.LevelByUser, "")
		return
	}
	if pageSize < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid pageSize"), errorcode.LevelByUser, "")
		return
	}

	// 适配enableUpgradeNodeFields参数
	enableUpgradeNodeFields, err := c.GetBool("enableUpgradeNodeFields", false)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid enableUpgradeNodeFields"), errorcode.LevelByUser, "")
		return
	}

	// 开启 enableInternalFields 附加 InstanceInternalFields 字段, 包含 FloatingIP
	enableInternalFields, err := c.GetBool("enableInternalFields", false)
	if err != nil {
		logger.Infof(ctx, "enableInternalFields not set")
	}

	logger.Infof(ctx, "list instances by instanceGroupID begin: clusterID=%v, instanceGroupID=%s, accountID=%v, keywordType=%v, keyword=%v, orderBy=%v, order=%v, phases=%s, pageNo=%v, pageSize=%v, gpuType=%s",
		clusterID, instanceGroupID, c.accountID, keywordType, keyword, orderBy, order, phases, pageNo, pageSize, gpuType)

	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		return
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
		return
	}

	// keywordType == k8sNodeName 转成 hostname, vpcIP 或 instanceName
	if keywordType == "k8sNodeName" {
		if cluster.Spec.K8SCustomConfig.EnableHostname {
			keywordType = "hostname"
		} else {
			keywordType = "vpcIP"
		}
	}

	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning {
		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}
	}

	instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		return
	}

	instanceService = instanceService.WithResourceCache(resourceCache)

	options := instancep.ListInstanceOptions{
		ClusterID:       clusterID,
		InstanceGroupID: instanceGroupID,
		KeywordType:     keywordType,
		Keyword:         keyword,
		PageParams: ccesdk.PageParams{
			PageNo:   pageNo,
			PageSize: pageSize,
			OrderBy:  ccesdk.InstanceOrderBy(orderBy),
			Order:    ccesdk.Order(order),
		},
		Phases:                     phases,
		EnableInternalFields:       enableInternalFields,
		EnableUpgradeNodeFields:    enableUpgradeNodeFields,
		GPUType:                    gpuType,
		CalculateGPUCountRequested: calculateGPUCountRequested,
	}
	page, err := instanceService.ListInstancesByInstanceGroup(ctx, options)
	if err != nil {
		logger.Errorf(ctx, "ListInstancesByInstanceGroup(%+v) failed, err: %v", options, err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		return
	}

	resp := ccesdk.ListInstancesByInstanceGroupIDResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(ctx),
		},
		Page: *page,
	}

	logger.Infof(ctx, "ListInstancesByInstanceGroupID success: %s", utils.ToJSON(resp))

	c.Data["json"] = resp
	c.ServeJSON()
}

// UnSupportedInstanceType - 不支持的 InstanceType
func (c *InstanceController) UnSupportedInstanceType() {
	ctx := c.ctx

	logger.Infof(ctx, "UnSupportedInstanceTypes : %v", c.config.UnSupportedInstanceTypes)

	c.Data["json"] = ccesdk.UnSupportedInstanceTypeResponse{
		InstanceTypes: c.config.UnSupportedInstanceTypes,
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(ctx),
		},
	}
	c.ServeJSON()
}

// GetInstanceCRD - 获取 instance crd
func (c *InstanceController) GetInstanceCRD() {
	ctx := c.ctx
	cceInstanceID := c.Ctx.Input.Param(":cceInstanceID")
	var err error

	logger.Infof(ctx, "GetInstanceCRD %s start", cceInstanceID)

	// 获取请求参数
	if cceInstanceID == "" {
		logger.Errorf(c.ctx, "cceInstanceID not specified")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "cceInstanceID not specified")
	}

	// 根据BCC InstanceID(兼容)获取Instance DB Model
	if !strings.Contains(cceInstanceID, ccetypes.CCEPrefix) {
		logger.Infof(ctx, "GetInstanceByBCCID")
		instance, err := c.models.GetInstanceByBCCID(ctx, cceInstanceID, c.accountID)
		if err != nil {
			logger.Errorf(c.ctx, "get instance from db failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
		}

		cceInstanceID = instance.Spec.CCEInstanceID
	}

	service, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, nil)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	instanceCRD, err := service.GetInstanceCRD(ctx, cceInstanceID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceCRD failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	c.Data["json"] = ccesdk.GetInstanceCRDResponse{
		Instance:  instanceCRD,
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// UpdateInstanceCRD - kubectl apply instance yaml
func (c *InstanceController) UpdateInstanceCRD() {
	ctx := c.ctx

	// 解析请求体
	applyReq := new(ccesdk.UpdateInstanceCRDRequest)
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, applyReq); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RequestBody to InstanceSpec failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "RequestBody format not json")
	}

	logger.Infof(ctx, "UpdateInstanceCRD start: %s", utils.ToJSON(applyReq))

	// 为了保证instance 字段是完整的, 这里校验下 metadata 字段
	if applyReq.Instance.ObjectMeta.Name == "" {
		logger.Errorf(c.ctx, "request body should be complete instance crd")
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "RequestBody format not complete instance crd")
	}

	service, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, nil)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	// apply yaml
	err = service.UpdateInstanceCRD(ctx, applyReq.Instance)
	if err != nil {
		logger.Errorf(ctx, "UpdateInstanceCRD failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	c.Data["json"] = ccesdk.CommonResponse{
		RequestID: logger.GetRequestID(ctx),
	}

	c.ServeJSON()
}

// UpdateNodeScaleDown - 节点缩容保护
func (c *InstanceController) UpdateNodeScaleDown() {
	ctx := c.ctx
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}
	// 解析请求体
	applyReq := new(ccesdk.UpdateNodeScaleDownRequest)
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, applyReq); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RequestBody to InstanceSpec failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "RequestBody format not json")
		return
	}

	logger.Infof(ctx, "UpdateInstanceScaleDownDisabled start: %s", utils.ToJSON(applyReq))

	// 为了保证instance 字段是完整的, 这里校验下 metadata 字段
	// 如果instanceIDs是空的，则直接返回
	if len(applyReq.InstanceIDs) <= 0 {
		logger.Infof(c.ctx, "InstanceIDs is empty, return")
		c.Data["json"] = ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(ctx),
		}
		c.ServeJSON()
		return
	}
	// 查询 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		return
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
		return
	}
	// 初始化用户集群 K8S Client, 仅支持这三种状态是因为其他状态集群可能访问 APIServer 失败
	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

		logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}
	}

	service, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
		return
	}

	// 部分成功，只有全部失败，或者传入实例中不满足变更操作时，会返回报错信息。这个时候客户理论上都能根据报错信息处理
	failedInstances, err := service.UpdateInstanceScaleDownDisabled(ctx, clusterID, *applyReq)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "not belong to cluster") || strings.Contains(err.Error(), "not support set scale-down-Disabled"):
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser,
				fmt.Sprintf("error is %s, failedInstances is %s", err.Error(), utils.ToJSON(failedInstances)))
		default:
			logger.Errorf(ctx, "UpdateInstanceScaleDownDisabled failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser,
				fmt.Sprintf("error is %s, failedInstances is %s", err.Error(), utils.ToJSON(failedInstances)))
		}
		return
	}

	c.Data["json"] = ccesdk.UpdateNodeScaleDownResponse{
		FailedInstances: failedInstances,
		RequestID:       logger.GetRequestID(ctx),
	}

	c.ServeJSON()
}

// CordonNodes - cordon or uncordon node
func (c *InstanceController) CordonNodes() {
	ctx := c.ctx

	var req ccesdk.CordonNodesRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	logger.Infof(ctx, "CordonNodes begin: %s", utils.ToJSON(req))

	clusterID := req.ClusterID
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
		logger.Errorf(ctx, "cluster %s not running", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser, "cluster %s not running", clusterID)
	}

	userConfig, err := c.models.GetKubeConfigCompatibility(ctx, clusterID, c.userID, models.KubeConfigTypeInternal)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "failed to get config: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "failed to get config")
	}

	if models.IsNotExist(err) || userConfig == nil {
		logger.Errorf(ctx, "need rbac permission")
		c.errorHandlerV2(errorcode.NewRBACUnauthorized(), errorcode.LevelByUser, "")
	}

	userK8SClient, err := utils.NewK8SClient(ctx, userConfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "failed to create client: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "failed to create k8s client")
	}

	if len(req.VKNodeNames) > 0 {
		if err := c.cordonVKNodes(ctx, clusterID, req.VKNodeNames, req.Cordon); err != nil {
			logger.Errorf(ctx, "failed to cordon vk node: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
		}
	}

	if len(req.CCEInstanceIDs) > 0 {
		instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, userK8SClient)
		if err != nil {
			logger.Errorf(ctx, "NewService failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}

		err = instanceService.CordonNodes(ctx, clusterID, req.CCEInstanceIDs, req.Cordon)
		if err != nil {
			logger.Errorf(ctx, "CordonNodes failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}

	logger.Infof(ctx, "CordonNodes succeeded: %s", utils.ToJSON(req))

	c.Data["json"] = ccesdk.CommonResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// DrainNodes - drain node
func (c *InstanceController) DrainNodes() {
	ctx := c.ctx

	var req ccesdk.DrainNodesRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	logger.Infof(ctx, "DrainNodes begin: %s", utils.ToJSON(req))

	clusterID := req.ClusterID
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
		logger.Errorf(ctx, "cluster %s not running", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser, "cluster %s not running", clusterID)
	}

	userConfig, err := c.models.GetKubeConfigCompatibility(ctx, clusterID, c.userID, models.KubeConfigTypeInternal)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "failed to get config: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "failed to get config")
	}

	if models.IsNotExist(err) || userConfig == nil {
		logger.Errorf(ctx, "need rbac permission")
		c.errorHandlerV2(errorcode.NewRBACUnauthorized(), errorcode.LevelByUser, "")
	}

	userK8SClient, err := utils.NewK8SClient(ctx, userConfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "failed to create client: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "failed to create k8s client")
	}

	instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, userK8SClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	err = instanceService.DrainNodes(ctx, clusterID, req.CCEInstanceIDs, req.DrainNodeConfig)
	if err != nil {
		logger.Errorf(ctx, "DrainNodes failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	logger.Infof(ctx, "DrainNodes succeeded: %s", utils.ToJSON(req))

	c.Data["json"] = ccesdk.CommonResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// UpdateGPUShare - set node gpu share or not
func (c *InstanceController) UpdateGPUShare() {
	ctx := c.ctx

	var req ccesdk.GPUShareNodesRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	logger.Infof(ctx, "UpdateGPUShare begin: %s", utils.ToJSON(req))

	clusterID := req.ClusterID
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
		logger.Errorf(ctx, "cluster %s not running", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser, "cluster %s not running", clusterID)
	}

	k8sClient, err := getClientSet(ctx, clusterID, c.models)
	if err != nil {
		logger.Errorf(ctx, "get k8s client failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	err = instanceService.GPUShareNodes(ctx, clusterID, req.CCEInstanceIDs, req.GPUShare, req.GPUHybrid, c.userID)
	if err != nil {
		logger.Errorf(ctx, "UpdateGPUShare failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByUser, err.Error())
	}

	if len(req.VKNodeNames) > 0 {
		logger.Infof(ctx, "vk node does not support updateGPUShare")
	}

	logger.Infof(ctx, "UpdateGPUShare succeeded: %s", utils.ToJSON(req))

	c.Data["json"] = ccesdk.CommonResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

// ResetInstanceRetryCount - Reset Instance.Status.RetryCount to 0
func (c *InstanceController) ResetInstanceRetryCount() {
	ctx := c.ctx

	// 获取 ClusterID
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "clusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
	}

	// 获取 Instance cceInstanceID
	cceInstanceID := c.Ctx.Input.Param(":cceInstanceID")
	if cceInstanceID == "" {
		logger.Errorf(ctx, "cceInstanceID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "cceInstanceID is empty")
	}

	skipReleaseENI := c.Ctx.Input.Query("skipReleaseENI")
	logger.Infof(c.ctx, "skip release eni is: %v", skipReleaseENI)
	if skipReleaseENI == "" {
		skipReleaseENI = "false"
	}
	skipReleaseENIBool, err := strconv.ParseBool(skipReleaseENI)
	if err != nil {
		logger.Errorf(ctx, "skipReleaseENI is not bool: %s", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "skipReleaseENI is not bool")
	}

	logger.Infof(ctx, "ResetInstanceRetryCount clusterID %s cceInstanceID %s", clusterID, cceInstanceID)

	// 更新 Instance.Status.RetryCount to 0
	if err := resetInstanceRetryCount(ctx, clusterID, cceInstanceID, c.clientSet.MetaClient, skipReleaseENIBool); err != nil {
		logger.Errorf(ctx, "resetInstanceRetryCount failed: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	// 返回结果
	c.Data["json"] = ccesdk.CommonResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

func (c *InstanceController) SkipInstanceReconcileStep() {
	ctx := c.ctx

	// 获取 ClusterID
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		logger.Errorf(ctx, "clusterID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID is empty")
		return
	}

	// 获取 Instance cceInstanceID
	cceInstanceID := c.Ctx.Input.Param(":cceInstanceID")
	if cceInstanceID == "" {
		logger.Errorf(ctx, "cceInstanceID is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "cceInstanceID is empty")
		return
	}

	var req ccesdk.SkipInstanceReconcileStepRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	logger.Infof(ctx, "SkipInstanceReconcileStep clusterID %s cceInstanceID %s", clusterID, cceInstanceID)

	if err := c.skipInstanceReconcileStep(ctx, &req); err != nil {
		logger.Errorf(ctx, "skipInstanceReconcileStep failed: %v", err)
		c.errorHandlerV2(errorcode.GetCode(err), errorcode.LevelByUser, err.Error())
		return
	}

	c.Data["json"] = ccesdk.SkipInstanceReconcileStepResponse{
		RequestID: logger.GetRequestID(ctx),
	}
	c.ServeJSON()
}

func (c *InstanceController) skipInstanceReconcileStep(ctx context.Context, req *ccesdk.SkipInstanceReconcileStepRequest) error {
	// 获取 InstanceCRD
	instance, err := c.clientSet.MetaClient.GetInstance(ctx, "default", req.CCEInstanceID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetInstance failed: %s", err)
		return err
	}

	// 检查 Instance 对应 ClusterID
	if instance.Spec.ClusterID != req.ClusterID {
		logger.Errorf(ctx, "instance.Spec.ClusterID %s != clusterID %s", instance.Spec.ClusterID, req.ClusterID)
		return errorcode.NewInvalidParam(fmt.Sprintf("instance %s not belongs to cluster %s", req.CCEInstanceID, req.ClusterID))
	}

	// 目前仅支持在删除实例阶段跳过“重装”阶段
	//	TODO：未来扩展支持在其他阶段跳过
	if instance.Status.InstancePhase != ccetypes.InstancePhaseDeleting && instance.Status.InstancePhase != ccetypes.InstancePhaseDeleteFailed {
		return errorcode.NewInvalidParam(fmt.Sprintf("instance %s is not in deleting phase", req.CCEInstanceID))
	}
	if req.Step != string(ccetypes.InstanceStepRebuildMachine) {
		return errorcode.NewInvalidParam(fmt.Sprintf("step %s not supported", req.Step))
	}

	// 检查 Step 是否存在
	if _, ok := instance.Status.ReconcileDeleteSteps[ccetypes.InstanceStepRebuildMachine]; !ok {
		return errorcode.NewInvalidParam(fmt.Sprintf("step %s not found", req.Step))
	}

	instance.Labels[ccev1.LabelInstanceSkipCheckRebuildMachine] = "true"

	if err := c.clientSet.MetaClient.UpdateInstance(ctx, "default", instance.Name, instance); err != nil {
		logger.Errorf(ctx, "UpdateInstance failed: %s", err)
		return err
	}

	if instance.Status.InstancePhase == ccetypes.InstancePhaseDeleteFailed {
		return resetInstanceRetryCount(ctx, instance.Spec.ClusterID, instance.Spec.CCEInstanceID, c.clientSet.MetaClient, false)
	}

	return nil
}

// GetAvailableConfig - get Available configmap value
func (c *InstanceController) GetAvailableConfig() {
	ctx := c.ctx

	keyword := strings.TrimSpace(c.GetString("configKey"))
	if keyword == "" {
		logger.Errorf(ctx, "configKey keyword is empty")
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "configKey keyword is empty")
	}

	clusterServiceConfig, err := configuration.GetClusterServiceConfig(ctx)
	if err != nil {
		logger.Errorf(ctx, "getClusterServiceConfig failed: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	AvailableConfigResponse := ccesdk.AvailableConfigResponse{}
	if keyword == string(ccetypes.AvailableConfigEBCSpecs) {
		AvailableConfigResponse.AvailableEBCMachineSpecs = clusterServiceConfig.AvailableEBCMachineSpecs
	}

	// 返回结果
	c.Data["json"] = AvailableConfigResponse
	c.ServeJSON()
}

// deleteInstanceCRD - 删除 Instance
//
// 如果 InstanceCRD 不存在, 则更新数据库状态为 deleted
// 如果 InstanceCRD 存在, 则删除 InstanceCRD
// 主要用于处理, 数据库中存在, 但是 MetaCluster 不存在的问题
func deleteInstance(ctx context.Context, cceInstanceID, accountID string, deleteOption *ccetypes.DeleteOption, model models.Interface, k8sClient k8s.Interface) error {
	// 检查 CRD 是否存在
	instance, err := k8sClient.GetInstance(ctx, cceInstanceID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance %s failed: %v", cceInstanceID, err)
		return err
	}

	// CRD 不存在
	// TODO: 这里应该使用 GetInstance 错误码, 但直接改 meta package 可能影响很多逻辑有稳定性风险, 暂时不处理
	if instance == nil {
		logger.Infof(ctx, "Instance exists in DB, but not in MetaCluster, update DB instance_phase")

		if err := model.UpdateInstancePhase(ctx, cceInstanceID, accountID, ccetypes.InstancePhaseDeleted); err != nil {
			logger.Errorf(ctx, "UpdateInstancePhase %s failed: %v", cceInstanceID, err)
			return err
		}
	}

	// CRD 存在
	if instance != nil {
		if err := k8sClient.ReconcileInstanceDeleted(ctx, cceInstanceID, deleteOption); err != nil {
			logger.Errorf(ctx, "ReconcileInstanceDeleted %s failed: %v, to be ensured in synchronization", cceInstanceID, err)
			return err
		}
	}

	return nil
}

// deleteNodeAllowed - 检查是否允许删除 Node, 托管 Master 集群有最小 Node 数限制
func (c *InstanceController) deleteNodeAllowed(ctx context.Context, clusterID string, deleteNodeNum int) (int, bool, error) {
	// 托管 Master 集群默认最小 Node 数
	minNodeNum := c.config.ManagedClusterMinNodeNum

	cluster, err := c.clients.K8SClient.GetCluster(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "K8SClient.GetCluster failed: %v", err)
		return minNodeNum, false, err
	}

	if cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeManaged &&
		cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeContainerizedManaged &&
		cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeManagedPro &&
		cluster.Spec.MasterConfig.MasterType != ccetypes.MasterTypeManagedOld {
		return minNodeNum, true, nil
	}

	// 正在删除中的集群无限制
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseDeleting || cluster.Status.ClusterPhase == ccetypes.ClusterPhaseDeleteFailed {
		return minNodeNum, true, nil
	}

	// 如果 annotations 设置, 则使用 annotations 值
	if minStr, ok := cluster.Annotations[ccev1.AnnotationManagedClusterMinNodeNum]; ok == true {
		min, err := strconv.Atoi(minStr)
		if err == nil {
			minNodeNum = min
		}
	}

	// minNodeNum <= 0 表示无限制
	if minNodeNum <= 0 {
		return minNodeNum, true, nil
	}

	currentNodeNum, err := c.models.GetInstanceNum(ctx, c.accountID, clusterID, models.InstanceKeywordTypeDefault, "", false)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceNum failed: %v", err)
		return minNodeNum, false, err
	}

	allow := true
	if currentNodeNum-deleteNodeNum < minNodeNum {
		allow = false
	}

	return minNodeNum, allow, nil
}

func checkInstancesCount(instanceSet []*ccesdk.InstanceSet) int {
	total := 0
	if len(instanceSet) == 0 {
		return total
	}

	for _, set := range instanceSet {
		if set.Count < 1 {
			if set.InstanceSpec.Existed && set.InstanceSpec.ExistedOption.ExistedInstanceID != "" {
				// 如果是已有节点，请求参数中count可能为0, 这个时候要手动+1
				total += 1
			}
			continue
		}
		total = total + set.Count
	}

	return total
}

func (c *InstanceController) getK8SNodeDetail(ctx context.Context, instance *ccesdk.Instance) (*appnode.NodeDetail, error) {
	if instance == nil {
		return nil, fmt.Errorf("instance is nil")
	}

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	clusterID := instance.Spec.ClusterID

	// 获取 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "models.GetCluster failed: %s", err)
		return nil, err
	}

	// 获取 NodeName
	nodeName := utils.GetNodeName(ctx, instance.Status.Machine.VPCIP, instance.Status.Machine.Hostname, instance.Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)

	// 通过 APPService 获取 NodeDetail
	nodeDetail, err := c.clients.APPServiceClient.GetNodeByName(ctx, clusterID, nodeName, true, c.clients.STSClient.NewSignOption(ctx, c.userID))
	if err != nil {
		logger.Errorf(ctx, "APPServiceClient.GetNodeByName failed: %s", err)
		return nil, err
	}

	return nodeDetail, nil
}

func (c *InstanceController) getVKNodeDetail(ctx context.Context, clusterID, nodeName string) (*appnode.NodeDetail, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 通过 APPService 获取 NodeDetail
	// 控制 app-service 是否查询 podList eventList metrics
	// 如果是 vk 节点不需要查询
	// 这行是跳过单测用的
	nodeDetail, err := c.clients.APPServiceClient.GetNodeByName(ctx, clusterID, nodeName, false, c.clients.STSClient.NewSignOption(ctx, c.userID))
	if err != nil {
		logger.Errorf(ctx, "APPServiceClient.GetNodeByName failed: %s", err)
		return nil, err
	}

	return nodeDetail, nil
}

// resetInstanceRetryCount - Set Instance.Status.RetryCount to 0
func resetInstanceRetryCount(ctx context.Context, clusterID, cceInstanceID string, metaClient meta.Interface, skipReleaseENI bool) error {
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if cceInstanceID == "" {
		return fmt.Errorf("cceInstanceID is empty")
	}

	if metaClient == nil {
		return fmt.Errorf("metaClient is nil")
	}

	// 获取 InstanceCRD
	instance, err := metaClient.GetInstance(ctx, "default", cceInstanceID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetInstance failed: %s", err)
		return err
	}

	if instance != nil && skipReleaseENI {
		if instance.Labels == nil {
			instance.Labels = make(map[string]string)
		}

		instance.Labels[ccev1.LabelInstanceSkipDeleteENI] = "true"
		if metaClient.UpdateInstance(ctx, "default", cceInstanceID, instance) != nil {
			logger.Errorf(ctx, "UpdateInstance failed: %s", err)
			return err
		}
	}

	// 检查 Instance 对应 ClusterID
	if instance.Spec.ClusterID != clusterID {
		logger.Errorf(ctx, "instance.Spec.ClusterID %s != clusterID %s", instance.Spec.ClusterID, clusterID)
		return fmt.Errorf("Instance %s not belongs to cluster %s", cceInstanceID, clusterID)
	}

	// 仅设置 RetryCount 达到最大次数
	if instance.Status.RetryCount < controllerDefaultMaxRetryCount {
		logger.Infof(ctx, "Instance retryCount %d < %d, skip update", instance.Status.RetryCount, controllerDefaultMaxRetryCount)
		return nil
	}

	if instance.Status.InstancePhase == ccetypes.InstancePhaseRunning {
		logger.Infof(ctx, "Instance phase is running, skip update")
		return nil
	}

	// 更新 RetryCount 至 0
	instance.Status.RetryCount = 0
	// 更新 step 信息
	for k, v := range instance.Status.ReconcileSteps {
		if v.Ready {
			continue
		}
		delete(instance.Status.ReconcileSteps, k)
	}

	for k, v := range instance.Status.ReconcileDeleteSteps {
		if v.Ready {
			continue
		}
		delete(instance.Status.ReconcileDeleteSteps, k)
	}

	// 更新 phase
	switch instance.Status.InstancePhase {
	case ccetypes.InstancePhaseCreateFailed:
		instance.Status.InstancePhase = ccetypes.InstancePhasePending
	case ccetypes.InstancePhaseDeleteFailed:
		instance.Status.InstancePhase = ccetypes.InstancePhaseDeleting
	default:
		return errorcode.NewCurrentPhaseCannotRetry(string(instance.Status.InstancePhase))
	}

	if err := metaClient.UpdateInstanceStatus(ctx, "default", cceInstanceID, &instance.Status); err != nil {
		logger.Errorf(ctx, "UpdateInstance failed: %s", err)
		return err
	}

	return nil
}

// checkInstancesCountBelowLimit - 检测 InstanceSet 数量不超过 limit
func checkInstancesCountBelowLimit(ctx context.Context, instanceSet []*ccesdk.InstanceSet, limit int) error {
	total := 0

	for _, set := range instanceSet {
		// 新增实例
		// 不处理已有实例是因为 InstanceGroup Controller 会使用该接口写入已有实例(节点组已限制50数量)
		if set.Count > 0 {
			total = total + set.Count
			continue
		}
		// 兼容前端纳管已有实例时，count不传，数量默认为1
		if set.Count == 0 && set.InstanceSpec.Existed {
			total += 1
		}
	}

	if total > limit {
		return fmt.Errorf("Total instance count %d > limit %d", total, limit)
	}

	return nil
}

func (c *InstanceController) checkCCENodeQuota(ctx context.Context, cluster *ccev1.Cluster, addNum int) (bool, error) {
	if cluster == nil {
		return false, fmt.Errorf("cluster is nil")
	}

	nodeQuota, err := c.quotaClient.GetNodeQuota(ctx, cluster.Spec.ClusterID)
	if err != nil {
		logger.Errorf(ctx, "GetNodeQuota failed: %v", err)
		return false, err
	}

	logger.Infof(ctx, "cluster %s node quota: %d, created: %d, add: %d", cluster.Spec.ClusterID, nodeQuota.Quota, nodeQuota.Used, addNum)

	return nodeQuota.Quota >= nodeQuota.Used+addNum, nil
}

func (c *InstanceController) getVKNodeResp(ctx context.Context, clusterID, instanceID string) (*ccesdk.Instance, error) {
	vkDetail, err := c.getVKNodeDetail(ctx, clusterID, instanceID)
	if err != nil {
		logger.Errorf(ctx, "get vkDetail failed: %s", err)
		return nil, err
	}

	res := &ccesdk.Instance{
		Spec: &ccesdk.InstanceSpec{
			ClusterID:     clusterID,
			CCEInstanceID: instanceID,
			ClusterRole:   ccetypes.ClusterRoleNode,
		},
		Status: &ccesdk.InstanceStatus{
			Machine: ccesdk.Machine{
				K8SNodeName: vkDetail.ObjectMeta.Name,
				VPCIP: instancep.K8sNodeInternalIP(ctx, &corev1.Node{
					Status: corev1.NodeStatus{
						Addresses: vkDetail.Addresses,
					},
				}),
			},
		},
		K8SNode:   vkDetail,
		IsVKNode:  true,
		CreatedAt: vkDetail.ObjectMeta.CreationTimestamp.Time,
	}

	var k8sClient kubernetes.Interface

	k8sClient, err = getClientSet(ctx, clusterID, c.models)
	if err != nil {
		logger.Errorf(ctx, "get k8s client failed: %v", err)
		return nil, err
	}

	// 与列表查询的可用状态保持一致
	res.Status.InstancePhase, err = instancep.GetVKNodeStatus(ctx, res, instanceID, k8sClient)
	if err != nil {
		logger.Errorf(ctx, "GetK8sNodeStatus failed: %v", err)
		return nil, err
	}
	return res, err
}

func (c *InstanceController) cordonVKNodes(ctx context.Context, clusterID string, cceInstanceIDs []string, isCordon bool) error {
	k8sClient, err := c.clientSet.Factory.NewK8SClientWithClusterID(ctx, clusterID, c.models)
	if err != nil {
		return err
	}
	if k8sClient == nil {
		return fmt.Errorf("k8s client is nil")
	}
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	nodesNum := len(cceInstanceIDs)
	if nodesNum == 0 {
		return fmt.Errorf("cceInstanceIDs is empty")
	}

	g, ctx := errgroup.WithContext(ctx)
	for _, nodeName := range cceInstanceIDs {
		nodeName := nodeName
		g.Go(func() error {
			node, err := k8sClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
			if err != nil {
				logger.Warnf(ctx, "get node %s in cluster %s failed: %v", nodeName, clusterID, err)
				return err
			}

			// NotReady 的节点也允许执行 Cordon
			if c.nodeStatus(node) != corev1.ConditionTrue {
				logger.Warnf(ctx, "node %s not ready", nodeName)
				// errChan <- fmt.Errorf("node %s not ready", cceInstanceID)
				// return
			}

			unschedulable := node.Spec.Unschedulable

			if isCordon && !unschedulable {
				_, err := c.cordonNode(ctx, node, k8sClient)
				return err
			}

			if !isCordon && unschedulable {
				_, err := c.uncordonNode(ctx, node, k8sClient)
				return err
			}

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}

func (c *InstanceController) cordonNode(ctx context.Context, node *corev1.Node, k8sClient kubernetes.Interface) (*corev1.Node, error) {
	exist := false
	for _, taint := range node.Spec.Taints {
		if taint.Key == taintKeyNodeUnschedulable && taint.Effect == corev1.TaintEffectNoSchedule {
			exist = true
			break
		}
	}

	if !exist {
		node.Spec.Taints = append(node.Spec.Taints, corev1.Taint{
			Key:    taintKeyNodeUnschedulable,
			Effect: corev1.TaintEffectNoSchedule,
			TimeAdded: &metav1.Time{
				Time: time.Now(),
			},
		})
	}
	node.Spec.Unschedulable = true

	updatedNode, err := k8sClient.CoreV1().Nodes().Update(ctx, node, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update node %s failed: %v", err)
		return nil, err
	}

	return updatedNode, nil
}

func (c *InstanceController) uncordonNode(ctx context.Context, node *corev1.Node, k8sClient kubernetes.Interface) (*corev1.Node, error) {
	taints := make([]corev1.Taint, 0)
	for _, taint := range node.Spec.Taints {
		if taint.Key == taintKeyNodeUnschedulable && taint.Effect == corev1.TaintEffectNoSchedule {
			continue
		}
		taints = append(taints, taint)
	}

	node.Spec.Taints = taints
	node.Spec.Unschedulable = false

	updatedNode, err := k8sClient.CoreV1().Nodes().Update(ctx, node, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update node %s failed: %v", err)
		return nil, err
	}

	return updatedNode, nil
}

func (c *InstanceController) nodeStatus(node *corev1.Node) corev1.ConditionStatus {
	nodeStatus := corev1.ConditionUnknown
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady {
			nodeStatus = condition.Status
			break
		}
	}
	return nodeStatus
}

func (c *InstanceController) RebuildInstance() {
	ctx := c.ctx
	clusterID := c.Ctx.Input.Param(":clusterID")
	instanceID := c.Ctx.Input.Param(":instanceID")

	rebuildInstanceReq := new(ccesdk.RebuildInstanceRequest)
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, rebuildInstanceReq); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RequestBody to RebuildInstanceRequest failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "RequestBody format not json")
	}

	logger.Infof(ctx, "RebuildInstance start: %s", utils.ToJSON(rebuildInstanceReq))

	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
		logger.Errorf(ctx, "cluster %s not running", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByUser, "cluster %s not running", clusterID)
	}

	var instance *models.Instance

	// 根据CCE InstanceID或BCC InstanceID(兼容二者)获取Instance DB Model
	if strings.Contains(instanceID, ccetypes.CCEPrefix) {
		logger.Infof(ctx, "GetInstanceByCCEID")
		instance, err = c.models.GetInstanceByCCEID(ctx, instanceID, c.accountID)
	} else {
		logger.Infof(ctx, "GetInstanceByBCCID")
		instance, err = c.models.GetInstanceByBCCID(ctx, instanceID, c.accountID)
	}

	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetInstance failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	if models.IsNotExist(err) || instance == nil {
		logger.Errorf(ctx, "Instance %s not exists", instance.Spec.CCEInstanceID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not exists", instance.Spec.CCEInstanceID)
	}

	if instance.Spec.ClusterID != clusterID {
		logger.Errorf(ctx, "Instance %s not belong to Cluster %s", instance.Spec.CCEInstanceID, clusterID)
		c.errorHandlerV2(errorcode.NewInstanceNotFound(), errorcode.LevelByUser, "Instance %s not belong to Cluster %s", instance.Spec.CCEInstanceID, clusterID)
	}

	logger.Infof(ctx, "models.Instance: %s", utils.ToJSON(instance))

	service, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, nil)
	if err != nil {
		logger.Errorf(ctx, "NewService failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	instanceCRD, err := service.GetInstanceCRD(ctx, instance.Spec.CCEInstanceID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceCRD failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	if instanceCRD.Status.Machine.InstanceID == "" && instanceCRD.Spec.ExistedOption.ExistedInstanceID == "" {
		logger.Errorf(ctx, "Instance %s has no Machine Info yet, can not be rebuilt", instance.Spec.CCEInstanceID)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "Instance %s has no Machine Info yet, can not be rebuilt", instance.Spec.CCEInstanceID)
	}

	retrieverInstance, err := retriever.NewClient(ctx, c.accountID, instance.Spec.MachineType, c.clients)
	if err != nil {
		logger.Errorf(ctx, "retriever.NewClient failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, err.Error())
	}

	service = service.WithImageRetriever(retrieverInstance)
	var needToRebuildOS bool
	if rebuildInstanceReq.ReInstallOsConfig != nil && rebuildInstanceReq.ReInstallOsConfig.Enabled {
		logger.Infof(ctx, "ReInstallOsConfig is enabled")
		needToRebuildOS = true
	}
	if err := service.RebuildInstance(ctx, instance.Spec.CCEInstanceID, rebuildInstanceReq.ImageID, needToRebuildOS); err != nil {
		logger.Errorf(ctx, "RebuildInstance failed: %v", err)
		c.errorHandlerV2(errorcode.GetCode(err), errorcode.LevelByUser, err.Error())
	}

	c.Data["json"] = ccesdk.RebuildInstanceResponse{
		RequestID:  logger.GetRequestID(ctx),
		ClusterID:  clusterID,
		InstanceID: instanceID,
	}

	c.ServeJSON()
}

var (
	// 集群名称-地域-年-月-日_list.csv
	filenameFormat = "%s-%s-%s_list.csv"

	instanceDownloadCSVHeader = []string{
		"节点名称", "实例名称", "实例ID", "状态", "支付方式", "可用区", "IP地址", "配置/类型",
		"加速芯片类型", "加速芯片（已使用/总卡数）", "空闲加速芯片卡数", "资源申请 | 限制",
		"容器组（已分配/总额度）", "节点组", "创建时间",
	}
)

// convertInfosToCSV 将信息转换成CSV格式信息，顺序按照instancesID顺序，如果全量，则按照默认传入顺序。infos已排好序
func convertInfosToCSV(ctx context.Context, infos *ccesdk.InstancePage) [][]string {
	result := make([][]string, 0)
	if infos == nil {
		return result
	}
	for _, info := range infos.InstanceList {
		if info == nil || info.Spec == nil || info.Status == nil {
			logger.Warnf(ctx, "info is nil")
			continue
		}
		temp := []string{
			info.Status.Machine.K8SNodeName,        // 节点名称
			info.Spec.InstanceName,                 // 实例名称
			info.Spec.CCEInstanceID,                // 实例ID
			info.Status.InstancePhase.Human(),      // 节点状态
			info.Spec.InstanceChargingType.Human(), // 支付方式
			fmt.Sprintf("可用区%s", []byte(strings.TrimPrefix(info.Spec.VPCConfig.AvailableZone, "zone"))), // 可用区
			info.Status.Machine.VPCIP, // IP地址
		}

		// 配置/类型
		machineSpecList := strings.Split(info.Spec.InstanceResource.MachineSpec, ".")
		var machineConfig string
		if len(machineSpecList) >= 2 {
			machineConfig = fmt.Sprintf("%d核/%dGB/%dGB/%s", info.Spec.InstanceResource.CPU, info.Spec.InstanceResource.MEM,
				info.Spec.InstanceResource.RootDiskSize, machineSpecList[1])
		} else {
			machineConfig = fmt.Sprintf("%d核/%dGB/%dGB", info.Spec.InstanceResource.CPU, info.Spec.InstanceResource.MEM,
				info.Spec.InstanceResource.RootDiskSize)
		}

		temp = append(temp, machineConfig)

		// 加速芯片类型
		temp = append(temp, string(info.Spec.InstanceResource.GPUType))
		// 加速芯片（已使用/总卡数）
		temp = append(temp, fmt.Sprintf(`"%d/%d"`, info.Status.Resources.GPUCountRequested, info.Spec.InstanceResource.GPUCount))
		// 空闲加速芯片卡数
		temp = append(temp, fmt.Sprintf("%d", info.Status.Resources.GPUCountRemaining))

		if info.K8SNode != nil {
			// 资源申请 | 限制
			temp = append(temp, fmt.Sprintf("CPU: %.2f%% | %.2f%% \n内存: %.2f%% | %.2f%%", info.K8SNode.AllocatedResources.CPURequestsFraction,
				info.K8SNode.AllocatedResources.CPULimitsFraction, info.K8SNode.AllocatedResources.MemoryRequestsFraction,
				info.K8SNode.AllocatedResources.MemoryLimitsFraction))
			// 容器组（已分配/总额度）
			temp = append(temp, fmt.Sprintf(`"%d/%d"`, info.K8SNode.AllocatedResources.AllocatedPods, info.K8SNode.AllocatedResources.PodCapacity))
		} else {
			temp = append(temp, "CPU: - | - \n内存: - | -", "- / -")
		}
		// 节点组
		if info.Spec.InstanceGroupID == "" {
			temp = append(temp, "")
		} else {
			temp = append(temp, fmt.Sprintf("%s（%s）", info.Spec.InstanceGroupName, info.Spec.InstanceGroupID))
		}
		// 创建时间
		temp = append(temp, clusterserviceutils.ConvertTimeToTimeZone(info.CreatedAt, "Asia/Shanghai").Format("2006-01-02 15:04:05"))

		// 完成一个节点的添加
		result = append(result, temp)
	}

	return result
}

// InstancesDownload 节点列表导出，生成 {集群名称-地域-年-月-日_list.csv} 文件
func (c *InstanceController) InstancesDownload() {
	ctx := c.ctx
	clusterID := c.Ctx.Input.Param(":clusterID")
	region := c.Ctx.Input.Header("x-region")

	if clusterID == "" {
		logger.Errorf(ctx, "clusterID is empty")
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "clusterID is empty")
		return
	}

	// 获取需要导出的节点
	var req ccesdk.InstancesDownloadRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RequestBody failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}
	logger.Infof(ctx, "InstancesDownload begin, InstancesDownloadRequest: %s", utils.ToJSON(req))

	// 获取集群
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		return
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
		return
	}

	// 初始化用户集群 K8S Client, 仅支持这三种状态是因为其他状态集群可能访问 APIServer 失败
	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning && cluster.Status.ClusterPhase != ccetypes.ClusterPhaseUpgrading &&
		cluster.Status.ClusterPhase != ccetypes.ClusterPhaseUpgradeFailed {
		logger.Errorf(ctx, "cluster %s status not support download node", clusterID)
		c.errorHandlerV2(errorcode.NewClusterMasterUnavailable(), errorcode.LevelByUser, "cluster %s not running", clusterID)
		return
	}

	dataString := make([][]string, 0)
	// 获取节点数据
	if req.ExportAll || len(req.CCEInstanceIDs) > 0 {

		k8sClient, err := getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}

		// 初始化 service
		instanceService, err := instancep.NewService(ctx, c.accountID, c.config, c.clients, c.models, c.service, k8sClient)
		if err != nil {
			logger.Errorf(ctx, "NewService failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}

		instanceService = instanceService.WithResourceCache(resourceCache)

		// 查询
		infos, err := instanceService.ListInstancesByInstancesDownloadRequest(ctx, req, clusterID, c.accountID)
		if err != nil {
			logger.Errorf(ctx, "ListInstancesByInstancesDownloadRequest failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}

		dataString = convertInfosToCSV(ctx, infos)
	}

	// 生成csv文件
	filename := fmt.Sprintf(filenameFormat, cluster.Spec.ClusterName, region, time.Now().Format("2006-01-02"))
	file, err := os.Create(filename)
	if err != nil {
		logger.Errorf(ctx, "can not create file failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByAdmin, "err is %s", err.Error())
		return
	}
	filePath := filepath.Join("./", filename)
	defer os.RemoveAll(filePath)

	// 创建 CSV Writer
	writer := csv.NewWriter(file)

	// 写入表头
	err = writer.Write(instanceDownloadCSVHeader)
	if err != nil {
		logger.Errorf(ctx, "write instanceDownloadCSVHeader failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByAdmin, "err is %s", err.Error())
		return
	}

	// 写入节点数据
	if len(dataString) > 0 {
		for _, row := range dataString {
			err = writer.Write(row)
			if err != nil {
				logger.Errorf(ctx, "write row %s failed: %v", utils.ToJSON(row), err)
				c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByAdmin, "err is %s", err.Error())
				return
			}
		}
	}
	// **手动 Flush 保证数据写入**
	writer.Flush()
	if err := writer.Error(); err != nil {
		logger.Errorf(ctx, "csv writer flush failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByAdmin, "csv writer flush failed %s", err.Error())
		return
	}

	logger.Infof(ctx, "InstancesDownload success")
	c.Ctx.Output.Download(filePath, filename)
}
