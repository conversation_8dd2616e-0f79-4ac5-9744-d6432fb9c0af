package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	beegoctx "github.com/astaxie/beego/context"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	testassert "github.com/stretchr/testify/assert"
	"gotest.tools/assert"
	apiv1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sruntime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	bccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	bceerror "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/error"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	errorcodemock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin"
	pluginclients "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/securitygroup"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s"
	k8smock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/autoscaler"
	autoscalermock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/autoscaler/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/speccheck"
)

func Test_CreateInstanceGroups(t *testing.T) {
	type fields struct {
		c *InstanceGroupController
		r ccesdk.CreateInstanceGroupsRequest
	}
	patches := &gomonkey.Patches{}
	ctrl := gomock.NewController(t)
	tests := []struct {
		name    string
		fields  func() fields
		wantErr bool
	}{
		{
			name: "ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, nil)
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: false,
		},
		{
			name: "not ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(models.ErrIgReplicasMaxLessThanMin.New(context.TODO(), "test"))
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, nil)
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: true,
		},
		{
			name: "not ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("Exceeding the number of nodes"))
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, nil)
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: true,
		},
		{
			name: "not ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("test"))
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, nil)
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: true,
		},
		{
			name: "not ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, errors.New("test"))
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: true,
		},
		{
			name: "not ok",
			fields: func() fields {
				instanceGroupService := mock.NewMockInterface(ctrl)
				instanceGroupService.EXPECT().VerifyAndPadDefault(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				instanceGroupService.EXPECT().CreateInstanceGroupCR(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("test"))
				model := models.NewMockInterface(ctrl)
				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"id-1"}, nil)
				c := &InstanceGroupController{
					instanceGroupService: instanceGroupService,
					BaseController: BaseController{
						models: model,
					},
				}
				patches = gomonkey.ApplyFuncReturn((*BaseController).UpdateInstanceSpecPreCheck, nil)
				patches.ApplyFuncReturn((*BaseController).CheckMachineSpecs, nil)
				patches.ApplyFuncReturn((*InstanceGroupController).setDefaultSecurityGroupForIg, nil)
				r := ccesdk.CreateInstanceGroupsRequest{
					InstanceGroups: []*ccetypes.InstanceGroupSpec{
						{
							InstanceTemplates: []ccetypes.InstanceTemplate{
								{
									InstanceSpec: ccetypes.InstanceSpec{
										InstanceResource: ccetypes.InstanceResource{},
									},
								},
							},
						},
					},
					Cluster: &models.Cluster{},
				}
				return fields{
					c: c,
					r: r,
				}
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := tt.fields()
			var err error
			errCode, _, _ := f.c.VerifyAndPadDefault(context.TODO(), &f.r)
			if errCode == nil {
				errCode, _, _ = f.c.CreateInstanceGroupsModel(context.TODO(), f.r.InstanceGroups)
				if errCode == nil {
					err = f.c.CreateInstanceGroupsCR(context.TODO(), f.r.Cluster, f.r.InstanceGroups)
				}
			}
			assert.Equal(t, tt.wantErr, errCode != nil || err != nil)
		})
	}
}

func Test_getSshKeyName(t *testing.T) {
	ctrl := gomock.NewController(t)
	bccClient := bccmock.NewMockInterface(ctrl)
	bccClient.EXPECT().GetKeyPair(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("test"))
	stsClient := stsmock.NewMockInterface(ctrl)
	stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	c := &InstanceGroupController{
		BaseController: BaseController{
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					OpenBCCClient: bccClient,
					STSClient:     stsClient,
				},
			},
		},
	}
	_, err := c.getSSHKeyName(context.TODO(), "")
	assert.Assert(t, err != nil)

	bccClient.EXPECT().GetKeyPair(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.KeypairResult{}, nil)
	_, err = c.getSSHKeyName(context.TODO(), "")
	assert.Assert(t, err == nil)
}

func Test_getAspName(t *testing.T) {
	ctrl := gomock.NewController(t)
	bccClient := bccmock.NewMockInterface(ctrl)
	bccClient.EXPECT().GetAutoSnapshot(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("test"))
	stsClient := stsmock.NewMockInterface(ctrl)
	stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	c := &InstanceGroupController{
		BaseController: BaseController{
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					OpenBCCClient: bccClient,
					STSClient:     stsClient,
				},
			},
		},
	}
	_, err := c.getAspName(context.TODO(), "")
	assert.Assert(t, err != nil)

	bccClient.EXPECT().GetAutoSnapshot(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.GetASPDetailResult{}, nil)
	_, err = c.getAspName(context.TODO(), "")
	assert.Assert(t, err == nil)
}

func Test_checkCAInit(t *testing.T) {
	c := &InstanceGroupController{
		BaseController: BaseController{
			config:  &configuration.Config{},
			clients: &clients.Clients{},
		},
	}
	getClientSetErr := errors.New("get clientset error")
	getPluginClientErr := errors.New("get plugin client error")
	newClientErr := errors.New("new client error")
	getCaErr := errors.New("get ca error")
	var ca *ccev2.Autoscaler
	ctrl := gomock.NewController(t)
	client := autoscalermock.NewMockInterface(ctrl)
	gomonkey.ApplyFunc(getClientSet, func(ctx context.Context, clusterID string, model models.Interface) (kubernetes.Interface, error) {
		return nil, getClientSetErr
	})
	gomonkey.ApplyFunc(getPluginClient, func(ctx context.Context, clusterID string, model models.Interface, metaclient k8s.Interface, pluginConfig *pluginclients.Config) (plugin.Interface, error) {
		return nil, getPluginClientErr
	})
	gomonkey.ApplyFunc(autoscaler.NewClient, func(ctx context.Context, accountID, userID string,
		models models.Interface, clients *clients.Clients,
		k8sClient kubernetes.Interface, pluginClient plugin.Interface,
		config *configuration.Config, clientSet *clientset.ClientSet) (autoscaler.Interface, error) {
		return client, newClientErr
	})
	client.EXPECT().GetAutoscaler(gomock.Any(), gomock.Any()).Return(ca, getCaErr)
	ok, err := c.checkCAInit("")
	assert.Equal(t, err, getClientSetErr)
	assert.Equal(t, ok, false)

	getClientSetErr = nil
	ok, err = c.checkCAInit("")
	assert.Equal(t, err, getPluginClientErr)
	assert.Equal(t, ok, false)

	getPluginClientErr = nil
	ok, err = c.checkCAInit("")
	assert.Equal(t, err, newClientErr)
	assert.Equal(t, ok, false)

	newClientErr = nil
	ok, err = c.checkCAInit("")
	assert.Equal(t, err, getCaErr)
	assert.Equal(t, ok, false)

	getCaErr = nil
	client.EXPECT().GetAutoscaler(gomock.Any(), gomock.Any()).Return(nil, nil)
	ok, err = c.checkCAInit("")
	assert.Equal(t, err != nil, false)
	assert.Equal(t, ok, false)

	ca = &ccev2.Autoscaler{}
	client.EXPECT().GetAutoscaler(gomock.Any(), gomock.Any()).Return(ca, nil)
	ok, err = c.checkCAInit("")
	assert.Equal(t, err != nil, false)
	assert.Equal(t, ok, true)
}

func Test_existConflictWithInstanceGroupScaling_2(t *testing.T) {
	clusterID := "cid"
	accountID := "aid"
	instanceGroupID := "gid"

	tests := []struct {
		name       string
		controller *InstanceGroupController
		instances  []string
		nodes      []string
		wantErr    bool
	}{
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   false,
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, errors.New("test"))
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{},
			nodes:     []string{},
			wantErr:   false,
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, errors.New("test"))
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i4",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n4",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := gomonkey.ApplyFuncReturn(existConflictWithInstanceGroupScaling, nil, errorcode.LevelByAdmin, "")
			defer p.Reset()
			code, _, _ := tt.controller.existConflictWithInstanceGroupScaling(context.TODO(),
				clusterID, accountID, instanceGroupID, tt.instances, tt.nodes)
			assert.Equal(t, code != nil, tt.wantErr)
		})
	}
}

func Test_existConflictWithInstanceGroupScaling_3(t *testing.T) {
	accountID := "aid"
	instanceGroupID := "gid"

	tests := []struct {
		name       string
		controller *InstanceGroupController
		instances  []string
		nodes      []string
		wantErr    bool
		clusterID  string
	}{
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{},
			nodes:     []string{},
			wantErr:   false,
			clusterID: "cid",
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				// mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: nil,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
			clusterID: "cid",
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
			clusterID: "",
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockMetaClient.EXPECT().ListTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.TaskList{}, errors.New("test"))
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   true,
			clusterID: "cid",
		},
		{
			controller: func() *InstanceGroupController {
				ctrl := gomock.NewController(t)
				mockMetaClient := metamock.NewMockInterface(ctrl)
				mockModelClient := models.NewMockInterface(ctrl)

				mockModelClient.EXPECT().GetCluster(
					gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				mockMetaClient.EXPECT().ListTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.TaskList{
					Items: []ccev1.Task{},
				}, nil)
				mockModelClient.EXPECT().GetInstancesByInstanceGroupID(
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceList{
					Items: []*models.Instance{
						{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "i3",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP:    "********",
									Hostname: "n3",
								},
							},
						},
					},
				}, nil)
				return &InstanceGroupController{
					BaseController: BaseController{
						clientSet: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMetaClient,
								Model:      mockModelClient,
							},
						},
					},
				}
			}(),
			instances: []string{"i1", "i2"},
			nodes:     []string{"n3"},
			wantErr:   false,
			clusterID: "cid",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code, _, _ := tt.controller.existConflictWithInstanceGroupScaling(context.TODO(),
				tt.clusterID, accountID, instanceGroupID, tt.instances, tt.nodes)
			assert.Equal(t, code != nil, tt.wantErr)
		})
	}
}

func Test_checkScaleUpParams(t *testing.T) {
	type args struct {
		upToReplica int
		upReplica   int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "all zero",
			args: args{
				upToReplica: 0,
				upReplica:   0,
			},
			wantErr: true,
		},
		{
			name: "all negative",
			args: args{
				upToReplica: -1,
				upReplica:   -1,
			},
			wantErr: true,
		},
		{
			name: "one negative",
			args: args{
				upToReplica: 0,
				upReplica:   -1,
			},
			wantErr: true,
		},
		{
			name: "all not zero",
			args: args{
				upToReplica: 4,
				upReplica:   4,
			},
			wantErr: true,
		},
		{
			name: "normal up to N",
			args: args{
				upToReplica: 4,
				upReplica:   0,
			},
			wantErr: false,
		},
		{
			name: "normal up N",
			args: args{
				upToReplica: 0,
				upReplica:   4,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkScaleUpParams(tt.args.upToReplica, tt.args.upReplica); (err != nil) != tt.wantErr {
				t.Errorf("checkScaleUpParams() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_CreateInstanceGroup(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	ctl := gomock.NewController(t)

	model := models.NewMockInterface(ctl)
	tests := []struct {
		name       string
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		specCheck  speccheck.SpecInterface
		errhandler errorcode.Interface
		clients    *clients.Clients
	}{
		{
			name: "test create instancegroup",
			ctx:  ctx,
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
								DeployCustomConfig: ccetypes.DeployCustomConfig{},
								RuntimeType:        ccetypes.RuntimeTypeContainerd,
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeContainerd,
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeDocker,
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(&ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: make(map[string]string),
					},
				}, nil)
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{}, []string{}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
		},
		{
			name: "test notsupported spec",
			ctx:  ctx,
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(&ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: make(map[string]string),
					},
				}, nil)
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
						Request: &http.Request{
							Method: http.MethodPost,
							URL: &url.URL{
								Path: "",
							},
						},
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{"bcc.g3.c2m8"}, []string{}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
			errhandler: func() errorcode.Interface {
				e := errorcodemock.NewMockInterface(ctl)
				e.EXPECT().ErrorHandler(ctx, errorcode.NewInvalidMachineSpec(), errorcode.LevelByUser, "该实例规格 CCE 暂不支持").AnyTimes()
				return e
			}(),
		},
		{
			name: "test unadapted spec",
			ctx:  ctx,
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(&ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: make(map[string]string),
					},
				}, nil)
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
						Request: &http.Request{
							Method: http.MethodPost,
							URL: &url.URL{
								Path: "",
							},
						},
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{}, []string{"bcc.g3.c2m8"}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
			errhandler: func() errorcode.Interface {
				e := errorcodemock.NewMockInterface(ctl)
				e.EXPECT().ErrorHandler(ctx, errorcode.NewInvalidMachineSpec(), errorcode.LevelByUser, "该实例规格 CCE 暂未适配，若有需要请提工单联系").AnyTimes()
				return e
			}(),
		},
		{
			name: "get cluster failed",
			ctx:  ctx,
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
						Request: &http.Request{
							Method: http.MethodPost,
							URL: &url.URL{
								Path: "",
							},
						},
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(nil, errors.New("get cluster failed"))
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),

			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				// mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				// specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{}, []string{"bcc.g3.c2m8"}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
			errhandler: func() errorcode.Interface {
				e := errorcodemock.NewMockInterface(ctl)
				e.EXPECT().ErrorHandler(ctx, errorcode.NewInternalServerError(), errorcode.LevelByAdmin,
					"GetCluster %s error", "cce-cluster").AnyTimes()
				return e
			}(),
		},

		{
			name: "create instance group invalid data root dir",
			ctx:  ctx,
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
								DeployCustomConfig: ccetypes.DeployCustomConfig{
									KubeletRootDir: "12345",
								},
								RuntimeType: ccetypes.RuntimeTypeContainerd,
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeContainerd,
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeDocker,
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			errhandler: errorcode.NewMockErrorHandler(),
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(&ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: make(map[string]string),
					},
				}, nil)
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{}, []string{}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
		},

		{
			name: "invalid kubeReserved",
			ctx:  ctx,
			controller: func() beego.Controller {
				body := ccesdk.CreateInstanceGroupRequest{
					InstanceGroupSpec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType:  "BCC",
								InstanceType: "N3",
								InstanceName: "",
								// InstanceOS: ccetypes.InstanceOS{
								// 	OSArch: "os-arch",
								// },
								DeployCustomConfig: ccetypes.DeployCustomConfig{
									KubeletRootDir: "/cce",
									KubeReserved: map[string]string{
										"cpu": "1Mi",
									},
								},
								RuntimeType: ccetypes.RuntimeTypeContainerd,
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N3",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g3",
										MachineSpec:  "bcc.g3.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-uhj41zevmcty",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeContainerd,
								},
							},
							{
								InstanceSpec: ccetypes.InstanceSpec{
									MachineType:  "BCC",
									InstanceType: "N5",
									InstanceName: "",
									ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
									InstanceOS: ccetypes.InstanceOS{
										ImageType: "System",
									},
									InstanceResource: ccetypes.InstanceResource{
										CPU:          2,
										MEM:          8,
										RootDiskType: "enhanced_ssd_pl1",
										RootDiskSize: 100,
										GPUType:      "",
										GPUCount:     1,
										SpecID:       "g4",
										MachineSpec:  "bcc.g4.c2m8",
									},
									VPCConfig: ccetypes.VPCConfig{

										VPCSubnetID:       "sbn-jsva4jyn8287",
										SecurityGroupType: "normal",
										SecurityGroup:     ccetypes.SecurityGroup{},
									},
									DeployCustomConfig: ccetypes.DeployCustomConfig{},
									RuntimeType:        ccetypes.RuntimeTypeDocker,
								},
							},
						},
						ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
							Enabled: false,
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								Name: "CCE-Worker默认安全组",
								Type: ccetypes.SecurityGroupTypeNormal,
								ID:   "g-xxxx",
							},
						},
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}
				return beego.Controller{
					Data: make(map[any]any),
					Ctx: &beegoctx.Context{
						Input: func() *beegoctx.BeegoInput {
							input := &beegoctx.BeegoInput{}
							input.Context = beegoctx.NewContext()
							input.SetParam(":clusterID", "cce-cluster")
							input.SetData("RequestID", "xxxx")
							input.RequestBody = requestBody

							return input
						}(),

						Output: func() *beegoctx.BeegoOutput {
							output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

							// httptest.NewRecorder()
							output.Context.ResponseWriter = &beegoctx.Response{
								ResponseWriter: httptest.NewRecorder(),
							}
							output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
							// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
							// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

							output.Context.Request = &http.Request{}
							output.Context.Request.Response = &http.Response{}

							output.Context.Request.Response.Header = make(http.Header)
							output.Context.Request.Response.Header.Add("Content-Type", "application/json")
							return output
						}(),
					},
				}
			}(),
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					Model: model,
				},
			},
			service: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctl)
				mockService.EXPECT().Create(gomock.Any(), gomock.Any()).Return("ig-xxx", nil)
				return mockService
			}(),
			errhandler: errorcode.NewMockErrorHandler(),
			clients: func() *clients.Clients {
				k8sClient := k8smock.NewMockInterface(ctl)
				k8sClient.EXPECT().GetCluster(ctx, "cce-cluster").Return(&ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: make(map[string]string),
					},
				}, nil)
				return &clients.Clients{
					K8SClient: k8sClient,
				}
			}(),
			specCheck: func() speccheck.SpecInterface {
				specService := speccheck.NewMockSpecInterface(ctl)
				specService.EXPECT().CheckSpecs("bcc.g3.c2m8", "bcc.g4.c2m8").Return([]string{}, []string{}, nil).AnyTimes()
				return specService
			}(),
			accountID:  "account-id",
			statusCode: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller:  tt.controller,
					ctx:         tt.ctx,
					clientSet:   tt.clientSet,
					accountID:   tt.accountID,
					specService: tt.specCheck,
					errHandler:  tt.errhandler,
					cancel:      func() {},

					clients: tt.clients,
				},

				instanceGroupService: tt.service,
			}
			patch := gomonkey.ApplyFuncReturn((*BaseController).errorHandlerV2)
			defer patch.Reset()
			c.CreateInstanceGroup()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func Test_Scaleup(t *testing.T) {
	t.Run("", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		c := InstanceGroupController{
			BaseController: BaseController{
				Controller: func() beego.Controller {
					return beego.Controller{
						Data: make(map[any]any),
						Ctx: &beegoctx.Context{
							Input: func() *beegoctx.BeegoInput {
								input := &beegoctx.BeegoInput{}
								input.Context = beegoctx.NewContext()
								input.SetParam(":clusterID", "cce-cluster")
								input.SetData("RequestID", "xxxx")
								input.SetParam(":instanceGroupID", "ig-xxx")
								input.SetParam("upToReplicas", "2")
								input.SetParam("upReplicas", "0")

								return input
							}(),

							Output: func() *beegoctx.BeegoOutput {
								output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

								// httptest.NewRecorder()
								output.Context.ResponseWriter = &beegoctx.Response{
									ResponseWriter: httptest.NewRecorder(),
								}
								output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
								// output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
								// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

								output.Context.Request = &http.Request{}
								output.Context.Request.Response = &http.Response{}

								output.Context.Request.Response.Header = make(http.Header)
								output.Context.Request.Response.Header.Add("Content-Type", "application/json")
								return output
							}(),

							Request: &http.Request{},
						},
					}
				}(),
				ctx:       context.TODO(),
				accountID: "1",
				errHandler: func() errorcode.Interface {
					e := errorcodemock.NewMockInterface(ctrl)
					e.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
					return e
				}(),
				cancel: func() {},
			},

			instanceGroupService: func() instancegroup.Interface {
				mockService := mock.NewMockInterface(ctrl)
				mockService.EXPECT().ScaleUp(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", models.ErrHPASNotImplemented.New(context.TODO(), ""))
				return mockService
			}(),
		}
		c.ScaleUpInstanceGroup()
	})
}

func Test_CreateInstanceGroupV2(t *testing.T) {
	var (
		ast         = testassert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
		}
	)

	var clusterID string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		return clusterID
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	var cluster = &ccev1.Cluster{}
	var getClusterErr = errors.New("get cluster failed")
	patch3 := gomonkey.ApplyFunc((*k8s.Client).GetCluster, func(_ *k8s.Client, ctx context.Context, clusterID string) (*ccev1.Cluster, error) {
		return cluster, getClusterErr
	})
	defer patch3.Reset()

	var specPreCheckErr = errors.New("spec check failed")
	patch4 := gomonkey.ApplyFunc((*BaseController).UpdateInstanceSpecPreCheck, func(_ *BaseController, instanceSpec ccetypes.InstanceSpec) error {
		return specPreCheckErr
	})
	defer patch4.Reset()

	var checkSpecErr = errors.New("check spec failed")
	checkSpecPatch := gomonkey.ApplyFunc((*BaseController).CheckMachineSpecs, func(_ *BaseController, ctx context.Context, specs []string) error {
		return checkSpecErr
	})
	defer checkSpecPatch.Reset()

	var setSgErr = errors.New("set security group failed")
	setSgPatch := gomonkey.ApplyFunc((*InstanceGroupController).setDefaultSecurityGroupForIg, func(_ *InstanceGroupController, ctx context.Context, igSpec *ccetypes.InstanceGroupSpec) error {
		return setSgErr
	})
	defer setSgPatch.Reset()

	// 没传clusterID
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// body unmarshal 失败
	clusterID = "123"
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewMalformedJSON())

	// 传入InstanceTemplate，预检查失败
	req := ccesdk.CreateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					InstanceName: "node1",
				},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	ast.True(strings.Contains(errHandler.ErrorMsg, specPreCheckErr.Error()))

	// 传入InstanceTemplates，预检查失败
	req = ccesdk.CreateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName: "node1",
					MachineType:  ccetypes.MachineTypeBCC,
				}},
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName: "node2",
					MachineType:  ccetypes.MachineTypeEBC,
				}},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	ast.True(strings.Contains(errHandler.ErrorMsg, specPreCheckErr.Error()))

	// 查询cluster失败
	specPreCheckErr = nil
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())

	getClusterErr = meta.ErrClusterNotExist.New(ctx, "mock")
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewNoSuchObject())

	// 检查机型不支持
	getClusterErr = nil
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidMachineSpec())
	ast.True(strings.Contains(errHandler.ErrorMsg, checkSpecErr.Error()))

	// 设置默认安全组失败
	checkSpecErr = nil
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)
	ast.True(strings.Contains(errHandler.ErrorMsg, setSgErr.Error()))

	setSgErr = nil
	cluster = &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			K8SVersion: ccetypes.K8S_1_24_4,
		},
	}
	req = ccesdk.CreateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName:   "node1",
					MachineType:    ccetypes.MachineTypeBCC,
					RuntimeType:    ccetypes.RuntimeTypeContainerd,
					RuntimeVersion: ccetypes.ContainerdVersion_1_5_4,
				}},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	cluster = &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			K8SVersion: ccetypes.K8S_1_24_4,
		},
	}
	req = ccesdk.CreateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName:   "node1",
					MachineType:    ccetypes.MachineTypeBCC,
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: ccetypes.DockerVersion19,
				}},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.CreateInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// TODO 后续逻辑再补充单测
}

var (
	scaleDownDisabledFalse = false
)

func Test_generateInstanceSpec(t *testing.T) {
	bbcOptions := &ccetypes.BBCOption{
		Flavor:      "xx",
		ReserveData: false,
		RaidID:      "xx",
		DiskInfo:    "xx",
		SysDiskSize: 0,
	}
	eipOption := &ccetypes.EIPOption{
		EIPName:         "xx",
		EIPChargingType: "xx",
		EIPBandwidth:    0,
	}
	tests := []struct {
		name   string
		input  ccetypes.InstanceTemplate
		output ccesdk.InstanceTemplate
	}{
		{
			name: "normal spec",
			input: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					Handler:           "default",
					CCEInstanceID:     "i-xxx",
					InstanceName:      "i-name",
					RuntimeType:       ccetypes.RuntimeTypeDocker,
					RuntimeVersion:    "1.20",
					ClusterID:         "clu-xxx",
					ClusterRole:       "node",
					UserID:            "user",
					AccountID:         "account",
					InstanceGroupID:   "ig-xxx",
					InstanceGroupName: "ig-name",
					BBCOption:         *bbcOptions,
					EIPOption:         *eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "System",
						SpecialVersion: "internal_cloud",
					},
					SSHKeyID:       "1",
					AutoSnapshotID: "1",
				},
			},
			output: ccesdk.InstanceTemplate{
				InstanceSpec: ccesdk.InstanceSpec{
					CCEInstanceID:     "i-xxx",
					InstanceName:      "i-name",
					RuntimeType:       ccetypes.RuntimeTypeDocker,
					RuntimeVersion:    "1.20",
					ClusterID:         "clu-xxx",
					ClusterRole:       "node",
					UserID:            "user",
					InstanceGroupID:   "ig-xxx",
					InstanceGroupName: "ig-name",
					BBCOption:         bbcOptions,
					EIPOption:         eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "internal",
						SpecialVersion: "internal_cloud",
					},
					SSHKeyID:          "1",
					AutoSnapshotID:    "1",
					ScaleDownDisabled: &scaleDownDisabledFalse,
				}},
		},

		{
			name: "adapt kubeReserved success",
			input: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					Handler:         "default",
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					AccountID:       "account",
					InstanceGroupID: "ig-xxx",
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeReserved: map[string]string{
							"cpu":    "1",
							"memory": "1Gi",
						},
						SystemReserved: map[string]string{
							"cpu":    "50m",
							"memory": "487Ki",
						},
					},
					InstanceGroupName: "ig-name",
					BBCOption:         *bbcOptions,
					EIPOption:         *eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "System",
						SpecialVersion: "internal_cloud",
					},
				},
			},
			output: ccesdk.InstanceTemplate{
				InstanceSpec: ccesdk.InstanceSpec{
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					InstanceGroupID: "ig-xxx",
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeReserved: map[string]string{
							"cpu":    "1000m",
							"memory": "1024Mi",
						},
						SystemReserved: map[string]string{
							"cpu":    "50m",
							"memory": "0Mi",
						},
					},
					InstanceGroupName: "ig-name",
					BBCOption:         bbcOptions,
					EIPOption:         eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "internal",
						SpecialVersion: "internal_cloud",
					},
					ScaleDownDisabled: &scaleDownDisabledFalse,
				}},
		},

		{
			name: "adapt kubeReserved invalid cpu，not change value",
			input: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					Handler:         "default",
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					AccountID:       "account",
					InstanceGroupID: "ig-xxx",
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeReserved: map[string]string{
							"cpu": "1Mi",
						},
					},
					InstanceGroupName: "ig-name",
					BBCOption:         *bbcOptions,
					EIPOption:         *eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "System",
						SpecialVersion: "internal_cloud",
					},
				},
			},
			output: ccesdk.InstanceTemplate{
				InstanceSpec: ccesdk.InstanceSpec{
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					InstanceGroupID: "ig-xxx",
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeReserved: map[string]string{
							"cpu": "1Mi",
						},
					},
					InstanceGroupName: "ig-name",
					BBCOption:         bbcOptions,
					EIPOption:         eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "internal",
						SpecialVersion: "internal_cloud",
					},
					ScaleDownDisabled: &scaleDownDisabledFalse,
				}},
		},

		{
			name: "adapt systemReserved invalid memory，not change value",
			input: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					Handler:         "default",
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					AccountID:       "account",
					InstanceGroupID: "ig-xxx",
					CheckGPUDriver:  true,
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						SystemReserved: map[string]string{
							"memory": "1024m",
						},
					},
					InstanceGroupName: "ig-name",
					BBCOption:         *bbcOptions,
					EIPOption:         *eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "System",
						SpecialVersion: "internal_cloud",
					},
				},
			},
			output: ccesdk.InstanceTemplate{
				InstanceSpec: ccesdk.InstanceSpec{
					CCEInstanceID:   "i-xxx",
					InstanceName:    "i-name",
					RuntimeType:     ccetypes.RuntimeTypeContainerd,
					RuntimeVersion:  "1.20",
					ClusterID:       "clu-xxx",
					ClusterRole:     "node",
					UserID:          "user",
					InstanceGroupID: "ig-xxx",
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						SystemReserved: map[string]string{
							"memory": "1024m",
						},
					},
					InstanceGroupName: "ig-name",
					CheckGPUDriver:    true,
					BBCOption:         bbcOptions,
					EIPOption:         eipOption,
					InstanceOS: ccetypes.InstanceOS{
						ImageType:      "internal",
						SpecialVersion: "internal_cloud",
					},
					ScaleDownDisabled: &scaleDownDisabledFalse,
				}},
		},
	}

	ctrl := gomock.NewController(t)

	bccClient := bccmock.NewMockInterface(ctrl)
	bccClient.EXPECT().GetDeploySet(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.DeploySetResult{}, nil)
	bccClient.EXPECT().GetKeyPair(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.KeypairResult{}, nil)
	bccClient.EXPECT().GetAutoSnapshot(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.GetASPDetailResult{}, nil)
	stsClient := stsmock.NewMockInterface(ctrl)
	stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil)
	client := &clientset.ClientSet{
		Clients: &clientset.Clients{
			OpenBCCClient: bccClient,
			STSClient:     stsClient,
		},
	}
	c := InstanceGroupController{
		BaseController: BaseController{
			clientSet: client,
		},
	}

	patches := gomonkey.ApplyFuncReturn((*InstanceGroupController).getSSHKeyName, "", errors.New("test"))
	patches.ApplyFuncReturn((*InstanceGroupController).getAspName, "", errors.New("test"))
	defer patches.Reset()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			out := c.generateInstanceSpec(context.TODO(), tt.input)
			assert.DeepEqual(t, out, tt.output)
		})
	}
}

func Test_instanceGroupModelToSDK(t *testing.T) {
	bbcOptions := &ccetypes.BBCOption{
		Flavor:      "xx",
		ReserveData: false,
		RaidID:      "xx",
		DiskInfo:    "xx",
		SysDiskSize: 0,
	}
	eipOption := &ccetypes.EIPOption{
		EIPName:         "xx",
		EIPChargingType: "xx",
		EIPBandwidth:    0,
	}
	tests := []struct {
		name   string
		input  *models.InstanceGroup
		output *ccesdk.InstanceGroup
	}{
		{
			name: "empty instancetemplates but has instancetemplate",
			input: &models.InstanceGroup{
				Status: &ccetypes.InstanceGroupStatus{
					ActualReplicas:   1,
					ReadyReplicas:    1,
					ScalingReplicas:  1,
					DeletingReplicas: 1,
					OtherReplicas:    1,
				},
				Spec: &ccetypes.InstanceGroupSpec{
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							Handler:           "default",
							CCEInstanceID:     "i-xxx",
							InstanceName:      "i-name",
							RuntimeType:       ccetypes.RuntimeTypeDocker,
							RuntimeVersion:    "1.20",
							ClusterID:         "clu-xxx",
							ClusterRole:       "node",
							UserID:            "user",
							AccountID:         "account",
							InstanceGroupID:   "ig-xxx",
							InstanceGroupName: "ig-name",
							BBCOption:         *bbcOptions,
							EIPOption:         *eipOption,
						},
					},
				},
			},

			output: &ccesdk.InstanceGroup{
				Spec: &ccesdk.InstanceGroupSpec{
					InstanceTemplate: ccesdk.InstanceTemplate{
						InstanceSpec: ccesdk.InstanceSpec{
							VPCConfig: ccesdk.VPCConfig{
								SecurityGroupType: "normal",
							},
							CCEInstanceID:     "i-xxx",
							InstanceName:      "i-name",
							RuntimeType:       ccetypes.RuntimeTypeDocker,
							RuntimeVersion:    "1.20",
							ClusterID:         "clu-xxx",
							ClusterRole:       "node",
							UserID:            "user",
							InstanceGroupID:   "ig-xxx",
							InstanceGroupName: "ig-name",
							BBCOption:         bbcOptions,
							EIPOption:         eipOption,
						},
					},
				},
				Status: &ccesdk.InstanceGroupStatus{
					ActualReplicas:   1,
					ReadyReplicas:    1,
					ScalingReplicas:  1,
					DeletingReplicas: 1,
					OtherReplicas:    1,
				},
			},
		},
		{
			name: "normal spec",
			input: &models.InstanceGroup{
				Status: &ccetypes.InstanceGroupStatus{
					ActualReplicas:   1,
					ReadyReplicas:    1,
					ScalingReplicas:  1,
					DeletingReplicas: 1,
					OtherReplicas:    1,
				},
				Spec: &ccetypes.InstanceGroupSpec{
					InstanceTemplates: []ccetypes.InstanceTemplate{
						{
							InstanceSpec: ccetypes.InstanceSpec{
								Handler:           "default",
								CCEInstanceID:     "i-xxx",
								InstanceName:      "i-name",
								RuntimeType:       ccetypes.RuntimeTypeDocker,
								RuntimeVersion:    "1.20",
								ClusterID:         "clu-xxx",
								ClusterRole:       "node",
								UserID:            "user",
								AccountID:         "account",
								InstanceGroupID:   "ig-xxx",
								InstanceGroupName: "ig-name",
								BBCOption:         *bbcOptions,
								EIPOption:         *eipOption,
							},
						},
						{
							InstanceSpec: ccetypes.InstanceSpec{
								Handler:           "default",
								CCEInstanceID:     "i-xxx",
								InstanceName:      "i-name",
								RuntimeType:       ccetypes.RuntimeTypeDocker,
								RuntimeVersion:    "1.20",
								ClusterID:         "clu-xxx",
								ClusterRole:       "node",
								UserID:            "user",
								AccountID:         "account",
								InstanceGroupID:   "ig-xxx",
								InstanceGroupName: "ig-name",
								BBCOption:         *bbcOptions,
								EIPOption:         *eipOption,
							},
						},
					},
				},
			},

			output: &ccesdk.InstanceGroup{
				Spec: &ccesdk.InstanceGroupSpec{
					InstanceTemplates: []ccesdk.InstanceTemplate{
						{
							InstanceSpec: ccesdk.InstanceSpec{
								VPCConfig: ccesdk.VPCConfig{
									SecurityGroupType: "normal",
								},
								CCEInstanceID:     "i-xxx",
								InstanceName:      "i-name",
								RuntimeType:       ccetypes.RuntimeTypeDocker,
								RuntimeVersion:    "1.20",
								ClusterID:         "clu-xxx",
								ClusterRole:       "node",
								UserID:            "user",
								InstanceGroupID:   "ig-xxx",
								InstanceGroupName: "ig-name",
								BBCOption:         bbcOptions,
								EIPOption:         eipOption,
								ScaleDownDisabled: &scaleDownDisabledFalse,
							},
						},
						{
							InstanceSpec: ccesdk.InstanceSpec{
								VPCConfig: ccesdk.VPCConfig{
									SecurityGroupType: "normal",
								},
								CCEInstanceID:     "i-xxx",
								InstanceName:      "i-name",
								RuntimeType:       ccetypes.RuntimeTypeDocker,
								RuntimeVersion:    "1.20",
								ClusterID:         "clu-xxx",
								ClusterRole:       "node",
								UserID:            "user",
								InstanceGroupID:   "ig-xxx",
								InstanceGroupName: "ig-name",
								BBCOption:         bbcOptions,
								EIPOption:         eipOption,
								ScaleDownDisabled: &scaleDownDisabledFalse,
							},
						},
					},
				},
				Status: &ccesdk.InstanceGroupStatus{
					ActualReplicas:   1,
					ReadyReplicas:    1,
					ScalingReplicas:  1,
					DeletingReplicas: 1,
					OtherReplicas:    1,
				},
			},
		},
	}

	ctrl := gomock.NewController(t)

	bccClient := bccmock.NewMockInterface(ctrl)
	bccClient.EXPECT().GetDeploySet(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.DeploySetResult{}, nil)
	bccClient.EXPECT().GetKeyPair(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.KeypairResult{}, nil)
	bccClient.EXPECT().GetAutoSnapshot(gomock.Any(), gomock.Any(), gomock.Any()).Return(&bccapi.GetASPDetailResult{}, nil)
	stsClient := stsmock.NewMockInterface(ctrl)
	stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil)
	client := &clientset.ClientSet{
		Clients: &clientset.Clients{
			OpenBCCClient: bccClient,
			STSClient:     stsClient,
		},
	}
	c := InstanceGroupController{
		BaseController: BaseController{
			clientSet: client,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			out := c.instanceGroupModelToSDK(context.TODO(), tt.input, nil)
			assert.DeepEqual(t, out.Spec.InstanceTemplates, tt.output.Spec.InstanceTemplates)
			assert.DeepEqual(t, out.Status, tt.output.Status)
		})
	}
}

func Test_GetInstanceGroup(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()

	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.InstancePage
		wantErr bool
	}{
		{
			name: "test get instancegroup",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "clusterID",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
							Spec: &ccetypes.InstanceGroupSpec{
								ClusterID:          "clusterID",
								CCEInstanceGroupID: "ig-aa",
							},
							Status: &ccetypes.InstanceGroupStatus{
								ActualReplicas:   10,
								ReadyReplicas:    5,
								ScalingReplicas:  2,
								DeletingReplicas: 1,
								FailedReplicas:   1,
								OtherReplicas:    1,
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
		{
			name: "test get instancegroup with cluster is nil",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				// errHandler := errorcodeMock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(nil, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)
				// gomock.InOrder(
				//	errHandler.EXPECT().ErrorHandler(ctx, gomock.Any(), gomock.Any(), gomock.Any(),gomock.Any()).Return(),
				//	errHandler.EXPECT().CustomAbortJson( gomock.Any(), gomock.Any()).Return(),
				// )

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:       ctx,
					accountID: "account-id",
					// errHandler: errHandler,
					statusCode: 500,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "GET",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
							Spec: &ccetypes.InstanceGroupSpec{
								ClusterID:          "clusterID",
								CCEInstanceGroupID: "ig-aa",
							},
							Status: &ccetypes.InstanceGroupStatus{
								ActualReplicas:   10,
								ReadyReplicas:    5,
								ScalingReplicas:  2,
								DeletingReplicas: 1,
								FailedReplicas:   1,
								OtherReplicas:    1,
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
		{
			name: "test get instancegroup with cluster is err",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				// errHandler := errorcodeMock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(nil, errors.New("get cluster err")),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:       ctx,
					accountID: "account-id",
					// errHandler: errHandler,
					statusCode: 500,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "GET",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
							Spec: &ccetypes.InstanceGroupSpec{
								ClusterID:          "clusterID",
								CCEInstanceGroupID: "ig-aa",
							},
							Status: &ccetypes.InstanceGroupStatus{
								ActualReplicas:   10,
								ReadyReplicas:    5,
								ScalingReplicas:  2,
								DeletingReplicas: 1,
								FailedReplicas:   1,
								OtherReplicas:    1,
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		}, {
			name: "test get instancegroup with cluster is err,code is 421",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				// errHandler := errorcodeMock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(nil, errors.New("get cluster err")),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:       ctx,
					accountID: "account-id",
					// errHandler: errHandler,
					statusCode: 500,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "GET",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
							Spec: &ccetypes.InstanceGroupSpec{
								ClusterID:          "clusterID",
								CCEInstanceGroupID: "ig-aa",
							},
							Status: &ccetypes.InstanceGroupStatus{
								ActualReplicas:   10,
								ReadyReplicas:    5,
								ScalingReplicas:  2,
								DeletingReplicas: 1,
								FailedReplicas:   1,
								OtherReplicas:    1,
							},
						}, errors.New("There are too many connections. The host is {host}\", Error Code: \"RateLimit\", Status Code: 421, Request Id: \"430a758c-057e-4deb-8171-dac0aa91c44c"))
						return mockService
					}(),
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.GetInstanceGroup()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func Test_ListInstanceGroup(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.InstancePage
		wantErr bool
	}{
		{
			name: "test list instancegroup",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "clusterID",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									input.SetParam("orderBy", "instanceGroupID")
									input.SetParam("order", "ASC")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						// opt := instancegroup.ListOptions{
						//	AccountID: "account-id",
						//	ClusterID: "cce-cluster",
						//	Role:      ccetypes.ClusterRoleNode,
						//	PageNo:    1,
						//	PageSize:  10,
						// }
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().List(gomock.Any(), gomock.Any()).Return(&models.InstanceGroupList{
							TotalCount: 1,
							Items: []*models.InstanceGroup{
								{
									Spec: &ccetypes.InstanceGroupSpec{
										ClusterID:          "clusterID",
										CCEInstanceGroupID: "ig-aa",
									},
									Status: &ccetypes.InstanceGroupStatus{
										ActualReplicas:   10,
										ReadyReplicas:    5,
										ScalingReplicas:  2,
										DeletingReplicas: 1,
										FailedReplicas:   1,
										OtherReplicas:    1,
									},
								},
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
		{
			name: "test list instancegroup with cluster is nil",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				// errHandler := errorcodeMock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(nil, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)
				// gomock.InOrder(
				//	errHandler.EXPECT().ErrorHandler(ctx, gomock.Any(), gomock.Any(), gomock.Any(),gomock.Any()).Return(),
				//	errHandler.EXPECT().CustomAbortJson( gomock.Any(), gomock.Any()).Return(),
				// )

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:       ctx,
					accountID: "account-id",
					// errHandler: errHandler,
					statusCode: 500,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "GET",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().List(gomock.Any(), gomock.Any()).Return(&models.InstanceGroupList{
							TotalCount: 1,
							Items: []*models.InstanceGroup{
								{
									Spec: &ccetypes.InstanceGroupSpec{
										ClusterID:          "clusterID",
										CCEInstanceGroupID: "ig-aa",
									},
									Status: &ccetypes.InstanceGroupStatus{
										ActualReplicas:   10,
										ReadyReplicas:    5,
										ScalingReplicas:  2,
										DeletingReplicas: 1,
										FailedReplicas:   1,
										OtherReplicas:    1,
									},
								},
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
		{
			name: "test list instancegroup with cluster is err",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				// errHandler := errorcodeMock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(nil, errors.New("get cluster err")),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:       ctx,
					accountID: "account-id",
					// errHandler: errHandler,
					statusCode: 500,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "GET",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									// httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().List(gomock.Any(), gomock.Any()).Return(&models.InstanceGroupList{
							TotalCount: 1,
							Items: []*models.InstanceGroup{
								{
									Spec: &ccetypes.InstanceGroupSpec{
										ClusterID:          "clusterID",
										CCEInstanceGroupID: "ig-aa",
									},
									Status: &ccetypes.InstanceGroupStatus{
										ActualReplicas:   10,
										ReadyReplicas:    5,
										ScalingReplicas:  2,
										DeletingReplicas: 1,
										FailedReplicas:   1,
										OtherReplicas:    1,
									},
								},
							},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.ListInstanceGroup()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func TestUpgradeInstanceGroup(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.InstancePage
		wantErr bool
	}{
		{
			name: "test UpgradeInstanceGroup",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "clusterID",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")

									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().GetUpgradeComponentVersions(gomock.Any(), gomock.Any(), gomock.Any(),
							gomock.Any()).Return(&instancegroup.UpgradeComponents{
							Kubelet: instancegroup.UpgradeVersionList{},
						}, nil)
						return mockService
					}(),
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.UpgradeInstanceGroup()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

// 阻止控制器中调用this.StopRun 而导致的panic
func recoverUserStop() {
	if x := recover(); x != nil {
		beego.Warn("panic:", x)
		for i := 1; ; i++ {
			_, file, line, ok := runtime.Caller(i)
			if !ok {
				break
			}
			beego.Error(file, line)
		}
	}
}

// TestListInstanceGroup_ChargingTypeFilter - 测试计费方式筛选功能
func TestListInstanceGroup_ChargingTypeFilter(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface
		models     models.Interface
		k8sClient  kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
		chargingType    string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.InstancePage
		wantErr bool
	}{
		{
			name: "Given_ValidPrepaidChargingType_When_ListInstanceGroup_Then_Success",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
				chargingType:    "Prepaid",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "clusterID",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{},
				}, nil)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									input.SetParam("orderBy", "instanceGroupID")
									input.SetParam("order", "ASC")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									// 🎯 关键：设置完整的HTTP请求上下文，确保c.GetString()能获取到参数
									req, _ := http.NewRequest("GET", "/api/instancegroups?chargingType=Prepaid", nil)
									input.Context.Request = req
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									// 🎯 关键：设置完整的HTTP请求上下文
									req, _ := http.NewRequest("GET", "/api/instancegroups?chargingType=Prepaid", nil)
									output.Context.Request = req
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						chargingTypePtr := "Prepaid"
						mockService.EXPECT().List(ctx, instancegroup.ListOptions{
							AccountID:         "account-id",
							ClusterID:         "cce-cluster",
							Role:              ccetypes.ClusterRoleNode,
							PageNo:            1,
							PageSize:          10,
							InstanceGroupName: "",
							InstanceGroupID:   "",
							ChargingType:      &chargingTypePtr,
						}).Return(&models.InstanceGroupList{
							TotalCount: 1,
							Items:      []*models.InstanceGroup{},
						}, nil)
						return mockService
					}(),
				}
			}(),
			want:    nil,
			wantErr: false,
		},
		{
			name: "Given_InvalidChargingType_When_ListInstanceGroup_Then_Error",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
				chargingType:    "InvalidType",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := metamock.NewMockInterface(ctl)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 400, // 期望返回400错误
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									input.SetParam("orderBy", "instanceGroupID")
									input.SetParam("order", "ASC")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									// 🎯 关键：设置完整的HTTP请求上下文，确保c.GetString()能获取到参数
									req, _ := http.NewRequest("GET", "/api/instancegroups?chargingType=InvalidType", nil)
									input.Context.Request = req
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									// 🎯 关键：设置完整的HTTP请求上下文
									req, _ := http.NewRequest("GET", "/api/instancegroups?chargingType=InvalidType", nil)
									output.Context.Request = req
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						// 不设置期望调用，因为应该在参数校验阶段就返回错误
						return mockService
					}(),
				}
			}(),
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.ListInstanceGroup()

			// 验证状态码
			if tt.fields.statusCode != 0 {
				if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
					t.Errorf("Expected status code %d, got %d", tt.fields.statusCode, c.Ctx.Output.Context.Request.Response.StatusCode)
				}
			}
		})
	}
}

// Test_isValidChargingType 测试计费方式参数校验函数
func Test_isValidChargingType(t *testing.T) {
	tests := []struct {
		name         string
		chargingType string
		want         bool
	}{
		{
			name:         "Given_PrepaidChargingType_When_Validate_Then_True",
			chargingType: "Prepaid",
			want:         true,
		},
		{
			name:         "Given_PostpaidChargingType_When_Validate_Then_True",
			chargingType: "Postpaid",
			want:         true,
		},
		{
			name:         "Given_BidChargingType_When_Validate_Then_True",
			chargingType: "bid",
			want:         true,
		},
		{
			name:         "Given_InvalidChargingType_When_Validate_Then_False",
			chargingType: "InvalidType",
			want:         false,
		},
		{
			name:         "Given_EmptyChargingType_When_Validate_Then_False",
			chargingType: "",
			want:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isValidChargingType(tt.chargingType)
			if got != tt.want {
				t.Errorf("isValidChargingType() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Test_handleScaleUpError 测试系统化的扩容错误处理
func Test_handleScaleUpError(t *testing.T) {
	ast := testassert.New(t)

	// 创建测试用的 controller
	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("errorcode包装的错误优先处理", func(t *testing.T) {
		c := createTestController()
		testErr := errorcode.NewInvalidParam()

		c.handleScaleUpError(testErr)

		// 验证错误处理器被调用
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal(testErr, mockHandler.ErrorCode)
	})

	t.Run("安全组数量超限错误", func(t *testing.T) {
		c := createTestController()
		testErr := securitygroup.NewSgCountLimitError()

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("默认内部服务器错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("unknown error")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("配额限制错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("exceed cluster node quota")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("权限错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("permision deny")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("AccessDenied", mockHandler.ErrorCode.Code)
	})

	t.Run("扩容业务错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("ClusterAutoscaler is enabled")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("实例不存在错误 - not found", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("The specified object is not found or resource do not exist")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
		ast.Equal("指定的实例不存在", mockHandler.ErrorMsg)
	})

	t.Run("实例不存在错误 - do not exist", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("instance i-12345 do not exist")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
		ast.Equal("指定的实例不存在", mockHandler.ErrorMsg)
	})

	t.Run("实例不存在错误 - GetServerByID failed", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("LogicBCCClient.GetServerByID i-nonexistent failed: The specified object is not found")

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
		ast.Equal("指定的实例不存在", mockHandler.ErrorMsg)
	})

	t.Run("BCE错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "NoSuchObject",
			Message:    "Resource not found",
			RequestID:  "test-request-id",
		}

		c.handleScaleUpError(testErr)

		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
	})
}

// Test_handleSecurityGroupErrors 测试安全组错误处理
func Test_handleSecurityGroupErrors(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("安全组数量超限错误", func(t *testing.T) {
		c := createTestController()
		testErr := securitygroup.NewSgCountLimitError()

		result := c.handleSecurityGroupErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("安全组获取失败", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("GetSecurityGroups failed: some error")

		result := c.handleSecurityGroupErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("安全组类型错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("invalid security group type")

		result := c.handleSecurityGroupErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("安全组ID为空", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("security group ids is empty")

		result := c.handleSecurityGroupErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("非安全组相关错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some other error")

		result := c.handleSecurityGroupErrors(testErr)

		ast.False(result)
	})
}

// Test_handleQuotaAndLimitErrors 测试配额和限制错误处理
func Test_handleQuotaAndLimitErrors(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("节点配额超限", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("exceed cluster node quota")

		result := c.handleQuotaAndLimitErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("集群规格限制", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("Exceeding the number of nodes by the cluster flavor limits")

		result := c.handleQuotaAndLimitErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("CCEInstanceNumberExceed", mockHandler.ErrorCode.Code)
	})

	t.Run("检查集群节点数量限制失败", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("check cce cluster node num limit failed")

		result := c.handleQuotaAndLimitErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("非配额限制相关错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some other error")

		result := c.handleQuotaAndLimitErrors(testErr)

		ast.False(result)
	})
}

// Test_handlePermissionAndResourceErrors 测试权限和资源错误处理
func Test_handlePermissionAndResourceErrors(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("权限拒绝", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("permision deny")

		result := c.handlePermissionAndResourceErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("AccessDenied", mockHandler.ErrorCode.Code)
		ast.Equal("权限不足", mockHandler.ErrorMsg)
	})

	t.Run("集群不存在", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("GetCluster failed")

		result := c.handlePermissionAndResourceErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
		ast.Equal("集群不存在", mockHandler.ErrorMsg)
	})

	t.Run("节点组不存在", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("failed to get instanceGroup from meta cluster")

		result := c.handlePermissionAndResourceErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InstanceGroupNotFound", mockHandler.ErrorCode.Code)
	})

	t.Run("非权限资源相关错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some other error")

		result := c.handlePermissionAndResourceErrors(testErr)

		ast.False(result)
	})
}

// Test_handleScaleUpBusinessErrors 测试扩容业务逻辑错误处理
func Test_handleScaleUpBusinessErrors(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("自动伸缩冲突", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("ClusterAutoscaler is enabled")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
		ast.Equal("节点组已开启自动伸缩，无法手动扩容", mockHandler.ErrorMsg)
	})

	t.Run("没有可加入的实例", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("not exist instance to attacch")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
		ast.Equal("没有可加入的实例", mockHandler.ErrorMsg)
	})

	t.Run("角色绑定失败", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("bind role: test-role failed")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("部分成功错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrPartialSuccess.New(ctx, "partial success")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("任务创建失败", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("failed to create scaling up task")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("非扩容业务相关错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some other error")

		result := c.handleScaleUpBusinessErrors(testErr)

		ast.False(result)
	})
}

// Test_handleBCEError 测试 BCE SDK 错误处理
func Test_handleBCEError(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("BCE 404 NoSuchObject错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "NoSuchObject",
			Message:    "The specified object is not found",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 404 NotFound错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "NotFound",
			Message:    "Resource not found",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 403错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 403,
			Code:       "AccessDenied",
			Message:    "Access denied",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("AccessDenied", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 400错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 400,
			Code:       "InvalidParam",
			Message:    "Invalid parameter",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 409错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 409,
			Code:       "Conflict",
			Message:    "Resource conflict",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 429错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 429,
			Code:       "TooManyRequests",
			Message:    "Too many requests",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 500错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 500,
			Code:       "InternalError",
			Message:    "Internal server error",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 502错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 502,
			Code:       "BadGateway",
			Message:    "Bad gateway",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 503错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 503,
			Code:       "ServiceUnavailable",
			Message:    "Service unavailable",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 504错误", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 504,
			Code:       "GatewayTimeout",
			Message:    "Gateway timeout",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InternalServerError", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE 404其他错误码", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "OtherNotFound",
			Message:    "Other not found error",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		// 由于 bce.IsNotFound 会捕获所有 404 错误，所以这里应该返回 true
		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("NoSuchObject", mockHandler.ErrorCode.Code)
	})

	t.Run("BCE未知状态码", func(t *testing.T) {
		c := createTestController()
		testErr := &bceerror.Error{
			StatusCode: 418,
			Code:       "ImATeapot",
			Message:    "I'm a teapot",
			RequestID:  "test-request-id",
		}

		result := c.handleBCEError(testErr)

		ast.False(result)
	})

	t.Run("非BCE错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some other error")

		result := c.handleBCEError(testErr)

		ast.False(result)
	})
}

// Test_handleKnownBusinessErrors 测试已知业务错误处理
func Test_handleKnownBusinessErrors(t *testing.T) {
	ast := testassert.New(t)

	createTestController := func() *InstanceGroupController {
		errHandler := errorcode.NewMockErrorHandler()
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		return &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "POST",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
			},
		}
	}

	t.Run("节点数量超限错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("Exceeding the number of nodes by the cluster flavor limits")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("CCEInstanceNumberExceed", mockHandler.ErrorCode.Code)
	})

	t.Run("缩容相关错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("could not scale down to 0 nodes")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("自动续费时间单位错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("invalid AutoRenewTimeUnit value")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("工作流冲突错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrWorkflowConflict.New(ctx, "workflow conflict")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("MultiWorkflowExistInCluster", mockHandler.ErrorCode.Code)
	})

	t.Run("HPAS不支持的操作", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrHPASNotImplemented.New(ctx, "HPAS not implemented")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("HPASNotImplemented", mockHandler.ErrorCode.Code)
		ast.Equal("HPAS operation not implemented", mockHandler.ErrorMsg)
	})

	t.Run("污点格式错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrTaintsInvalid.New(ctx, "invalid taint format")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("节点组副本数配置错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrIgReplicasMaxLessThanMin.New(ctx, "max replicas less than min")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("实例移除中的错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrIgInstanceRemovalInProgress.New(ctx, "instance removal in progress")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InvalidParam", mockHandler.ErrorCode.Code)
	})

	t.Run("权限相关错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrNoPermissions.New(ctx, "no permissions")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("AccessDenied", mockHandler.ErrorCode.Code)
	})

	t.Run("资源不存在错误", func(t *testing.T) {
		c := createTestController()
		ctx := context.Background()
		testErr := models.ErrNotExist.New(ctx, "resource not exist")

		result := c.handleKnownBusinessErrors(testErr)

		ast.True(result)
		mockHandler := c.errHandler.(*errorcode.MockErrorHandler)
		ast.Equal("InstanceGroupNotFound", mockHandler.ErrorCode.Code)
	})

	t.Run("非已知业务错误", func(t *testing.T) {
		c := createTestController()
		testErr := errors.New("some unknown error")

		result := c.handleKnownBusinessErrors(testErr)

		ast.False(result)
	})
}

func Test_getWorkflow(t *testing.T) {
	ctl := gomock.NewController(t)
	metaClient := metamock.NewMockInterface(ctl)

	igc := &InstanceGroupController{
		BaseController: BaseController{
			clientSet: &clientset.ClientSet{
				Clients: &clientset.Clients{
					MetaClient: metaClient,
				},
			},
		},
	}
	time1 := time.Now()
	time0 := time1.Add(100 * time.Second)
	want := []ccev1.Workflow{
		{
			Spec: ccetypes.WorkflowSpec{
				WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
				WorkflowConfig: ccetypes.WorkflowConfig{
					UpgradeNodesWorkflowConfig: &ccetypes.UpgradeNodesWorkflowConfig{
						InstanceGroupID: "igid",
					},
				},
				ClusterID: "clusterID",
			},
			ObjectMeta: metav1.ObjectMeta{
				CreationTimestamp: metav1.Time{
					Time: time0,
				},
				Name: "w1",
			},
		},
		{
			Spec: ccetypes.WorkflowSpec{
				WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
				WorkflowConfig: ccetypes.WorkflowConfig{
					UpgradeNodesWorkflowConfig: &ccetypes.UpgradeNodesWorkflowConfig{
						InstanceGroupID: "igid",
					},
				},
				ClusterID: "clusterID",
			},
			ObjectMeta: metav1.ObjectMeta{
				CreationTimestamp: metav1.Time{
					Time: time1,
				},
				Name: "w2",
			},
		},
	}
	metaClient.EXPECT().ListWorkflows(
		gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.WorkflowList{
		Items: []ccev1.Workflow{
			{
				Spec: ccetypes.WorkflowSpec{
					WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
					WorkflowConfig: ccetypes.WorkflowConfig{
						UpgradeNodesWorkflowConfig: &ccetypes.UpgradeNodesWorkflowConfig{
							InstanceGroupID: "igid",
						},
					},
					ClusterID: "clusterID",
				},
				ObjectMeta: metav1.ObjectMeta{
					CreationTimestamp: metav1.Time{
						Time: time0,
					},
					Name: "w1",
				},
			},
			{
				Spec: ccetypes.WorkflowSpec{
					WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
					WorkflowConfig: ccetypes.WorkflowConfig{
						UpgradeNodesWorkflowConfig: &ccetypes.UpgradeNodesWorkflowConfig{
							InstanceGroupID: "igid",
						},
					},
					ClusterID: "clusterID",
				},
				ObjectMeta: metav1.ObjectMeta{
					CreationTimestamp: metav1.Time{
						Time: time1,
					},
					Name: "w2",
				},
			},
		},
	}, nil)

	workflows, err := igc.getUpgradeNodesWorkflows(context.TODO(), "clusterID")
	assert.Assert(t, err == nil)
	assert.Assert(t, len(workflows) == 1)
	assert.Assert(t, reflect.DeepEqual(workflows["igid"], want))
}

func Test_instanceGroupExitConflictWorkflowByInstanceGroup(t *testing.T) {
	type args struct {
		cceInstanceID string
		accountID     string
		deleteOption  *ccetypes.DeleteOption
	}

	tests := []struct {
		name             string
		args             args
		instancesGroupID string
		workflows        *ccev1.WorkflowList
		wantErr          bool
	}{
		{
			name: "不存在冲突任务",
			args: args{
				cceInstanceID: "cce-instance-id",
				accountID:     "account-id",
				deleteOption:  &ccetypes.DeleteOption{},
			},
			instancesGroupID: "ig-1",
			workflows: &ccev1.WorkflowList{
				Items: []ccev1.Workflow{
					{
						Spec: ccetypes.WorkflowSpec{
							WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
						},
						Status: ccetypes.WorkflowStatus{
							WorkflowPhase: ccetypes.WorkflowPhaseFailed,
						},
					},
					{
						Spec: ccetypes.WorkflowSpec{
							WorkflowType: ccetypes.WorkflowTypeConfigureClusterEIP,
						},
						Status: ccetypes.WorkflowStatus{
							WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
						},
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.TODO()
			ctrl := gomock.NewController(t)

			mockMetaClient := metamock.NewMockInterface(ctrl)

			mockMetaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.workflows, nil)
			c := &InstanceGroupController{
				BaseController: BaseController{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: mockMetaClient,
						},
					},
				},
			}
			if err := c.exitConflictWorkflowByInstanceGroup(ctx, tt.instancesGroupID); (err != nil) != tt.wantErr {
				t.Errorf("deleteInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_instanceGroupExistConflictTaskByInstanceGroup(t *testing.T) {
	type args struct {
		cceInstanceID string
		accountID     string
		deleteOption  *ccetypes.DeleteOption
	}
	now := utils.Now()
	tests := []struct {
		name             string
		args             args
		instancesGroupID string
		workflows        *ccev1.WorkflowList
		task             *ccev1.TaskList
		wantErr          bool
	}{
		{
			name: "存在冲突任务",
			args: args{
				cceInstanceID: "cce-instance-id",
				accountID:     "account-id",
				deleteOption:  &ccetypes.DeleteOption{},
			},
			instancesGroupID: "ig-1",
			task: &ccev1.TaskList{
				Items: []ccev1.Task{
					{
						Spec: ccetypes.TaskSpec{
							TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
							CreatedTime: now.Format(time.RFC3339Nano),
							TargetRef: &ccetypes.TaskTargetReference{
								Namespace: "default",
								Name:      "ig",
							},
							AntiAffinity: ccetypes.TaskAntiAffinity{
								ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
							},
							Operation: k8sruntime.RawExtension{
								Object: &ccev1.InstanceGroupReplicasOperation{
									TypeMeta: apiv1.TypeMeta{},
									Spec: ccetypes.InstanceGroupReplicasOperationSpec{
										OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
										InstancesToBeDeleted: []string{"instance-1", "instance-2"},
									},
								},
							},
						},
						Status: ccetypes.TaskStatus{
							TaskProcesses: []ccetypes.TaskProcess{
								{
									Name:  "RemoveInstancesFromInstanceGroup",
									Phase: ccetypes.TaskProcessPhaseDone,
								},
							},
							Phase: ccetypes.TaskPhaseCollecting,
						},
					},
					{
						Spec: ccetypes.TaskSpec{
							TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
							CreatedTime: now.Format(time.RFC3339Nano),
							TargetRef: &ccetypes.TaskTargetReference{
								Namespace: "default",
								Name:      "ig",
							},
							AntiAffinity: ccetypes.TaskAntiAffinity{
								ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
							},
							Operation: k8sruntime.RawExtension{
								Object: &ccev1.InstanceGroupReplicasOperation{
									TypeMeta: apiv1.TypeMeta{},
									Spec: ccetypes.InstanceGroupReplicasOperationSpec{
										OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
										InstancesToBeDeleted: []string{"instance-1", "instance-2"},
									},
								},
							},
						},
						Status: ccetypes.TaskStatus{
							TaskProcesses: []ccetypes.TaskProcess{
								{
									Name:  "RemoveInstancesFromInstanceGroup",
									Phase: ccetypes.TaskProcessPhaseDone,
								},
							},
							Phase: ccetypes.TaskPhaseAborted,
						},
					},
					{
						Spec: ccetypes.TaskSpec{
							TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
							CreatedTime: now.Format(time.RFC3339Nano),
							TargetRef: &ccetypes.TaskTargetReference{
								Namespace: "default",
								Name:      "ig",
							},
							AntiAffinity: ccetypes.TaskAntiAffinity{
								ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
							},
							Operation: k8sruntime.RawExtension{
								Object: &ccev1.InstanceGroupReplicasOperation{
									TypeMeta: apiv1.TypeMeta{},
									Spec: ccetypes.InstanceGroupReplicasOperationSpec{
										OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
										InstancesToBeDeleted: []string{"instance-1", "instance-2"},
									},
								},
							},
						},
						Status: ccetypes.TaskStatus{
							TaskProcesses: []ccetypes.TaskProcess{
								{
									Name:  "RemoveInstancesFromInstanceGroup",
									Phase: ccetypes.TaskProcessPhaseDone,
								},
							},
							Phase: ccetypes.TaskPhasePending,
						},
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.TODO()
			ctrl := gomock.NewController(t)

			mockMetaClient := metamock.NewMockInterface(ctrl)

			mockMetaClient.EXPECT().ListTask(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.task, nil)
			c := &InstanceGroupController{
				BaseController: BaseController{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: mockMetaClient,
						},
					},
				},
			}
			if err := c.existConflictTaskByInstanceGroup(ctx, tt.instancesGroupID); (err != nil) != tt.wantErr {
				t.Errorf("deleteInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_UpdateInstanceGroupInstanceTemplate(t *testing.T) {
	var (
		ast         = testassert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
			instanceGroupService: &instancegroup.InstanceGroupService{},
		}
	)

	var clusterID, igID string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		switch key {
		case ":clusterID":
			return clusterID
		case ":instanceGroupID":
			return igID
		}
		return ""
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	var cpErr = errors.New("copier copy failed")
	patch2 := gomonkey.ApplyFunc(copier.Copy, func(toValue any, fromValue any) (err error) {
		return cpErr
	})
	defer patch2.Reset()

	var updateIGErr = errors.New("update failed")
	patch3 := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).Update, func(_ *instancegroup.InstanceGroupService, ctx context.Context, spec *ccetypes.InstanceGroupSpec, opts instancegroup.UpdateOptions) error {
		return updateIGErr
	})
	defer patch3.Reset()

	var specPreCheckErr = errors.New("spec check failed")
	patch4 := gomonkey.ApplyFunc((*BaseController).UpdateInstanceSpecPreCheck, func(_ *BaseController, instanceSpec ccetypes.InstanceSpec) error {
		return specPreCheckErr
	})
	defer patch4.Reset()

	// 没传clusterID
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 没传instanceGroupID
	clusterID = "123"
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// body unmarshal 失败
	igID = uuid.NewString()
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode, errorcode.NewMalformedJSON())

	// copy 失败
	req := ccetypes.InstanceTemplate{
		InstanceSpec: ccetypes.InstanceSpec{
			InstanceName: "node1",
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 传入InstanceTemplate，预检查失败
	cpErr = nil
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	ast.True(strings.Contains(errHandler.ErrorMsg, specPreCheckErr.Error()))

	// update失败
	specPreCheckErr = nil
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	taintsErr := updateIGErr
	updateIGErr = models.ErrTaintsInvalid.New(ctx, "test")
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	updateIGErr = taintsErr

	// 成功
	errHandler.ErrorCode = nil
	updateIGErr = nil
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Nil(errHandler.ErrorCode)

	var setAndCheckDataDirErr error
	patch5 := gomonkey.ApplyFunc(fillspec.SetAndCheckDataDir, func(deployCustomConfig ccetypes.DeployCustomConfig, runtimeType ccetypes.RuntimeType) error {
		return setAndCheckDataDirErr
	})
	defer patch5.Reset()
	patch6 := gomonkey.ApplyFuncReturn((*BaseController).errorHandlerV2)
	defer patch6.Reset()

	cpErr = nil
	specPreCheckErr = nil
	errHandler.ErrorCode = &errorcode.ErrorCode{Code: "InvalidDataRootDir"}
	setAndCheckDataDirErr = errors.New("invalid kubelet data dir")
	c.UpdateInstanceGroupInstanceTemplate()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidDataRootDir().Code)
}

func Test_UpdateInstanceGroupConfigure(t *testing.T) {
	var (
		ast         = testassert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceGroupController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
			instanceGroupService: &instancegroup.InstanceGroupService{},
		}
	)

	var clusterID, igID string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		switch key {
		case ":clusterID":
			return clusterID
		case ":instanceGroupID":
			return igID
		}
		return ""
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	var updateIGErr = errors.New("update failed")
	updateIGPatch := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).Update, func(_ *instancegroup.InstanceGroupService, ctx context.Context, spec *ccetypes.InstanceGroupSpec, opts instancegroup.UpdateOptions) error {
		return updateIGErr
	})
	defer updateIGPatch.Reset()

	var getIGErr = errors.New("get failed")
	req := ccesdk.UpdateInstanceGroupRequest{
		InstanceGroupSpec: ccetypes.InstanceGroupSpec{
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName: "node1",
					MachineType:  ccetypes.MachineTypeBCC,
					InstanceResource: ccetypes.InstanceResource{
						NUMAConfig: "2",
					},
				}},
				{InstanceSpec: ccetypes.InstanceSpec{
					InstanceName: "node2",
					MachineType:  ccetypes.MachineTypeEBC,
				}},
			},
			ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{},
		},
	}
	getIGPatch := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).Get, func(_ *instancegroup.InstanceGroupService, ctx context.Context, accountID, instanceGroupID string) (*models.InstanceGroup, error) {
		return &models.InstanceGroup{
			Spec: &req.InstanceGroupSpec,
		}, getIGErr
	})
	defer getIGPatch.Reset()

	var specPreCheckErr = errors.New("spec check failed")
	patch4 := gomonkey.ApplyFunc((*BaseController).UpdateInstanceSpecPreCheck, func(_ *BaseController, instanceSpec ccetypes.InstanceSpec) error {
		return specPreCheckErr
	})
	defer patch4.Reset()

	var checkSpecErr = errors.New("check spec failed")
	checkSpecPatch := gomonkey.ApplyFunc((*BaseController).CheckMachineSpecs, func(_ *BaseController, ctx context.Context, specs []string) error {
		return checkSpecErr
	})
	defer checkSpecPatch.Reset()

	var cluster = &ccev1.Cluster{}
	var getClusterErr = errors.New("get cluster failed")
	getClusterPatch := gomonkey.ApplyFunc((*k8s.Client).GetCluster, func(_ *k8s.Client, ctx context.Context, clusterID string) (*ccev1.Cluster, error) {
		return cluster, getClusterErr
	})
	defer getClusterPatch.Reset()

	var updateCAErr = errors.New("update ca failed")
	updateCAPatch := gomonkey.ApplyFunc((*InstanceGroupController).updateCAGroups, func(_ *InstanceGroupController, clusterID string) error {
		return updateCAErr
	})
	defer updateCAPatch.Reset()

	// 没传clusterID
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 没传instanceGroupID
	clusterID = "123"
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// body unmarshal 失败
	igID = uuid.NewString()
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode, errorcode.NewMalformedJSON())

	// 传入InstanceGroupSpec，预检查失败
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	ast.True(strings.Contains(errHandler.ErrorMsg, specPreCheckErr.Error()))

	// 获取cluster失败
	specPreCheckErr = nil
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())

	getClusterErr = meta.ErrClusterNotExist.New(ctx, "cluster not exist")
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode, errorcode.NewNoSuchObject())

	// get ig 失败
	getClusterErr = nil
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 套餐检查失败
	getIGErr = nil
	cluster = &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			MasterConfig: ccetypes.MasterConfig{
				MasterType: ccetypes.MasterTypeManagedPro,
			},
			ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
				Mode: ccetypes.ContainerNetworkModeVPCENI,
			},
		},
	}
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidMachineSpec())

	// update ig 失败
	checkSpecErr = nil
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 更新CA节点组失败
	updateIGErr = nil
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	taintsErr := updateIGErr
	updateIGErr = models.ErrTaintsInvalid.New(ctx, "test")
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	updateIGErr = taintsErr

	// 成功
	errHandler.ErrorCode = nil
	updateCAErr = nil
	c.UpdateInstanceGroupConfigure()
	ast.Nil(errHandler.ErrorCode)

	//  kubelet 和 容器运行时数据目录校验失败
	var setAndCheckDataDirErr = errors.New("invalid kubelet data dir")
	patch5 := gomonkey.ApplyFunc(fillspec.SetAndCheckDataDir, func(deployCustomConfig ccetypes.DeployCustomConfig, runtimeType ccetypes.RuntimeType) error {
		return setAndCheckDataDirErr
	})
	patch5.ApplyFuncReturn((*BaseController).errorHandlerV2)
	patch5.ApplyFuncReturn((*fillspec.BaseFiller).DeployCustomConfig, nil, nil)
	defer patch5.Reset()

	errHandler.ErrorCode = &errorcode.ErrorCode{Code: "InvalidDataRootDir"}
	c.UpdateInstanceGroupConfigure()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidDataRootDir().Code)
}

func Test_createTaskToSyncMeta(t *testing.T) {
	type args struct {
		instanceGroupID string
	}
	tests := []struct {
		name    string
		args    args
		ig      *ccev1.InstanceGroup
		wantErr bool
	}{
		{
			name: "创建任务",
			args: args{
				instanceGroupID: "ig-1",
			},
			ig: &ccev1.InstanceGroup{
				Spec: ccetypes.InstanceGroupSpec{
					ClusterID:          "cluster-1",
					CCEInstanceGroupID: "ig-1",
					UserID:             "0",
					AccountID:          "0",
					InstanceTemplates:  []ccetypes.InstanceTemplate{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.TODO()
			ctrl := gomock.NewController(t)
			mockMetaClient := metamock.NewMockInterface(ctrl)
			mockMetaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.ig, nil)
			mockMetaClient.EXPECT().CreateTask(gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.Task{}, nil)
			c := &InstanceGroupController{
				BaseController: BaseController{
					clients: &clients.Clients{
						MetaK8SClient: mockMetaClient,
					},
				},
			}
			if err := c.createTaskToSyncMeta(ctx, tt.args.instanceGroupID); (err != nil) != tt.wantErr {
				t.Errorf("deleteInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_UpdateInstanceGroupCRD(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name                  string
		fields                fields
		args                  args
		want                  *ccesdk.InstancePage
		SetAndCheckDataDirErr error
		wantErr               bool
	}{
		{
			name: "test UpgradeInstanceGroup",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-cluster",
							AccountID: "account-id",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				errHandle := errorcodemock.NewMockInterface(ctl)
				errHandle.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return()

				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{
										RequestBody: func() []byte {
											body := ccev1.InstanceGroup{
												ObjectMeta: metav1.ObjectMeta{
													Name: "cce-ig-xxx",
												},
												Spec: ccetypes.InstanceGroupSpec{
													ClusterID:          "cce-cluster",
													AccountID:          "account-id",
													CCEInstanceGroupID: "cce-ig-xxx",
													InstanceTemplate: ccetypes.InstanceTemplate{
														InstanceSpec: ccetypes.InstanceSpec{
															DeployCustomConfig: ccetypes.DeployCustomConfig{
																KubeletRootDir: "/cce",
															},
														},
													},
												},
												Status: ccetypes.InstanceGroupStatus{},
											}
											requestBody, err := json.Marshal(body)
											if err != nil {
												t.Errorf(err.Error())
											}
											return requestBody
										}(),
									}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")

									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().UpdateCRD(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

						return mockService
					}(),
					errHandler: errHandle,
				}
			}(),
			SetAndCheckDataDirErr: errors.New("invalid data directory"),
			wantErr:               true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel

			patch := gomonkey.ApplyFuncReturn(fillspec.SetAndCheckDataDir, tt.SetAndCheckDataDirErr)
			patch.ApplyFuncReturn(c.errorHandlerV2)
			defer patch.Reset()
			defer recoverUserStop()
			c.UpdateInstanceGroupCRD()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func Test_AttachInstancesToInstanceGroup(t *testing.T) {
	var (
		ast         = testassert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceGroupController{
			instanceGroupService: &instancegroup.InstanceGroupService{},
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				models: &models.Client{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
		}
	)

	var clusterID, instanceGroupID string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		if strings.Contains(key, "cluster") {
			return clusterID
		}

		if strings.Contains(key, "instanceGroup") {
			return instanceGroupID
		}
		return ""
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	varPatch := gomonkey.ApplyGlobalVar(&maxAttachInstanceCount, 1)
	defer varPatch.Reset()

	var isWeak = true
	patch2 := gomonkey.ApplyFunc((*BaseController).CheckInternalUserWeakPassword, func(_ *BaseController, ctx context.Context, adminPassword string) bool {
		return isWeak
	})
	defer patch2.Reset()

	conflictErr := errors.New("conflict error")
	patch3 := gomonkey.ApplyFunc((*InstanceGroupController).existConflictTaskByInstanceGroup, func(_ *InstanceGroupController, ctx context.Context,
		instanceGroupID string) error {
		return conflictErr
	})
	defer patch3.Reset()

	scaleUpErr := errors.New("scale up error")
	taskID := uuid.NewString()
	patch4 := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).ScaleUpExistNode, func(_ *instancegroup.InstanceGroupService, ctx context.Context,
		service services.Interface, accountID string, clusterID string, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption,
		instanceCount int) (string, error) {
		return taskID, scaleUpErr
	})
	defer patch4.Reset()

	// 集群id不存在
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	clusterID = "cluster-id"

	// 节点组ID不存在
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	instanceGroupID = "instanceGroup-id"
	c.Ctx.Input.RequestBody = []byte("a")

	// 解析body失败
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewMalformedJSON().Code)
	c.Ctx.Input.RequestBody = []byte(`{"inCluster":true}`)

	// 参数缺少
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	c.Ctx.Input.RequestBody = []byte(`{"inCluster":false}`)

	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	req := ccesdk.ScaleUpExistInstanceGroupOption{
		Incluster: false,
		ExistedInstances: []*ccesdk.InstanceSet{
			{
				InstanceSpec: ccetypes.InstanceSpec{
					CCEInstanceID: "instance-id1",
					AdminPassword: "password",
				},
			},
			{
				InstanceSpec: ccetypes.InstanceSpec{
					CCEInstanceID: "instance-id2",
				},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInstancesCountBeyondLimit().Code)

	// 弱密码
	varPatch.Reset()
	varPatch = gomonkey.ApplyGlobalVar(&maxAttachInstanceCount, 10)
	defer varPatch.Reset()
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalWeakPassword().Code)
	isWeak = false

	// 冲突task
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	conflictErr = nil

	// 扩容报错
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)
	scaleUpErr = securitygroup.NewSgCountLimitError()

	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)
	scaleUpErr = errorcode.NewAccessDenied()

	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewAccessDenied().Code)
	scaleUpErr = nil

	// 成功
	errHandler.ErrorCode = nil
	c.AttachInstancesToInstanceGroup()
	ast.Nil(errHandler.ErrorCode)

	// 过长密码检查失败
	req1 := ccesdk.ScaleUpExistInstanceGroupOption{
		Incluster: false,
		ExistedInstances: []*ccesdk.InstanceSet{
			{
				InstanceSpec: ccetypes.InstanceSpec{
					CCEInstanceID: "instance-id1",
					AdminPassword: "012345678901234567890123456789012345678901234567890123456789",
				},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req1)
	c.AttachInstancesToInstanceGroup()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewTooLongPassword().Code)
}

// Test_AttachInstancesToInstanceGroup_NoSuchObjectError 专门测试NoSuchObject错误处理
func Test_AttachInstancesToInstanceGroup_NoSuchObjectError(t *testing.T) {
	var (
		ast         = testassert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceGroupController{
			instanceGroupService: &instancegroup.InstanceGroupService{},
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				clientSet: &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: nil, // 设置为 nil，我们会 mock existConflictTaskByInstanceGroup 方法
					},
				},
				models: &models.Client{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[any]any),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
				accountID:   "test-account-id", // 添加 accountID
			},
		}
	)

	// Set path parameters
	c.Ctx.Input.SetParam(":clusterID", "cluster-id")
	c.Ctx.Input.SetParam(":instanceGroupID", "instanceGroup-id")

	// Note: Path parameters are set directly using SetParam above, no need to mock GetPathParams

	// Mock ServeJSON
	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	// Mock CheckInternalUserWeakPassword to return false (no weak password)
	patch2 := gomonkey.ApplyFunc((*BaseController).CheckInternalUserWeakPassword, func(_ *BaseController, ctx context.Context, adminPassword string) bool {
		return false
	})
	defer patch2.Reset()

	// Mock existConflictTaskByInstanceGroup to return no conflict
	patch3 := gomonkey.ApplyFunc((*InstanceGroupController).existConflictTaskByInstanceGroup, func(_ *InstanceGroupController, ctx context.Context,
		instanceGroupID string) error {
		return nil
	})
	defer patch3.Reset()

	// Test case 1: NoSuchObject error - 使用正确的 bceerror.Error 类型
	patch4 := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).ScaleUpExistNode, func(_ *instancegroup.InstanceGroupService, ctx context.Context,
		service services.Interface, accountID string, clusterID string, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption,
		instanceCount int) (string, error) {
		return "", &bceerror.Error{
			StatusCode: 404,
			Code:       "NoSuchObject",
			Message:    "The specified object is not found or resource do not exist.",
			RequestID:  "67ed3cdb-a500-4cc6-8f75-cac9e58d44fc",
		}
	})
	defer patch4.Reset()

	// Prepare valid request body
	req := ccesdk.ScaleUpExistInstanceGroupOption{
		Incluster: false,
		ExistedInstances: []*ccesdk.InstanceSet{
			{
				InstanceSpec: ccetypes.InstanceSpec{
					CCEInstanceID: "instance-id1",
					AdminPassword: "password",
				},
			},
		},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)

	// Test NoSuchObject error - 应该返回 NoSuchObject
	c.AttachInstancesToInstanceGroup()
	actualCode1 := errHandler.ErrorCode.Code
	ast.Equal("NoSuchObject", actualCode1, "Expected NoSuchObject for NoSuchObject error, got: %s", actualCode1)

	// Test case 2: NotFound error - 使用正确的 bceerror.Error 类型
	patch4.Reset()
	patch5 := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).ScaleUpExistNode, func(_ *instancegroup.InstanceGroupService, ctx context.Context,
		service services.Interface, accountID string, clusterID string, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption,
		instanceCount int) (string, error) {
		return "", &bceerror.Error{
			StatusCode: 404,
			Code:       "NotFound",
			Message:    "Resource not found",
			RequestID:  "test-request-id",
		}
	})
	defer patch5.Reset()

	errHandler.ErrorCode = nil // Reset error handler
	c.AttachInstancesToInstanceGroup()
	actualCode2 := errHandler.ErrorCode.Code
	ast.Equal("NoSuchObject", actualCode2, "Expected NoSuchObject for NotFound error, got: %s", actualCode2)

	// Test case 3: Other error (should return InternalServerError)
	patch5.Reset()
	patch6 := gomonkey.ApplyFunc((*instancegroup.InstanceGroupService).ScaleUpExistNode, func(_ *instancegroup.InstanceGroupService, ctx context.Context,
		service services.Interface, accountID string, clusterID string, instanceGroupID string, param ccesdk.ScaleUpExistInstanceGroupOption,
		instanceCount int) (string, error) {
		return "", errors.New("some other error")
	})
	defer patch6.Reset()

	errHandler.ErrorCode = nil // Reset error handler
	c.AttachInstancesToInstanceGroup()
	actualCode3 := errHandler.ErrorCode.Code
	ast.Equal("InternalServerError", actualCode3, "Expected InternalServerError for other errors, got: %s", actualCode3)
}

// Test_AttachInstancesToInstanceGroup_ErrorHandling 专门测试错误处理逻辑
func Test_AttachInstancesToInstanceGroup_ErrorHandling(t *testing.T) {
	ast := testassert.New(t)

	// 创建一个控制器实例来测试错误处理方法
	errHandler := errorcode.NewMockErrorHandler()
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	c := &InstanceGroupController{
		BaseController: BaseController{
			ctx:        ctx,
			cancel:     cancel,
			errHandler: errHandler,
			Controller: beego.Controller{
				Data: make(map[any]any),
				Ctx: &beegocontext.Context{
					Request: &http.Request{
						Method: "GET",
						URL:    &url.URL{Path: "/v1"},
						Header: make(http.Header),
					},
					Input: &beegocontext.BeegoInput{},
				},
			},
		},
	}

	// 测试 handleScaleUpError 方法对 NoSuchObject 错误的处理
	t.Run("NoSuchObject error should return NoSuchObject code", func(t *testing.T) {
		// 创建一个 NoSuchObject 错误
		bceErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "NoSuchObject",
			Message:    "The specified object is not found or resource do not exist.",
			RequestID:  "67ed3cdb-a500-4cc6-8f75-cac9e58d44fc",
		}

		// 调用错误处理方法
		c.handleScaleUpError(bceErr)

		// 验证返回的错误码
		ast.NotNil(errHandler.ErrorCode, "Error code should not be nil")
		ast.Equal("NoSuchObject", errHandler.ErrorCode.Code, "Expected NoSuchObject error code")
	})

	// 测试 NotFound 错误也应该返回 NoSuchObject
	t.Run("NotFound error should return NoSuchObject code", func(t *testing.T) {
		errHandler.ErrorCode = nil // Reset

		bceErr := &bceerror.Error{
			StatusCode: 404,
			Code:       "NotFound",
			Message:    "Resource not found",
			RequestID:  "test-request-id",
		}

		c.handleScaleUpError(bceErr)

		ast.NotNil(errHandler.ErrorCode, "Error code should not be nil")
		ast.Equal("NoSuchObject", errHandler.ErrorCode.Code, "Expected NoSuchObject for NotFound error")
	})

	// 测试其他错误应该返回 InternalServerError
	t.Run("Other errors should return InternalServerError", func(t *testing.T) {
		errHandler.ErrorCode = nil // Reset

		genericErr := errors.New("some other error")

		c.handleScaleUpError(genericErr)

		ast.NotNil(errHandler.ErrorCode, "Error code should not be nil")
		ast.Equal("InternalServerError", errHandler.ErrorCode.Code, "Expected InternalServerError for generic error")
	})

	// 测试错误字符串解析
	t.Run("Error string parsing should work", func(t *testing.T) {
		err := errors.New(`[Error Message: "The specified object is not found or resource do not exist.", Error Code: "NoSuchObject", Status Code: 404, Request Id: "67ed3cdb-a500-4cc6-8f75-cac9e58d44fc"]`)

		// 检查错误字符串是否包含NoSuchObject
		hasNoSuchObject := strings.Contains(err.Error(), "NoSuchObject")
		ast.True(hasNoSuchObject, "Error should contain NoSuchObject")
	})

	t.Run("Status Code 404 error should be detected", func(t *testing.T) {
		err := errors.New("some error with Status Code: 404")

		// 检查错误字符串是否包含Status Code: 404
		hasStatusCode404 := strings.Contains(err.Error(), "Status Code: 404")
		ast.True(hasStatusCode404, "Error should contain Status Code: 404")
	})

	t.Run("Other errors should not match 404 patterns", func(t *testing.T) {
		err := errors.New("some other error")

		// 检查错误字符串不包含NoSuchObject或Status Code: 404
		hasNoSuchObject := strings.Contains(err.Error(), "NoSuchObject")
		hasStatusCode404 := strings.Contains(err.Error(), "Status Code: 404")
		ast.False(hasNoSuchObject, "Error should not contain NoSuchObject")
		ast.False(hasStatusCode404, "Error should not contain Status Code: 404")
	})
}

func Test_UpdateInstanceGroupReplicas(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name                  string
		fields                fields
		args                  args
		want                  *ccesdk.InstancePage
		SetAndCheckDataDirErr error
		wantErr               bool
	}{
		{
			name: "Test_UpdateInstanceGroupReplicas failed",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				mockMetaClient := metamock.NewMockInterface(ctl)
				mockMetaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{},
					Status: ccetypes.InstanceGroupStatus{
						ActualReplicas: 1,
					},
				}, nil).AnyTimes()

				model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
						AccountID: "account-id",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}, nil)
				model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
					KubeConfigFile: "kube-config-file",
				}, nil)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: mockMetaClient,
					},
				}
				errHandle := errorcodemock.NewMockInterface(ctl)
				errHandle.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return()

				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{
										RequestBody: func() []byte {
											body := ccesdk.UpdateInstanceGroupReplicasRequest{
												Replicas: 2,
											}
											requestBody, err := json.Marshal(body)
											if err != nil {
												t.Errorf(err.Error())
											}
											return requestBody
										}(),
									}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().
							Return(models.ErrWorkflowConflict)

						return mockService
					}(),
					errHandler: errHandle,
				}
			}(),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel

			patch := gomonkey.ApplyFuncReturn((*InstanceGroupController).existConflictTaskByInstanceGroup, nil)
			patch.ApplyFuncReturn((*instancegroup.InstanceGroupService).Update, errors.New("could not scale down to"))
			defer patch.Reset()
			defer recoverUserStop()
			c.UpdateInstanceGroupReplicas()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func Test_UpdateInstanceGroupClusterAutoscalerSpec(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancegroup.Interface
		errHandler errorcode.Interface

		// clients   *clients.Clients
		models models.Interface

		k8sClient kubernetes.Interface
	}
	type args struct {
		ctx             context.Context
		clusterID       string
		instanceGroupID string
	}
	tests := []struct {
		name                  string
		fields                fields
		args                  args
		want                  *ccesdk.InstancePage
		SetAndCheckDataDirErr error
		wantErr               bool
	}{
		{
			name: "test UpdateInstanceGroupClusterAutoscalerSpec failed",
			args: args{
				ctx:             ctx,
				clusterID:       "cce-cluster",
				instanceGroupID: "ig-aa",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-cluster",
							AccountID: "account-id",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					model.EXPECT().GetAdminKubeConfigCompatibility(ctx, "cce-cluster", models.KubeConfigTypeInternal).Return(&models.KubeConfig{
						KubeConfigFile: "kube-config-file",
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				errHandle := errorcodemock.NewMockInterface(ctl)
				errHandle.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return()

				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{
										RequestBody: func() []byte {
											body := ccesdk.ClusterAutoscalerSpec{
												Enabled:              true,
												MinReplicas:          0,
												MaxReplicas:          10,
												ScalingGroupPriority: 5,
											}
											requestBody, err := json.Marshal(body)
											if err != nil {
												t.Errorf(err.Error())
											}
											return requestBody
										}(),
									}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")

									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancegroup.Interface {
						mockService := mock.NewMockInterface(ctl)
						mockService.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().
							Return(models.ErrWorkflowConflict)

						return mockService
					}(),
					errHandler: errHandle,
				}
			}(),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel

			defer recoverUserStop()
			c.UpdateInstanceGroupClusterAutoscalerSpec()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func TestInstanceGroupController_DeleteInstanceGroup(t *testing.T) {
	type fields struct {
		controller beego.Controller
		ctx        context.Context
		clientSet  *clientset.ClientSet
		accountID  string
		models     models.Interface
		errHandler errorcode.Interface
		service    instancegroup.Interface
	}

	tests := []struct {
		name               string
		fields             fields
		clusterID          string
		instanceGroupID    string
		deleteInstances    string
		releaseAllResource string
		wantErr            bool
		errLevel           errorcode.Level
		statusCode         int
	}{
		{
			name: "delete instance group failed",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockService := mock.NewMockInterface(ctrl)
				mockModels := models.NewMockInterface(ctrl)
				mockErrHandler := errorcodemock.NewMockInterface(ctrl)
				mockErrHandler.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return()

				mockService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{},
				}, nil)
				mockService.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("ig not exists"))
				return fields{
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceGroupID", "cce-ig-xxx")
									input.SetData("RequestID", "xxxx")

									input.SetParam("deleteInstances", "true")
									input.SetParam("releaseAllResources", "true")

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					ctx:        context.Background(),
					clientSet:  &clientset.ClientSet{},
					accountID:  "test-account",
					models:     mockModels,
					errHandler: mockErrHandler,
					service:    mockService,
				}
			}(),
			clusterID:       "cluster-1",
			instanceGroupID: "ig-1",
			wantErr:         true,
			errLevel:        errorcode.LevelByUser,
			statusCode:      404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceGroupController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.models,
					errHandler: tt.fields.errHandler,
				},
				instanceGroupService: tt.fields.service,
			}

			patch := gomonkey.ApplyFuncReturn((*InstanceGroupController).exitConflictWorkflowByInstanceGroup, nil)
			patch.ApplyFuncReturn((*InstanceGroupController).existConflictTaskByInstanceGroup, nil)
			// patch.ApplyFuncReturn((*BaseController).errorHandlerV2)
			defer patch.Reset()
			defer recoverUserStop()

			c.DeleteInstanceGroup()
			if tt.wantErr {
				assert.Equal(t, c.Ctx.Output.Context.Request.Response.StatusCode, tt.statusCode)
			} else {
				assert.Equal(t, c.Ctx.Output.Context.Request.Response.StatusCode, 200)
			}
		})
	}
}
