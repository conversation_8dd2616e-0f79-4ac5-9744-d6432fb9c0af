package controllers

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sruntime "k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	beegoctx "github.com/astaxie/beego/context"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/esg"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/appservice"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	bccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	errorcodemock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	pkgmetamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/app/app-service/resource/node"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s"
	k8smock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s/mock"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
	instancep "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance"
	instanceMock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instance/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup"
	quotamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/quota/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/speccheck"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/userscript"
)

// MockUserScriptService is a mock implementation of userscript.Interface
type MockUserScriptService struct {
	GetFunc    func(ctx context.Context, scriptID string) (*models.UserScript, error)
	CreateFunc func(ctx context.Context, accountID, userID, userScript string) (string, error)
}

func (m *MockUserScriptService) Get(ctx context.Context, scriptID string) (*models.UserScript, error) {
	if m.GetFunc != nil {
		return m.GetFunc(ctx, scriptID)
	}
	return nil, fmt.Errorf("not implemented")
}

func (m *MockUserScriptService) Create(ctx context.Context, accountID, userID, userScript string) (string, error) {
	if m.CreateFunc != nil {
		return m.CreateFunc(ctx, accountID, userID, userScript)
	}
	return "", fmt.Errorf("not implemented")
}

func Test_existConflictWithInstanceGroupScaling(t *testing.T) {
	type args struct {
		cceInstanceID string
		accountID     string
		deleteOption  *ccetypes.DeleteOption
	}
	type fields struct {
		BaseController BaseController
	}
	now := utils.Now()
	tests := []struct {
		name    string
		args    args
		wantErr bool
		fields  fields
	}{
		{
			name: "存在冲突任务",
			args: args{
				cceInstanceID: "cce-instance-id",
				accountID:     "account-id",
				deleteOption:  &ccetypes.DeleteOption{},
			},
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				task := &ccev1.TaskList{
					Items: []ccev1.Task{
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.InstanceGroupIDLabelKey: "igId1",
								},
							},
							Spec: ccetypes.TaskSpec{
								TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
								CreatedTime: now.Format(time.RFC3339Nano),
								TargetRef: &ccetypes.TaskTargetReference{
									Namespace: "default",
									Name:      "ig",
								},
								AntiAffinity: ccetypes.TaskAntiAffinity{
									ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
								},
								Operation: k8sruntime.RawExtension{
									Object: &ccev1.InstanceGroupReplicasOperation{
										Spec: ccetypes.InstanceGroupReplicasOperationSpec{
											OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
											InstancesToBeDeleted: []string{"instance-1", "instance-2"},
										},
									},
								},
							},
							Status: ccetypes.TaskStatus{
								TaskProcesses: []ccetypes.TaskProcess{
									{
										Name:  "RemoveInstancesFromInstanceGroup",
										Phase: ccetypes.TaskProcessPhaseDone,
									},
								},
								Phase: ccetypes.TaskPhaseCollecting,
							},
						},
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.InstanceGroupIDLabelKey: "igId2",
								},
							},
							Spec: ccetypes.TaskSpec{
								TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
								CreatedTime: now.Format(time.RFC3339Nano),
								TargetRef: &ccetypes.TaskTargetReference{
									Namespace: "default",
									Name:      "ig",
								},
								AntiAffinity: ccetypes.TaskAntiAffinity{
									ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
								},
								Operation: k8sruntime.RawExtension{
									Object: &ccev1.InstanceGroupReplicasOperation{
										Spec: ccetypes.InstanceGroupReplicasOperationSpec{
											OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
											InstancesToBeDeleted: []string{"instance-1", "instance-2"},
										},
									},
								},
							},
							Status: ccetypes.TaskStatus{
								TaskProcesses: []ccetypes.TaskProcess{
									{
										Name:  "RemoveInstancesFromInstanceGroup",
										Phase: ccetypes.TaskProcessPhaseDone,
									},
								},
								Phase: ccetypes.TaskPhaseAborted,
							},
						},
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.InstanceGroupIDLabelKey: "igId3",
								},
							},
							Spec: ccetypes.TaskSpec{
								TaskType:    ccetypes.TaskTypeInstanceGroupReplicas,
								CreatedTime: now.Format(time.RFC3339Nano),
								TargetRef: &ccetypes.TaskTargetReference{
									Namespace: "default",
									Name:      "ig",
								},
								AntiAffinity: ccetypes.TaskAntiAffinity{
									ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: "ig",
								},
								Operation: k8sruntime.RawExtension{
									Object: &ccev1.InstanceGroupReplicasOperation{
										Spec: ccetypes.InstanceGroupReplicasOperationSpec{
											OperationType:        ccetypes.InstanceGroupOperationTypeRepair,
											InstancesToBeDeleted: []string{"instance-1", "instance-2"},
										},
									},
								},
							},
							Status: ccetypes.TaskStatus{
								TaskProcesses: []ccetypes.TaskProcess{
									{
										Name:  "RemoveInstancesFromInstanceGroup",
										Phase: ccetypes.TaskProcessPhaseDone,
									},
								},
								Phase: ccetypes.TaskPhasePending,
							},
						},
					},
				}

				metaClient.EXPECT().ListTask(gomock.Any(), gomock.Any(), gomock.Any()).Return(task, nil)

				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        ctx,
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}}
			}(),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.TODO()
			errCode, _, _ := existConflictWithInstanceGroupScaling(ctx, tt.fields.BaseController.clientSet.MetaClient, "1", map[string]string{
				"a": "igId1",
			})
			assert.Equal(t, tt.wantErr, errCode != nil)
		})
	}
}

func Test_deleteInstance(t *testing.T) {
	type args struct {
		ctl           *gomock.Controller
		ctx           context.Context
		cceInstanceID string
		accountID     string
		deleteOption  *ccetypes.DeleteOption
		model         models.Interface
		k8sClient     k8s.Interface
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Instance 在 MetaCluster 不存在, 修改数据库为 deleted",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				k8sClient := k8smock.NewMockInterface(ctl)
				model := models.NewMockInterface(ctl)

				gomock.InOrder(
					k8sClient.EXPECT().GetInstance(ctx, "cce-instance-id").Return(nil, nil),
					model.EXPECT().UpdateInstancePhase(ctx, "cce-instance-id", "account-id", ccetypes.InstancePhaseDeleted).Return(nil),
				)

				return args{
					ctl:           ctl,
					ctx:           ctx,
					cceInstanceID: "cce-instance-id",
					accountID:     "account-id",
					deleteOption:  &ccetypes.DeleteOption{},
					model:         model,
					k8sClient:     k8sClient,
				}
			}(),
			wantErr: false,
		},
		{
			name: "Instance 在 MetaCluster 存在, 删除 CRD",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				k8sClient := k8smock.NewMockInterface(ctl)
				model := models.NewMockInterface(ctl)

				gomock.InOrder(
					k8sClient.EXPECT().GetInstance(ctx, "cce-instance-id").Return(&ccev1.Instance{}, nil),
					k8sClient.EXPECT().ReconcileInstanceDeleted(ctx, "cce-instance-id", &ccetypes.DeleteOption{}).Return(nil),
				)

				return args{
					ctl:           ctl,
					ctx:           ctx,
					cceInstanceID: "cce-instance-id",
					accountID:     "account-id",
					deleteOption:  &ccetypes.DeleteOption{},
					model:         model,
					k8sClient:     k8sClient,
				}
			}(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := deleteInstance(tt.args.ctx, tt.args.cceInstanceID, tt.args.accountID, tt.args.deleteOption, tt.args.model, tt.args.k8sClient); (err != nil) != tt.wantErr {
				t.Errorf("deleteInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_exitConflictWorkflowByInstanceGroup(t *testing.T) {
	type args struct {
		cceInstanceID string
		accountID     string
		deleteOption  *ccetypes.DeleteOption
	}

	tests := []struct {
		name             string
		args             args
		instancesInGroup map[string][]string
		workflows        *ccev1.WorkflowList
		wantErr          bool
	}{
		{
			name: "不存在冲突任务",
			args: args{
				cceInstanceID: "cce-instance-id",
				accountID:     "account-id",
				deleteOption:  &ccetypes.DeleteOption{},
			},
			instancesInGroup: map[string][]string{
				"group-id-1": {"cce-i-111"},
			},
			workflows: &ccev1.WorkflowList{
				Items: []ccev1.Workflow{
					{
						Spec: ccetypes.WorkflowSpec{
							WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
						},
						Status: ccetypes.WorkflowStatus{
							WorkflowPhase: ccetypes.WorkflowPhaseFailed,
						},
					},
					{
						Spec: ccetypes.WorkflowSpec{
							WorkflowType: ccetypes.WorkflowTypeConfigureClusterEIP,
						},
						Status: ccetypes.WorkflowStatus{
							WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
						},
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.TODO()
			ctrl := gomock.NewController(t)

			mockMetaClient := pkgmetamock.NewMockInterface(ctrl)

			mockMetaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.workflows, nil)
			c := &InstanceController{
				BaseController: BaseController{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: mockMetaClient,
						},
					},
				},
			}
			if err := c.exitConflictWorkflowByInstanceGroup(ctx, tt.instancesInGroup); (err != nil) != tt.wantErr {
				t.Errorf("deleteInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestInstanceController_deleteNodeAllowed(t *testing.T) {
	type fields struct {
		BaseController BaseController
	}
	type args struct {
		ctx           context.Context
		clusterID     string
		deleteNodeNum int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int
		want1   bool
		wantErr bool
	}{
		{
			name: "非托管集群，允许删除",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeCustom,
						},
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 10,
			},
			want:    3,
			want1:   true,
			wantErr: false,
		},

		{
			name: "托管集群，有Annotation，允许删除",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							ccev1.AnnotationManagedClusterMinNodeNum: "5",
						},
					},
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
					model.EXPECT().GetInstanceNum(ctx, "accountID", "clusterID", models.InstanceKeywordTypeDefault, "", false).Return(6, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 1,
			},
			want:    5,
			want1:   true,
			wantErr: false,
		},

		{
			name: "托管集群，有Annotation，不允许删除",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							ccev1.AnnotationManagedClusterMinNodeNum: "5",
						},
					},
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
					model.EXPECT().GetInstanceNum(ctx, "accountID", "clusterID", models.InstanceKeywordTypeDefault, "", false).Return(6, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 2,
			},
			want:    5,
			want1:   false,
			wantErr: false,
		},

		{
			name: "正在删除的集群无限制",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							ccev1.AnnotationManagedClusterMinNodeNum: "5",
						},
					},
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
					Status: ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseDeleting,
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 5,
			},
			want:    3,
			want1:   true,
			wantErr: false,
		},

		{
			name: "minNodeNum <= 0 无限制",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							ccev1.AnnotationManagedClusterMinNodeNum: "0",
						},
					},
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
					Status: ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseDeleting,
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 5,
			},
			want:    3,
			want1:   true,
			wantErr: false,
		},

		{
			name: "托管集群，无Annotation，不允许删除",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
					model.EXPECT().GetInstanceNum(ctx, "accountID", "clusterID", models.InstanceKeywordTypeDefault, "", false).Return(6, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 3,
			},
			want:    3,
			want1:   true,
			wantErr: false,
		},

		{
			name: "托管集群，无Annotation，允许删除",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							MasterType: ccetypes.MasterTypeManaged,
						},
					},
				}

				gomock.InOrder(
					k8sClient.EXPECT().GetCluster(ctx, "clusterID").Return(cluster, nil),
					model.EXPECT().GetInstanceNum(ctx, "accountID", "clusterID", models.InstanceKeywordTypeDefault, "", false).Return(6, nil),
				)
				return fields{BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        context.TODO(),
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				}}
			}(),
			args: args{
				ctx:           context.TODO(),
				clusterID:     "clusterID",
				deleteNodeNum: 4,
			},
			want:    3,
			want1:   false,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceController{
				BaseController: tt.fields.BaseController,
			}
			got, got1, err := c.deleteNodeAllowed(tt.args.ctx, tt.args.clusterID, tt.args.deleteNodeNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("deleteNodeAllowed() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("deleteNodeAllowed() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("deleteNodeAllowed() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_VerifyInstancesDependency(t *testing.T) {
	instId1 := "i-iMazyvl1"
	openbcc1 := &bcc.Instance{
		InstanceID:   instId1,
		InstanceName: "inst01-1klkjofj9801",
		Hostname:     "inst01-1klkjofj9801",
		InstanceType: "BCC",
	}

	instId2 := "i-iMazyvl2"
	openbcc2 := &bcc.Instance{
		InstanceID:   instId2,
		InstanceName: "inst02-09fjjfds98x90s",
		Hostname:     "inst02-09fjjfds98x90s",
		InstanceType: "BCC",
	}

	instId3 := "i-iMazyvl3"
	openbcc3 := &bcc.Instance{
		InstanceID:   instId3,
		InstanceName: "inst02-09fjjfds98x90s0o9fue390j0qa9fj00000f209ebaeoql",
		Hostname:     "inst02-09fjjfds98x90s0o9fue390j0qa9fj00000f209ebaeoql",
		InstanceType: "BCC",
	}

	ctx1, cancel1 := context.WithCancel(context.TODO())
	ctx2, cancel2 := context.WithCancel(context.TODO())
	ctx3, cancel3 := context.WithCancel(context.TODO())

	type fields struct {
		InstanceController InstanceController
	}
	type args struct {
		ctx           context.Context
		clusterID     string
		cceInstanceID string
		instanceSet   []*ccesdk.InstanceSet
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantErr    bool
		wantCancel bool
		wantIRet   int
	}{
		{
			name: "Positive Case 1",
			fields: func() fields {
				ctx := ctx1
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)

				model := models.NewMockInterface(ctrl)

				bccClient.EXPECT().DescribeInstance(ctx, instId1, nil).Return(openbcc1, nil)
				bccClient.EXPECT().DescribeInstance(ctx, instId2, nil).Return(openbcc2, nil)
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()

				return fields{
					InstanceController: InstanceController{
						BaseController: BaseController{
							Controller: beego.Controller{},
							ctx:        ctx,
							cancel:     cancel1,
							clients: &clients.Clients{
								K8SClient: k8sClient,
								BCCClient: bccClient,
								STSClient: stsClient,
							},
							config: &configuration.Config{
								ManagedClusterMinNodeNum: 3,
							},
							models:    model,
							accountID: "accountID",
						}}}
			}(),
			args: func() args {
				return args{
					ctx:           ctx1,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					instanceSet: []*ccesdk.InstanceSet{
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBCC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId1,
								},
							},
						},
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBCC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId2,
								},
							},
						},
					},
				}
			}(),
			wantErr:    false,
			wantCancel: false,
			wantIRet:   2,
		},
		{
			name: "Negative Case 1: Bad MachineType",
			fields: func() fields {
				ctx := ctx2
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)

				model := models.NewMockInterface(ctrl)

				bccClient.EXPECT().DescribeInstance(ctx, instId1, nil).Return(openbcc1, nil)
				bccClient.EXPECT().DescribeInstance(ctx, instId2, nil).Return(openbcc2, nil)
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()

				return fields{
					InstanceController: InstanceController{

						BaseController: BaseController{
							Controller: beego.Controller{},
							ctx:        ctx,
							cancel:     cancel2,
							clients: &clients.Clients{
								K8SClient: k8sClient,
								BCCClient: bccClient,
								STSClient: stsClient,
							},
							config: &configuration.Config{
								ManagedClusterMinNodeNum: 3,
							},
							models:    model,
							accountID: "accountID",
						}}}
			}(),
			args: func() args {
				ctx := ctx2

				return args{
					ctx:           ctx,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					instanceSet: []*ccesdk.InstanceSet{
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBBC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId1,
								},
							},
						},
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBCC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId2,
								},
							},
						},
					},
				}
			}(),
			wantErr:    false,
			wantCancel: false,
			wantIRet:   1,
		},
		{
			name: "Negative Case 2: HostName Exceed Limit",
			fields: func() fields {
				ctx := ctx3
				ctrl := gomock.NewController(t)

				k8sClient := metamock.NewMockInterface(ctrl)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)

				model := models.NewMockInterface(ctrl)

				bccClient.EXPECT().DescribeInstance(ctx, instId1, nil).Return(openbcc1, nil)
				bccClient.EXPECT().DescribeInstance(ctx, instId2, nil).Return(openbcc2, nil)
				bccClient.EXPECT().DescribeInstance(ctx, instId3, nil).Return(openbcc3, nil)
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()

				var f fields = fields{
					InstanceController: InstanceController{
						BaseController: BaseController{
							Controller: beego.Controller{},
							ctx:        ctx,
							cancel:     cancel3,
							clients: &clients.Clients{
								K8SClient: k8sClient,
								BCCClient: bccClient,
								STSClient: stsClient,
							},
							config: &configuration.Config{
								ManagedClusterMinNodeNum: 3,
							},
							models:    model,
							accountID: "accountID",
						}}}
				f.InstanceController.Controller.Ctx = &beegoctx.Context{}
				f.InstanceController.Controller.Ctx.Request = &http.Request{
					Method: "GET",
					URL:    &url.URL{Path: "testpath"},
				}

				e := errorcodemock.NewMockInterface(ctrl)
				e.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
				f.InstanceController.errHandler = e

				return f
			}(),
			args: func() args {
				ctx := ctx3

				return args{
					ctx:           ctx,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					instanceSet: []*ccesdk.InstanceSet{
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBBC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId1,
								},
							},
						},
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBCC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId2,
								},
							},
						},
						{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeBCC,
								Existed:     true,
								ExistedOption: ccetypes.ExistedOption{
									ExistedInstanceID: instId3,
								},
							},
						},
					},
				}
			}(),
			wantErr:    false,
			wantCancel: true,
			wantIRet:   0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var i int
			var err error
			c := tt.fields.InstanceController

			if i, err = c.VerifyInstancesDependency(tt.args.ctx, tt.args.clusterID, tt.args.instanceSet); (err != nil) != tt.wantErr {
				t.Errorf("VerifyInstancesDependency() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantCancel == true {
				if tt.args.ctx.Err() == nil || tt.args.ctx.Err() != context.Canceled {
					t.Errorf("")
				}
			}
			if tt.wantIRet > 0 {
				if tt.wantIRet != i {
					t.Errorf("")
				}
			}
			tt.fields.InstanceController.cancel()
		})
	}
}

func Test_resetInstanceRetryCount(t *testing.T) {
	type args struct {
		ctx           context.Context
		clusterID     string
		cceInstanceID string
		metaClient    meta.Interface
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常更新",
			args: func() args {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
					},
					Status: ccetypes.InstanceStatus{
						RetryCount:    20,
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepCreateMachine: {
								Ready: true,
							},
							ccetypes.InstanceStepEnsureSecurityGroups: {
								Ready: true,
							},
							ccetypes.InstanceStepDeploy: {
								Ready: false,
							},
						},
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepDeleteMachine: {
								Ready: false,
							},
							ccetypes.NodeStepDeleteK8SNode: {
								Ready: true,
							},
						},
					},
				}

				updateInstance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
					},
					Status: ccetypes.InstanceStatus{
						RetryCount:    0,
						InstancePhase: ccetypes.InstancePhaseDeleting,
						ReconcileSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepCreateMachine: {
								Ready: true,
							},
							ccetypes.InstanceStepEnsureSecurityGroups: {
								Ready: true,
							},
						},
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.NodeStepDeleteK8SNode: {
								Ready: true,
							},
						},
					},
				}

				gomock.InOrder(
					metaClient.EXPECT().GetInstance(ctx, "default", "cce-instance-id", &metav1.GetOptions{}).Return(instance, nil),

					metaClient.EXPECT().UpdateInstanceStatus(ctx, "default", "cce-instance-id", &updateInstance.Status).Return(nil),
				)

				return args{
					ctx:           ctx,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					metaClient:    metaClient,
				}
			}(),
			wantErr: false,
		},
		{
			name: "RetryCount < 20 次",
			args: func() args {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				gomock.InOrder(
					metaClient.EXPECT().GetInstance(ctx, "default", "cce-instance-id", &metav1.GetOptions{}).Return(
						&ccev1.Instance{
							Spec: ccetypes.InstanceSpec{
								ClusterID: "cce-cluster",
							},
							Status: ccetypes.InstanceStatus{
								RetryCount: 10,
							},
						}, nil,
					),
				)

				return args{
					ctx:           ctx,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					metaClient:    metaClient,
				}
			}(),
			wantErr: false,
		},
		{
			name: "ClusterID 不匹配",
			args: func() args {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				gomock.InOrder(
					metaClient.EXPECT().GetInstance(ctx, "default", "cce-instance-id", &metav1.GetOptions{}).Return(
						&ccev1.Instance{
							Spec: ccetypes.InstanceSpec{
								ClusterID: "cce-othercluster",
							},
							Status: ccetypes.InstanceStatus{
								RetryCount: 20,
							},
						}, nil,
					),
				)

				return args{
					ctx:           ctx,
					clusterID:     "cce-cluster",
					cceInstanceID: "cce-instance-id",
					metaClient:    metaClient,
				}
			}(),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := resetInstanceRetryCount(tt.args.ctx, tt.args.clusterID, tt.args.cceInstanceID, tt.args.metaClient, false); (err != nil) != tt.wantErr {
				t.Errorf("resetInstanceRetryCount() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkInstancesCountBelowLimit(t *testing.T) {
	type args struct {
		ctx         context.Context
		instanceSet []*ccesdk.InstanceSet
		limit       int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "新建实例不超限",
			args: args{
				ctx: context.TODO(),
				instanceSet: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        10,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        5,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        2,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        10,
					},
				},
				limit: 50,
			},
			wantErr: false,
		},
		{
			name: "新建实例超限",
			args: args{
				ctx: context.TODO(),
				instanceSet: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        10,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        5,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        2,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
						Count:        10,
					},
				},
				limit: 20,
			},
			wantErr: true,
		},
		{
			name: "已有实例不超限",
			args: args{
				ctx: context.TODO(),
				instanceSet: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{},
					},
				},
				limit: 20,
			},
			wantErr: false,
		},
		{
			name: "已有实例超限",
			args: args{
				ctx: context.TODO(),
				instanceSet: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
						},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
						},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
						},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
						},
					},
				},
				limit: 2,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkInstancesCountBelowLimit(tt.args.ctx, tt.args.instanceSet, tt.args.limit); (err != nil) != tt.wantErr {
				t.Errorf("checkInstancesCountBelowLimit() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_UpdateInstance(t *testing.T) {

	var (
		ast         = assert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				models: &models.Client{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[interface{}]interface{}),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
		}
	)

	var instanceID = "i-123"
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		return instanceID
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	getInstanceErr := models.ErrNotExist.New(ctx, "instance not found")
	var instance = &models.Instance{
		Spec: &ccetypes.InstanceSpec{
			InstanceName: "cce-instance",
		},
	}
	patch2 := gomonkey.ApplyFunc((*models.Client).GetInstanceByCCEID, func(_ *models.Client, ctx context.Context, cceInstanceID string, accountID string) (*models.Instance, error) {
		return instance, getInstanceErr
	})
	defer patch2.Reset()

	patch3 := gomonkey.ApplyFunc((*models.Client).GetInstanceByBCCID, func(_ *models.Client, ctx context.Context, instanceID string, accountID string) (*models.Instance, error) {
		return nil, fmt.Errorf("test error")
	})
	defer patch3.Reset()

	updateDBErr := fmt.Errorf("update db error")
	patch4 := gomonkey.ApplyFunc((*models.Client).UpdatePartInstanceSpec, func(_ *models.Client, ctx context.Context, accountID string, cceInstanceID string, spec *ccetypes.InstanceSpec) error {
		return updateDBErr
	})
	defer patch4.Reset()

	patch5 := gomonkey.ApplyFunc((*InstanceController).GetInstance, func(_ *InstanceController) {})
	defer patch5.Reset()

	getInstanceCRErr := fmt.Errorf("get instance cr error")
	getInstancePatch := gomonkey.ApplyFunc((*k8s.Client).GetInstance, func(_ *k8s.Client, ctx context.Context, cceInstanceID string) (*ccev1.Instance, error) {
		return &ccev1.Instance{}, getInstanceCRErr
	})
	defer getInstancePatch.Reset()

	reconcileInstanceErr := fmt.Errorf("reconcile instance error")
	reconcileInstancePatch := gomonkey.ApplyFunc((*k8s.Client).ReconcileInstance, func(_ *k8s.Client, ctx context.Context, instance *models.Instance, accountID string) (*ccev1.Instance, error) {
		return nil, reconcileInstanceErr
	})
	defer reconcileInstancePatch.Reset()

	// body解析失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewMalformedJSON())
	c.Ctx.Input.RequestBody = []byte(`{"instance_name": "cce-instance"}`)

	// 查询bcc失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())
	instanceID = "cce-instance-id"

	// 查询cce失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceNotFound())
	getInstanceErr = nil

	// 获取 CRD失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceNotFound())
	getInstanceCRErr = nil

	// 更新DB失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())
	updateDBErr = nil

	// 更新CRD失败
	c.UpdateInstance()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())
	reconcileInstanceErr = nil

	// 更新实例成功
	errHandler.ErrorCode = nil
	c.UpdateInstance()
	ast.Nil(errHandler.ErrorCode)

	// 缩容保护更新成功
	// 传入InstanceTemplate，预检查失败
	req := ccesdk.InstanceSpec{
		InstanceName:      "node1",
		ScaleDownDisabled: &scaleDownDisabledFalse,
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.UpdateInstance()
	ast.Nil(errHandler.ErrorCode)
}

func TestInstanceController_GetInstanceByNodeName(t *testing.T) {
	type fields struct {
		BaseController BaseController
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "HostName模式成功获取实例",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				errHandler := errorcode.NewMockErrorHandler()

				return fields{BaseController: BaseController{
					Controller: beego.Controller{
						Ctx: &beegocontext.Context{
							Request: &http.Request{
								Method: "GET",
								URL:    &url.URL{Path: "/v1/clusters/test-cluster/instances/node/test-node"},
							},
							Input: &beegocontext.BeegoInput{},
						},
						Data: make(map[interface{}]interface{}),
					},
					ctx:        ctx,
					cancel:     cancel,
					errHandler: errHandler,
					models:     &models.Client{},
					accountID:  "test-account",
				}}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceController{
				BaseController: tt.fields.BaseController,
			}

			// Mock the path parameters
			patches := gomonkey.ApplyFunc((*beego.Controller).GetString, func(self *beego.Controller, key string, def ...string) string {
				switch key {
				case ":clusterID":
					return "test-cluster"
				case ":nodeName":
					return "test-node"
				}
				return ""
			})
			defer patches.Reset()

			// Mock ServeJSON to prevent actual HTTP response
			serveJSONPatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
			defer serveJSONPatch.Reset()

			// Mock models.GetCluster
			getClusterPatch := gomonkey.ApplyFunc((*models.Client).GetCluster, func(client *models.Client, ctx context.Context, clusterID string, accountID string) (*models.Cluster, error) {
				return &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil
			})
			defer getClusterPatch.Reset()

			// Mock models.GetInstanceByClusterIDAndHostname
			getInstancePatch := gomonkey.ApplyFunc((*models.Client).GetInstanceByClusterIDAndHostname, func(client *models.Client, ctx context.Context, clusterID string, hostname string) (*models.Instance, error) {
				return &models.Instance{
					Spec: &ccetypes.InstanceSpec{
						CCEInstanceID: "cce-test-instance",
						InstanceName:  "test-node",
					},
				}, nil
			})
			defer getInstancePatch.Reset()

			// Mock instancep.InstanceModelToSDK
			instanceSDKPatch := gomonkey.ApplyFunc(instancep.InstanceModelToSDK, func(ctx context.Context, cluster *models.Cluster, instance *models.Instance, enableInternalFields bool, enableK8SNodeDetail bool) (*ccesdk.Instance, error) {
				return &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						CCEInstanceID: "cce-test-instance",
					},
				}, nil
			})
			defer instanceSDKPatch.Reset()

			// Mock addInstancePropertiesNotInDB
			addPropPatch := gomonkey.ApplyFunc((*InstanceController).addInstancePropertiesNotInDB, func(c *InstanceController, ctx context.Context, cluster *models.Cluster, instance *ccesdk.Instance) error {
				return nil
			})
			defer addPropPatch.Reset()

			// Mock loadUserScript
			loadScriptPatch := gomonkey.ApplyFunc((*InstanceController).loadUserScript, func(c *InstanceController, ctx context.Context, instance *ccesdk.Instance) error {
				return nil
			})
			defer loadScriptPatch.Reset()

			c.GetInstanceByNodeName()
		})
	}
}

func TestInstanceController_addInstancePropertiesNotInDB(t *testing.T) {
	type args struct {
		ctx      context.Context
		cluster  *models.Cluster
		instance *ccesdk.Instance
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "instance为nil",
			args: args{
				ctx:      context.TODO(),
				cluster:  &models.Cluster{},
				instance: nil,
			},
			wantErr: true,
		},
		{
			name: "cluster为nil",
			args: args{
				ctx:     context.TODO(),
				cluster: nil,
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						CCEInstanceID: "cce-test-instance",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "获取实例失败",
			args: args{
				ctx: context.TODO(),
				cluster: &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterName: "test-cluster",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						CCEInstanceID: "cce-test-instance",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "成功添加属性",
			args: args{
				ctx: context.TODO(),
				cluster: &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterName: "test-cluster",
					},
				},
				instance: &ccesdk.Instance{
					Spec: &ccesdk.InstanceSpec{
						CCEInstanceID: "cce-test-instance",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceController{}

			// Mock GetInstance for cases that need it
			if tt.name == "获取实例失败" || tt.name == "成功添加属性" {
				getInstancePatch := gomonkey.ApplyFunc((*meta.Client).GetInstance, func(client *meta.Client, ctx context.Context, namespace string, name string, options *metav1.GetOptions) (*ccev1.Instance, error) {
					if tt.name == "获取实例失败" {
						return nil, fmt.Errorf("instance not found")
					}
					// 成功添加属性
					return &ccev1.Instance{
						Spec: ccetypes.InstanceSpec{
							NeedGPU:                       true,
							IsOpenHostnameDomain:          true,
							UserData:                      "test-userdata",
							EhcClusterID:                  "ehc-cluster-id",
							ScaleDownDisabled:             true,
							NvidiaContainerToolkitVersion: "1.0.0",
							AIInfraOption: ccetypes.AIInfraOption{
								TemplateID: "template-123",
							},
						},
					}, nil
				})
				defer getInstancePatch.Reset()

				// Set up clientSet for these test cases
				c.clientSet = &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: &meta.Client{},
					},
				}
			}

			if err := c.addInstancePropertiesNotInDB(tt.args.ctx, tt.args.cluster, tt.args.instance); (err != nil) != tt.wantErr {
				t.Errorf("addInstancePropertiesNotInDB() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_verifyEhcStockAvailable(t *testing.T) {
	type args struct {
		ctx       context.Context
		bccClient bcc.Interface
		stsClient sts.Interface
		nodeSpecs []*ccesdk.InstanceSet
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		want1   string
		want2   *bccapi.BccStock
		wantErr bool
	}{
		{
			name: "没有EHC集群节点",
			args: args{
				ctx: context.TODO(),
				nodeSpecs: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							AccountID: "test-account",
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "bcc.g1.c1m1",
							},
						},
						Count: 1,
					},
				},
			},
			want:    true,
			want1:   "",
			want2:   nil,
			wantErr: false,
		},
		{
			name: "EHC集群库存充足",
			args: args{
				ctx: context.TODO(),
				nodeSpecs: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							AccountID:    "test-account",
							EhcClusterID: "ehc-cluster-1",
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "bcc.g1.c1m1",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "cn-bj-a",
							},
						},
						Count: 2,
					},
				},
			},
			want:    true,
			want1:   "",
			want2:   nil,
			wantErr: false,
		},
		{
			name: "EHC集群库存不足",
			args: args{
				ctx: context.TODO(),
				nodeSpecs: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							AccountID:    "test-account",
							EhcClusterID: "ehc-cluster-1",
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "bcc.g1.c1m1",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "cn-bj-a",
							},
						},
						Count: 10,
					},
				},
			},
			want:    false,
			want1:   "ehc-cluster-1",
			want2:   &bccapi.BccStock{InventoryQuantity: 5},
			wantErr: false,
		},
		{
			name: "获取库存信息失败",
			args: args{
				ctx: context.TODO(),
				nodeSpecs: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							AccountID:    "test-account",
							EhcClusterID: "ehc-cluster-1",
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "bcc.g1.c1m1",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "cn-bj-a",
							},
						},
						Count: 2,
					},
				},
			},
			want:    false,
			want1:   "ehc-cluster-1",
			want2:   nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockBCCClient := bccmock.NewMockInterface(ctrl)
			mockSTSClient := stsmock.NewMockInterface(ctrl)

			if tt.name == "EHC集群库存充足" {
				stockResult := &bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{
						{
							ZoneName:          "cn-bj-a",
							InventoryQuantity: 10,
						},
					},
				}
				mockBCCClient.EXPECT().GetStockBySpecAndEhcClusterID(gomock.Any(), "bcc.g1.c1m1", "ehc-cluster-1", gomock.Any()).
					Return(stockResult, nil)
				mockSTSClient.EXPECT().NewSignOption(gomock.Any(), "test-account").Return(nil)
			} else if tt.name == "EHC集群库存不足" {
				stockResult := &bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{
						{
							ZoneName:          "cn-bj-a",
							InventoryQuantity: 5,
						},
					},
				}
				mockBCCClient.EXPECT().GetStockBySpecAndEhcClusterID(gomock.Any(), "bcc.g1.c1m1", "ehc-cluster-1", gomock.Any()).
					Return(stockResult, nil)
				mockSTSClient.EXPECT().NewSignOption(gomock.Any(), "test-account").Return(nil)
			} else if tt.name == "获取库存信息失败" {
				mockBCCClient.EXPECT().GetStockBySpecAndEhcClusterID(gomock.Any(), "bcc.g1.c1m1", "ehc-cluster-1", gomock.Any()).
					Return(nil, fmt.Errorf("get stock failed"))
				mockSTSClient.EXPECT().NewSignOption(gomock.Any(), "test-account").Return(nil)
			}

			// Mock checkEhcClusterStock function
			checkStockPatch := gomonkey.ApplyFunc(checkEhcClusterStock, func(ctx context.Context, stockResults *bccapi.GetStockWithSpecResults, zoneAndCount map[internalvpc.AvailableZone]int) (bool, *bccapi.BccStock, error) {
				if tt.name == "EHC集群库存充足" {
					return true, nil, nil
				} else if tt.name == "EHC集群库存不足" {
					return false, &bccapi.BccStock{InventoryQuantity: 5}, nil
				}
				return false, nil, nil
			})
			defer checkStockPatch.Reset()

			got, got1, got2, err := verifyEhcStockAvailable(tt.args.ctx, mockBCCClient, mockSTSClient, tt.args.nodeSpecs...)
			if (err != nil) != tt.wantErr {
				t.Errorf("verifyEhcStockAvailable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("verifyEhcStockAvailable() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("verifyEhcStockAvailable() got1 = %v, want %v", got1, tt.want1)
			}
			if tt.want2 != nil && got2 != nil {
				if got2.InventoryQuantity != tt.want2.InventoryQuantity {
					t.Errorf("verifyEhcStockAvailable() got2.InventoryQuantity = %v, want %v", got2.InventoryQuantity, tt.want2.InventoryQuantity)
				}
			} else if (got2 != nil) != (tt.want2 != nil) {
				t.Errorf("verifyEhcStockAvailable() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func Test_checkEhcClusterStock(t *testing.T) {
	type args struct {
		ctx          context.Context
		stockResults *bccapi.GetStockWithSpecResults
		zoneAndCount map[internalvpc.AvailableZone]int
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		want1   *bccapi.BccStock
		wantErr bool
	}{
		{
			name: "库存为空",
			args: args{
				ctx: context.TODO(),
				stockResults: &bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{},
				},
				zoneAndCount: map[internalvpc.AvailableZone]int{
					"cn-bj-a": 1,
				},
			},
			want:    false,
			want1:   nil,
			wantErr: false,
		},
		{
			name: "库存充足",
			args: args{
				ctx: context.TODO(),
				stockResults: &bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{
						{
							ZoneName:          "cn-bj-a",
							InventoryQuantity: 10,
						},
					},
				},
				zoneAndCount: map[internalvpc.AvailableZone]int{
					"cn-bj-a": 5,
				},
			},
			want:    true,
			want1:   nil,
			wantErr: false,
		},
		{
			name: "库存不足",
			args: args{
				ctx: context.TODO(),
				stockResults: &bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{
						{
							ZoneName:          "cn-bj-a",
							InventoryQuantity: 3,
						},
					},
				},
				zoneAndCount: map[internalvpc.AvailableZone]int{
					"cn-bj-a": 5,
				},
			},
			want: false,
			want1: &bccapi.BccStock{
				ZoneName:          "cn-bj-a",
				InventoryQuantity: 3,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock ccetypes.TransZoneNameToAvailableZone
			patches := gomonkey.ApplyFunc(ccetypes.TransZoneNameToAvailableZone, func(ctx context.Context, zoneName string) (internalvpc.AvailableZone, error) {
				return internalvpc.AvailableZone(zoneName), nil
			})
			defer patches.Reset()

			got, got1, err := checkEhcClusterStock(tt.args.ctx, tt.args.stockResults, tt.args.zoneAndCount)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkEhcClusterStock() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkEhcClusterStock() got = %v, want %v", got, tt.want)
			}
			if tt.want1 != nil && got1 != nil {
				if got1.InventoryQuantity != tt.want1.InventoryQuantity {
					t.Errorf("checkEhcClusterStock() got1.InventoryQuantity = %v, want %v", got1.InventoryQuantity, tt.want1.InventoryQuantity)
				}
			} else if (got1 != nil) != (tt.want1 != nil) {
				t.Errorf("checkEhcClusterStock() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

// TODO: Add TestInstanceController_loadUserScript test later - models.UserScript type issue

func Test_UpdateNodeScaleDown(t *testing.T) {
	type fields struct {
		BaseController BaseController
	}

	tests := []struct {
		name                               string
		fields                             fields
		setupMockErrorHandler              func(*gomock.Controller) errorcode.Interface
		setupMock                          func(*gomock.Controller, *InstanceController)
		setupInput                         func(*beegocontext.Context)
		getClientSetErr                    error
		newServiceErr                      error
		updateInstanceScaleDownDisabledErr error
		wantErr                            bool
		wantCode                           *errorcode.ErrorCode
	}{
		{
			name: "ClusterID为空",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "PUT",
					URL:    &url.URL{Path: "/v1/clusters//instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "")
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInvalidParam(),
		},
		{
			name: "请求体解析失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 设置非法JSON
				input.RequestBody = []byte(`{invalid-json`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewMalformedJSON(),
		},
		{
			name: "instanceIDs is empty",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 设置非法JSON
				input.RequestBody = []byte(`{"instanceIDs":[],"scaleDownDisabled":true}`)
				ctx.Input = input
			},
			wantErr:  false,
			wantCode: nil,
		},
		{
			name: "GetCluster failed",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu,
					fmt.Errorf("invalid clusterID")).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError("invalid clusterID"),
		},

		{
			name: "GetCluster cluster is nil",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError("invalid clusterID"),
		},

		{
			name: "getClientSet 报错",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:         true,
			wantCode:        errorcode.NewInternalServerError(""),
			getClientSetErr: fmt.Errorf("invalid error"),
		},
		{
			name: "newService error",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:       true,
			wantCode:      errorcode.NewInternalServerError(""),
			newServiceErr: fmt.Errorf("new service error"),
		},
		{
			name: "UpdateInstanceScaleDownDisabled 失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:                            true,
			wantCode:                           errorcode.NewInternalServerError(""),
			updateInstanceScaleDownDisabledErr: fmt.Errorf("invalid update error"),
		},
		{
			name: "无效节点",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			updateInstanceScaleDownDisabledErr: fmt.Errorf(" not belong to cluster cluste-1"),
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},

		{
			name: "更新成功",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				clu := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-cluster",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}
				mockModelClient := models.NewMockInterface(ctrl)
				mockModelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(clu, nil).AnyTimes()

				metaClient := pkgmetamock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
				c.models = mockModelClient

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instanceScaleDown"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"instanceIDs":["instance-1","instance-2"],"scaleDownDisabled":true}`)
				ctx.Input = input

			},
			wantErr:  false,
			wantCode: errorcode.NewInternalServerError(""),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			errHandler := tt.setupMockErrorHandler(ctrl)
			if !tt.wantErr {
				patch := gomonkey.ApplyFuncReturn((*InstanceController).ServeJSON)
				defer patch.Reset()
			}
			c := &InstanceController{
				BaseController: tt.fields.BaseController,
			}

			if tt.setupMock != nil {
				tt.setupMock(ctrl, c)
			}

			// 设置控制器的Ctx
			c.Controller.Ctx = &beegocontext.Context{}
			tt.setupInput(c.Controller.Ctx)

			// 设置错误处理器
			c.errHandler = errHandler

			mockInstance := instanceMock.NewMockInterface(ctrl)
			mockInstance.EXPECT().UpdateInstanceScaleDownDisabled(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(
				[]ccesdk.FailedInstance{
					{
						InstanceID: "instance-1",
						Reason:     "failed",
					},
				}, tt.updateInstanceScaleDownDisabledErr)

			patch := gomonkey.ApplyMethodReturn(&beego.Controller{}, "ServeJSON")
			patch.ApplyFuncReturn(getClientSet, nil, tt.getClientSetErr)
			patch.ApplyFuncReturn(instancep.NewService, mockInstance, tt.newServiceErr)
			defer patch.Reset()

			// 执行测试方法
			c.UpdateNodeScaleDown()
		})
	}
}

func Test_checkInstancesCount(t *testing.T) {
	type args struct {
		instanceSet []*ccesdk.InstanceSet
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{
			name: "正常",
			args: args{
				[]*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
						Count: 2,
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceName: "cce-instance2",
						},
						Count: 3,
					},
				},
			},
			want: 5,
		},
		{
			name: "正常",
			args: args{
				[]*ccesdk.InstanceSet{},
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := checkInstancesCount(tt.args.instanceSet); got != tt.want {
				t.Errorf("checkInstancesCount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInstanceController_checkCCENodeQuota(t *testing.T) {
	type fields struct {
		BaseController BaseController
	}
	type args struct {
		ctx     context.Context
		cluster *ccev1.Cluster
		addNum  int
	}

	cluster := &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			ClusterID: "ClusterID",
		},
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				quotaClient := quotamock.NewMockInterface(ctl)
				clusterQuota := &ccetypes.Quota{
					Quota: 200,
					Used:  40,
				}

				gomock.InOrder(
					quotaClient.EXPECT().GetNodeQuota(ctx, cluster.Spec.ClusterID).Return(clusterQuota, nil),
				)

				return fields{BaseController: BaseController{
					quotaClient: quotaClient,
					accountID:   "accountID",
				}}
			}(),
			args: args{
				ctx:     context.TODO(),
				cluster: cluster,
				addNum:  1,
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "GetNodeQuota return err",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				quotaClient := quotamock.NewMockInterface(ctl)
				clusterQuota := &ccetypes.Quota{
					Quota: 200,
					Used:  40,
				}

				gomock.InOrder(
					quotaClient.EXPECT().GetNodeQuota(ctx, cluster.Spec.ClusterID).Return(clusterQuota, fmt.Errorf("err")),
				)

				return fields{BaseController: BaseController{
					quotaClient: quotaClient,
					accountID:   "accountID",
				}}
			}(),
			args: args{
				ctx:     context.TODO(),
				cluster: cluster,
				addNum:  1,
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "cluster is nil",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				quotaClient := quotamock.NewMockInterface(ctl)
				clusterQuota := &ccetypes.Quota{
					Quota: 200,
					Used:  40,
				}

				gomock.InOrder(
					quotaClient.EXPECT().GetNodeQuota(ctx, cluster.Spec.ClusterID).Return(clusterQuota, nil),
				)

				return fields{BaseController: BaseController{
					quotaClient: quotaClient,
					accountID:   "accountID",
				}}
			}(),
			args: args{
				ctx:    context.TODO(),
				addNum: 1,
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceController{
				BaseController: tt.fields.BaseController,
			}
			got, err := c.checkCCENodeQuota(tt.args.ctx, tt.args.cluster, tt.args.addNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkCCENodeQuota() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("checkCCENodeQuota() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestInstanceController_VerifyEhcStockAvailable(t *testing.T) {
	ctx := context.TODO()

	type args struct {
		ctx       context.Context
		nodeSpec  []*ccesdk.InstanceSet
		bccClient bcc.Interface
		stsClient sts.Interface
	}
	tests := []struct {
		name        string
		args        args
		want        bool
		wantErr     bool
		wantCluster string
		wantStock   *bccapi.BccStock
	}{
		{
			name: "库存不足",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				bccStock := bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{{ZoneName: "cn-bj-a", InventoryQuantity: 2},
						{ZoneName: "cn-bj-f", InventoryQuantity: 2}},
				}
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(&bccStock, nil)
				return args{
					ctx:       ctx,
					bccClient: bccClient,
					stsClient: stsClient,
					nodeSpec: []*ccesdk.InstanceSet{{
						Count: 1,
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "zoneA",
							},
							EhcClusterID: "ehc-zf4u7Wrn",
						},
					},
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
								EhcClusterID: "ehc-zf4u7Wrn",
							},
						},
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
								EhcClusterID: "ehc-zf4u7Wrn",
							},
						},
					},
				}
			}(),
			want:        false,
			wantErr:     false,
			wantCluster: "ehc-zf4u7Wrn",
			wantStock:   &bccapi.BccStock{ZoneName: "cn-bj-f", InventoryQuantity: 2},
		},
		{
			name: "无对应可用区",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				bccStock := bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{{ZoneName: "cn-bj-a", InventoryQuantity: 2}},
				}
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(&bccStock, nil)
				return args{
					ctx:       ctx,
					bccClient: bccClient,
					stsClient: stsClient,
					nodeSpec: []*ccesdk.InstanceSet{
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
								EhcClusterID: "ehc-zf4u7Wrn",
							},
						},
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
								EhcClusterID: "ehc-zf4u7Wrn",
							},
						},
					},
				}
			}(),
			want:        false,
			wantErr:     false,
			wantCluster: "ehc-zf4u7Wrn",
			wantStock:   nil,
		},
		{
			name: "正常",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				bccStock := bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{{ZoneName: "cn-bj-a", InventoryQuantity: 2},
						{ZoneName: "cn-bj-f", InventoryQuantity: 5}},
				}
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(&bccStock, nil)
				return args{
					ctx:       ctx,
					bccClient: bccClient,
					stsClient: stsClient,
					nodeSpec: []*ccesdk.InstanceSet{{
						Count: 1,
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "zoneA",
							},
							EhcClusterID: "ehc-zf4u7Wrn",
						},
					},
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
								EhcClusterID: "ehc-zf4u7Wrn",
							},
						},
					},
				}
			}(),
			want:        true,
			wantErr:     false,
			wantCluster: "",
			wantStock:   nil,
		},
		{
			name: "无需校验",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				bccStock := bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{{ZoneName: "cn-bj-a", InventoryQuantity: 2},
						{ZoneName: "cn-bj-f", InventoryQuantity: 5}},
				}
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(&bccStock, nil)
				return args{
					ctx:       ctx,
					bccClient: bccClient,
					stsClient: stsClient,
					nodeSpec: []*ccesdk.InstanceSet{{
						Count: 1,
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "zoneA",
							},
						},
					},
						{
							Count: 3,
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceResource: ccetypes.InstanceResource{
									MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
								},
								VPCConfig: ccetypes.VPCConfig{
									AvailableZone: "zoneF",
								},
							},
						},
					},
				}
			}(),
			want:      true,
			wantErr:   false,
			wantStock: nil,
		},
		{
			name: "可用区错误",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				bccStock := bccapi.GetStockWithSpecResults{
					BccStocks: []bccapi.BccStock{{ZoneName: "cn-bj-z", InventoryQuantity: 2}},
				}
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(&bccStock, nil)
				return args{
					ctx: ctx,
					nodeSpec: []*ccesdk.InstanceSet{{
						Count: 1,
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "zoneA",
							},
							EhcClusterID: "ehc-zf4u7Wrn",
						},
					},
					},
					bccClient: bccClient,
					stsClient: stsClient,
				}
			}(),
			want:        false,
			wantErr:     true,
			wantCluster: "ehc-zf4u7Wrn",
			wantStock:   nil,
		},
		{
			name: "请求 bcc 错误",
			args: func() args {
				ctrl := gomock.NewController(t)
				bccClient := bccmock.NewMockInterface(ctrl)
				stsClient := stsmock.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
				bccClient.EXPECT().GetStockBySpecAndEhcClusterID(ctx, "ehc.lgn7s.c208m1024.8l20.4re.4d", "ehc-zf4u7Wrn", nil).Return(nil, errors.New("test error "))
				return args{
					ctx: ctx,
					nodeSpec: []*ccesdk.InstanceSet{{
						Count: 1,
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec: "ehc.lgn7s.c208m1024.8l20.4re.4d",
							},
							VPCConfig: ccetypes.VPCConfig{
								AvailableZone: "zoneA",
							},
							EhcClusterID: "ehc-zf4u7Wrn",
						},
					},
					},
					bccClient: bccClient,
					stsClient: stsClient,
				}
			}(),
			want:        false,
			wantErr:     true,
			wantCluster: "ehc-zf4u7Wrn",
			wantStock:   nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, clusterID, stock, err := verifyEhcStockAvailable(tt.args.ctx, tt.args.bccClient, tt.args.stsClient, tt.args.nodeSpec...)
			if (err != nil) != tt.wantErr {
				t.Errorf("verifyEhcStockAvailable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("verifyEhcStockAvailable() got = %v, want %v", got, tt.want)
			}
			if clusterID != tt.wantCluster {
				t.Errorf("verifyEhcStockAvailable() got = %v, want %v", clusterID, tt.wantCluster)
			}
			if !reflect.DeepEqual(stock, tt.wantStock) {
				t.Errorf("verifyEhcStockAvailable() got = %v, want %v", stock, tt.wantStock)
			}
		})
	}
}

// 需要在终端的痛目录下调用，go test -gcflags=-l -run=Test_CreateInstances
func Test_CreateInstances(t *testing.T) {

	var (
		ast         = assert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[interface{}]interface{}),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
		}
	)

	var clusterId string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		return clusterId
	})
	defer patch1.Reset()

	var checkQuotaErr = fmt.Errorf("check quota failed")
	patch2 := gomonkey.ApplyFunc(checkInstancesCountBelowLimit, func(ctx context.Context, instanceSet []*ccesdk.InstanceSet, limit int) error {
		return checkQuotaErr
	})
	defer patch2.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	var cluster *ccev1.Cluster
	var getClusterErr = fmt.Errorf("get cluster failed")
	patch3 := gomonkey.ApplyFunc((*k8s.Client).GetCluster, func(_ *k8s.Client, ctx context.Context, clusterID string) (*ccev1.Cluster, error) {
		return cluster, getClusterErr
	})
	defer patch3.Reset()

	var specCheckErr = fmt.Errorf("spec check failed")
	patch4 := gomonkey.ApplyFunc((*BaseController).UpdateInstanceSpecPreCheck, func(_ *BaseController, instanceSpec ccetypes.InstanceSpec) error {
		return specCheckErr
	})
	defer patch4.Reset()

	var checkSgErr = fmt.Errorf("check security group failed")
	limitSgErr := fmt.Errorf("security group limit")
	patch5 := gomonkey.ApplyFunc((*InstanceController).checkInstanceSecurityGroupCountLimit, func(_ *InstanceController, ctx context.Context, instanceSets []*ccesdk.InstanceSet, cluster *ccev1.Cluster) (error, error) {
		return checkSgErr, limitSgErr
	})
	defer patch5.Reset()

	var checkNodeAllow bool
	var checkNodeErr = fmt.Errorf("check node allow failed")
	patch6 := gomonkey.ApplyFunc(instancep.CheckCCENodeNumLimit, func(ctx context.Context, clusterID string, accountID string, addNum int, config *configuration.Config, models models.Interface) (bool, error) {
		return checkNodeAllow, checkNodeErr
	})
	defer patch6.Reset()

	var notSupportEniErr = fmt.Errorf("node not support eni")
	failEniErr := fmt.Errorf("check node eni failed")
	supportEniPatch := gomonkey.ApplyFunc((*services.Service).ValidateInstanceSetsSupportEni, func(_ *services.Service, ctx context.Context,
		clusterRole ccetypes.ClusterRole, clusterSpec *ccetypes.ClusterSpec, instances []*ccesdk.InstanceSet) (error, error) {
		return failEniErr, notSupportEniErr
	})
	defer supportEniPatch.Reset()

	var ensureErr = fmt.Errorf("ensure instance failed")
	ensureInstanceSetPatch := gomonkey.ApplyFunc((*services.Service).EnsureInstanceSets, func(_ *services.Service, ctx context.Context, clusterRole ccetypes.ClusterRole, clusterSpec *ccetypes.ClusterSpec, instances []*ccesdk.InstanceSet) error {
		return ensureErr
	})
	defer ensureInstanceSetPatch.Reset()

	var nodeCreateScriptErr = fmt.Errorf("create node script failed")
	createScriptPatch := gomonkey.ApplyFunc(userscript.CreateUserScripts, func(ctx context.Context, userScriptService userscript.Interface, instanceSpec *ccetypes.InstanceSpec) error {
		return nodeCreateScriptErr
	})
	defer createScriptPatch.Reset()

	var checkSpecErr = fmt.Errorf("check spec failed")
	checkSpecPatch := gomonkey.ApplyFunc((*BaseController).CheckMachineSpecs, func(_ *BaseController, ctx context.Context, specs []string) error {
		return checkSpecErr
	})
	defer checkSpecPatch.Reset()

	// 没传clusterID
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())

	// body unmarshal 失败
	clusterId = "123"
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewMalformedJSON())

	// 没有传入nodes
	errHandler.ErrorCode = nil
	c.Ctx.Input.RequestBody = []byte(`[]`)
	c.CreateInstances()
	ast.Nil(errHandler.ErrorCode)

	// 传入nodes，预检查失败
	req := []*ccesdk.InstanceSet{
		{InstanceSpec: ccetypes.InstanceSpec{
			InstanceName: "node1",
		}},
		{InstanceSpec: ccetypes.InstanceSpec{
			InstanceName: "node2",
		}},
		{InstanceSpec: ccetypes.InstanceSpec{
			InstanceName:    "node2",
			InstanceGroupID: "ig-1",
		}},
	}
	c.Ctx.Input.RequestBody, _ = json.Marshal(req)
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	ast.True(strings.Contains(errHandler.ErrorMsg, specCheckErr.Error()))

	// 批量操作过多
	specCheckErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstancesCountBeyondLimit())

	// 获取cluster失败
	checkQuotaErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())

	getClusterErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewNoSuchObject())

	// 检查安全组失败
	cluster = &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			MasterConfig: ccetypes.MasterConfig{
				MasterType: ccetypes.MasterTypeManagedPro,
			},
			ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
				Mode: ccetypes.ContainerNetworkModeVPCENI,
			},
		},
	}
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())

	limitSgErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())

	// 检查节点数量限制失败
	checkSgErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())

	checkNodeErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewCCEInstanceNumberExceed())

	// 检查节点数量限制通过，检查节点是否允许加入集群失败
	checkNodeAllow = true
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())
	ast.True(strings.Contains(errHandler.ErrorMsg, failEniErr.Error()))

	failEniErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewNotSupportEni())
	ast.True(strings.Contains(errHandler.ErrorMsg, notSupportEniErr.Error()))

	//  补全 NodeSpecs 字段失败
	notSupportEniErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidParam())
	ast.True(strings.Contains(errHandler.ErrorMsg, ensureErr.Error()))

	// CreateUserScripts失败
	ensureErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInternalServerError())
	ast.True(strings.Contains(errHandler.ErrorMsg, nodeCreateScriptErr.Error()))

	taintsErr := specCheckErr
	specCheckErr = models.ErrTaintsInvalid.New(ctx, "test")
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInstanceSpecPreCheckFailed())
	specCheckErr = taintsErr

	// CheckSpecs失败
	errHandler.ErrorCode = nil
	nodeCreateScriptErr = nil
	c.CreateInstances()
	ast.Equal(errHandler.ErrorCode, errorcode.NewInvalidMachineSpec())
}

// TODO 后续逻辑再补充单测
func TestCheckInstanceSecurityGroupCountLimit(t *testing.T) {
	type args struct {
		ctx          context.Context
		instanceSets []*ccesdk.InstanceSet
		cluster      *ccev1.Cluster
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				ctx: context.TODO(),
				instanceSets: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
							ExistedOption: ccetypes.ExistedOption{
								ExistedInstanceID: "cce-i-111",
							},
						},
					},
				},
				cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						AccountID: "account-id",
						VPCID:     "vpc-id",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test2",
			args: args{
				ctx: context.TODO(),
				instanceSets: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							Existed: true,
							ExistedOption: ccetypes.ExistedOption{
								ExistedInstanceID: "cce-i-111",
							},
							InstanceGroupID: "ig-1",
						},
					},
				},
				cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						AccountID: "account-id",
						VPCID:     "vpc-id",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctx := context.TODO()
			ctrl := gomock.NewController(t)

			k8sClient := metamock.NewMockInterface(ctrl)
			bccClient := bccmock.NewMockInterface(ctrl)
			stsClient := stsmock.NewMockInterface(ctrl)

			model := models.NewMockInterface(ctrl)

			ngs := &bccapi.ListSecurityGroupResult{
				SecurityGroups: []bccapi.SecurityGroupModel{
					{},
					{},
					{},
					{},
					{},
					{},
				},
			}

			egs := &esg.ListEsgResult{
				EnterpriseSecurityGroups: []esg.EnterpriseSecurityGroup{
					{},
					{},
					{},
					{},
					{},
					{},
				},
			}

			stsClient.EXPECT().NewSignOption(ctx, gomock.Any()).Return(nil).AnyTimes()
			bccClient.EXPECT().ListSecurityGroups(ctx, gomock.Any(), gomock.Any()).Return(ngs, nil)
			bccClient.EXPECT().ListEnterpriseSecurityGroup(ctx, gomock.Any(), gomock.Any()).Return(egs, nil)

			c := &InstanceController{
				BaseController: BaseController{
					Controller: beego.Controller{},
					ctx:        ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						BCCClient: bccClient,
						STSClient: stsClient,
					},
					config: &configuration.Config{
						ManagedClusterMinNodeNum: 3,
					},
					models:    model,
					accountID: "accountID",
				},
			}

			if failedErr, limitErr := c.checkInstanceSecurityGroupCountLimit(tt.args.ctx, tt.args.instanceSets, tt.args.cluster); limitErr != nil || failedErr != nil {
				fmt.Printf(limitErr.Error())
				assert.Equal(t, tt.wantErr, true)
			}
		})
	}
}
func TestRebuildInstance(t *testing.T) {
	type fields struct {
		ctx        context.Context
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		controller beego.Controller
		clients    *clients.Clients
		config     *configuration.Config
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "测试重建实例",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				ctx := context.TODO()

				// 模拟获取实例
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), "cce-instance", "account-id").Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						ClusterID:     "clusterID",
						CCEInstanceID: "cce-instance",
						ImageID:       "old-image-id",
					},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)

				// 模拟获取集群
				model.EXPECT().GetCluster(gomock.Any(), "clusterID", "account-id").Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clusterID",
						AccountID: "account-id",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}, nil)

				// 模拟获取K8S实例
				k8sClient.EXPECT().GetInstance(gomock.Any(), "cce-instance").Return(&ccev1.Instance{}, nil)

				// 模拟更新实例
				model.EXPECT().UpdatePartInstanceSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				// 模拟调和实例
				k8sClient.EXPECT().ReconcileInstance(gomock.Any(), gomock.Any(), "account-id").Return(&ccev1.Instance{}, nil)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}

				// 构造请求体
				body := ccesdk.RebuildInstanceRequest{
					ImageID: "new-image-id",
					ReInstallOsConfig: &ccesdk.ReInstallOsConfig{
						Enabled: false,
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}

				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
					},
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "POST",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "clusterID")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
					config:     &configuration.Config{},
				}
			}(),
		},
		{
			name: "测试重建实例失败-实例不存在",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				ctx := context.TODO()

				// 模拟获取集群
				model.EXPECT().GetCluster(gomock.Any(), "clusterID", "account-id").Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clusterID",
						AccountID: "account-id",
					},
					Status: &ccetypes.ClusterStatus{
						ClusterPhase: ccetypes.ClusterPhaseRunning,
					},
				}, nil)

				// 模拟获取实例失败
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), "cce-instance", "account-id").Return(nil, models.ErrNotExist)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}

				// 构造请求体
				body := ccesdk.RebuildInstanceRequest{
					ImageID: "new-image-id",
					ReInstallOsConfig: &ccesdk.ReInstallOsConfig{
						Enabled: true,
					},
				}

				requestBody, err := json.Marshal(body)
				if err != nil {
					t.Errorf(err.Error())
				}

				return fields{
					ctx: ctx,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "POST",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "clusterID")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 404,
					config:     &configuration.Config{},
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.clientSet.Model,
					clients:    tt.fields.clients,
					config:     tt.fields.config,
				},
			}

			ctx, cancel := context.WithTimeout(c.ctx, time.Second*10)
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()

			c.RebuildInstance()

			recorder := tt.fields.controller.Ctx.Output.Context.ResponseWriter.ResponseWriter.(*httptest.ResponseRecorder)
			if recorder.Code != tt.fields.statusCode {
				t.Errorf("RebuildInstance() 状态码 = %v, 期望 %v", recorder.Code, tt.fields.statusCode)
			}
		})
	}
}

func Test_SkipInstanceReconcileStep(t *testing.T) {
	type fields struct {
		BaseController BaseController
	}

	tests := []struct {
		name                  string
		fields                fields
		setupMockErrorHandler func(*gomock.Controller) errorcode.Interface
		setupMock             func(*gomock.Controller, *InstanceController)
		setupInput            func(*beegocontext.Context)
		wantErr               bool
		wantCode              *errorcode.ErrorCode
	}{
		{
			name: "ClusterID为空",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters//instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "")
				input.SetParam(":cceInstanceID", "cce-instance")
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInvalidParam(),
		},
		{
			name: "InstanceID为空",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				errHandler := errorcodemock.NewMockInterface(ctrl)
				errHandler.EXPECT().ErrorHandler(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return()
				return errHandler
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances//skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "")
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInvalidParam(),
		},
		{
			name: "请求体解析失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 设置非法JSON
				input.RequestBody = []byte(`{invalid-json`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewMalformedJSON(),
		},
		{
			name: "skipInstanceReconcileStep执行失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, fmt.Errorf("mock error"))
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"instance.deploy","phase":"create"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},
		{
			name: "skipInstanceReconcileStep执行失败， clusterID 不匹配",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster-1",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleting,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
					},
				}

				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil)
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"RebuildMachine", "clusterID": "cce-cluster", "cceInstanceID": "cce-instance"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},
		{
			name: "skipInstanceReconcileStep执行失败， instance phase 不是 Deleting",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
					},
				}

				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil)
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"RebuildMachine", "clusterID": "cce-cluster", "cceInstanceID": "cce-instance"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},

		{
			name: "skipInstanceReconcileStep执行失败， step 名称错误",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleting,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepDeliverPackage: ccetypes.Step{
								Ready: false,
							},
						},
					},
				}

				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil)
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"RebuildMachine", "clusterID": "cce-cluster", "cceInstanceID": "cce-instance"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},
		{
			name: "skipInstanceReconcileStep执行失败, updateInstanceStatus失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleting,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"RebuildMachine", "clusterID": "cce-cluster", "cceInstanceID": "cce-instance"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},
		{
			name: "skipInstanceReconcileStep执行失败, resetInstanceRetryCount 失败",
			fields: func() fields {
				ctx, cancel := context.WithCancel(context.TODO())
				return fields{
					BaseController: BaseController{
						ctx:    ctx,
						cancel: cancel,
					},
				}
			}(),
			setupMockErrorHandler: func(ctrl *gomock.Controller) errorcode.Interface {
				return errorcode.NewMockErrorHandler()
			},
			setupMock: func(ctrl *gomock.Controller, c *InstanceController) {
				ins := &ccev1.Instance{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "cce-instance",
						Labels: map[string]string{},
					},
					Spec: ccetypes.InstanceSpec{
						ClusterID: "cce-cluster",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							ExistedInstanceID: "cce-i-111",
						},
					},
					Status: ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseDeleteFailed,
						ReconcileDeleteSteps: map[ccetypes.StepName]ccetypes.Step{
							ccetypes.InstanceStepRebuildMachine: ccetypes.Step{
								Ready: false,
							},
						},
						RetryCount: 20,
					},
				}

				metaClient := pkgmetamock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(ins, nil).AnyTimes()
				metaClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil).AnyTimes()
				metaClient.EXPECT().UpdateInstanceStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fmt.Errorf("update instance status failed")).AnyTimes()
				clientSet := &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				}
				c.clientSet = clientSet

				c.Data = map[interface{}]interface{}{}
			},
			setupInput: func(ctx *beegocontext.Context) {
				ctx.Request = &http.Request{
					Method: "POST",
					URL:    &url.URL{Path: "/v1/clusters/cce-cluster/instances/cce-instance/skip-step"},
				}
				input := &beegocontext.BeegoInput{}
				input.Context = beegocontext.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.SetParam(":cceInstanceID", "cce-instance")
				// 有效的JSON请求体
				input.RequestBody = []byte(`{"step":"RebuildMachine", "clusterID": "cce-cluster", "cceInstanceID": "cce-instance"}`)
				ctx.Input = input
			},
			wantErr:  true,
			wantCode: errorcode.NewInternalServerError(""),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			errHandler := tt.setupMockErrorHandler(ctrl)

			c := &InstanceController{
				BaseController: tt.fields.BaseController,
			}

			if tt.setupMock != nil {
				tt.setupMock(ctrl, c)
			}

			// 设置控制器的Ctx
			c.Controller.Ctx = &beegocontext.Context{}
			tt.setupInput(c.Controller.Ctx)

			// 设置错误处理器
			c.errHandler = errHandler

			// 执行测试方法
			c.SkipInstanceReconcileStep()
		})
	}
}

func TestInstancesExport(t *testing.T) {
	testInfos := []struct {
		name          string
		ctx           context.Context
		accountID     string
		statusCode    int
		cluster       *models.Cluster
		getClusterErr error
		newServiceErr error
		controller    beego.Controller
		info          *ccesdk.InstancePage
		success       bool
	}{
		{
			name: "success",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
			},
			info: func() *ccesdk.InstancePage {
				instances := []*ccesdk.Instance{
					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "ig-1",
							InstanceGroupName:    "ig-test-name",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "bcc.gn5i.c4g4",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseRunning,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},
					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "ig-1",
							InstanceGroupName:    "ig-test-name",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "bcc.gn5i.c4g4",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseCreateFailed,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},

					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "ig-1",
							InstanceGroupName:    "ig-test-name",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "bcc.gn5i.c4g4",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseProvisioning,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},

					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "ig-1",
							InstanceGroupName:    "ig-test-name",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "bcc.gn5i.c4g4",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseUnknown,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},
					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "ig-1",
							InstanceGroupName:    "ig-test-name",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "bcc.gn5i.c4g4",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseDeleteFailed,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},
					{
						K8SNode: &node.NodeDetail{
							AllocatedResources: node.NodeAllocatedResources{
								CPURequestsFraction: 0.2339,
							},
						},
						CreatedAt: time.Now(),
						Spec: &ccesdk.InstanceSpec{
							InstanceGroupID:      "",
							InstanceName:         "nvidia-tool-1",
							CCEInstanceID:        "ins-1",
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							VPCConfig: ccesdk.VPCConfig{
								AvailableZone: "zoneA",
							},
							InstanceResource: ccetypes.InstanceResource{
								MachineSpec:  "",
								CPU:          4,
								MEM:          4,
								RootDiskSize: 100,
								GPUType:      "A800",
								GPUCount:     4,
							},
						},
						Status: &ccesdk.InstanceStatus{
							Machine: ccesdk.Machine{
								K8SNodeName: "k8s-name",
								VPCIP:       "*******",
							},
							InstancePhase: ccetypes.InstancePhaseDeleting,
							Resources: ccesdk.ResourceList{
								GPUCountRemaining: 1,
								GPUCountRequested: 3,
							},
						},
					},
				}
				return &ccesdk.InstancePage{
					InstanceList: instances,
				}
			}(),
			cluster: &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					ClusterName: "cluster-name",
				},
				Status: &ccetypes.ClusterStatus{
					ClusterPhase: ccetypes.ClusterPhaseRunning,
				},
			},
			statusCode: 200,
		},
		{
			name: "success but instance is empty",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
			},
			cluster: &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					ClusterName: "cluster-name",
				},
				Status: &ccetypes.ClusterStatus{
					ClusterPhase: ccetypes.ClusterPhaseRunning,
				},
			},
			statusCode: 200,
		},
		{
			name: "success but info.InstanceList is empty struct",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
			},
			cluster: &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					ClusterName: "cluster-name",
				},
				Status: &ccetypes.ClusterStatus{
					ClusterPhase: ccetypes.ClusterPhaseRunning,
				},
			},
			info: func() *ccesdk.InstancePage {
				return &ccesdk.InstancePage{
					InstanceList: []*ccesdk.Instance{
						{},
					},
				}
			}(),
			statusCode: 200,
		},

		{
			name: "failed. clusterID is empty",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						//input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			statusCode: 400,
		},
		{
			name: "failed. body is nil",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			statusCode: 400,
		},

		{
			name: "getCluster failed",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			getClusterErr: fmt.Errorf("get cluster failed"),
			statusCode:    400,
		},
		{
			name: "failed. cluster is nil",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			statusCode: 400,
		},
		{
			name: "failed. clusterPhase is not running",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			cluster: &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					ClusterName: "cluster-name",
				},
				Status: &ccetypes.ClusterStatus{
					ClusterPhase: ccetypes.ClusterPhaseProvisioning,
				},
			},
			statusCode: 400,
		},
		{
			name: "NewService failed",
			controller: beego.Controller{
				Ctx: &beegoctx.Context{
					Request: &http.Request{
						Method: "POST",
						URL: &url.URL{
							Path: "xxx",
						},
					},
					Input: func() *beegoctx.BeegoInput {
						body := &ccesdk.InstancesDownloadRequest{
							ExportAll:                  true,
							CCEInstanceIDs:             []string{"x-1", "x-2"},
							CalculateGPUCountRequested: true,
						}
						requestBody, _ := json.Marshal(body)

						input := &beegoctx.BeegoInput{}
						input.Context = beegoctx.NewContext()
						input.SetParam(":clusterID", "cce-cluster")
						input.Context.Request = &http.Request{}
						input.Context.Request.Header = make(http.Header)
						input.Context.Request.Header.Add("x-region", "bj")
						input.RequestBody = requestBody
						return input
					}(),

					Output: func() *beegoctx.BeegoOutput {
						output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
						output.Context.ResponseWriter = &beegoctx.Response{
							ResponseWriter: httptest.NewRecorder(),
						}
						output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
						output.Context.Request = &http.Request{}
						output.Context.Request.URL = &url.URL{
							Path: "xxxx",
						}

						output.Context.Request.Response = &http.Response{}
						output.Context.Request.Response.Header = make(http.Header)
						output.Context.Request.Response.Header.Add("Content-Type", "application/json")
						return output
					}(),
				},
				Data: make(map[interface{}]interface{}),
			},
			cluster: &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					ClusterName: "cluster-name",
				},
				Status: &ccetypes.ClusterStatus{
					ClusterPhase: ccetypes.ClusterPhaseProvisioning,
				},
			},
			newServiceErr: fmt.Errorf("newServiceErr"),
			statusCode:    400,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			modelMock := models.NewMockInterface(ctrl)
			modelMock.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.cluster, tt.getClusterErr)

			modelMock.EXPECT().GetAdminKubeConfigCompatibility(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&models.KubeConfig{
				KubeConfigFile: "kube-config-1",
			}, nil)

			mockInstance := instanceMock.NewMockInterface(ctrl)
			var mockInstanceRes instancep.Interface
			mockInstance.EXPECT().WithResourceCache(gomock.Any()).AnyTimes().Return(mockInstanceRes)
			mockInstance.EXPECT().ListInstancesByInstancesDownloadRequest(gomock.Any(), gomock.Any(), gomock.Any(),
				gomock.Any()).AnyTimes().Return(tt.info, nil)

			patches := gomonkey.ApplyFuncReturn(instancep.NewService, mockInstance, tt.newServiceErr)
			patches.ApplyFuncReturn(clientcmd.RESTConfigFromKubeConfig, &restclient.Config{}, nil)
			patches.ApplyFuncReturn(kubernetes.NewForConfig, nil, nil)
			defer patches.Reset()
			errHandler := errorcode.NewMockErrorHandler()
			ctx, cancel := context.WithTimeout(context.TODO(), 0)
			c := InstanceController{
				BaseController: BaseController{
					ctx:        ctx,
					Controller: tt.controller,
					models:     modelMock,
					errHandler: errHandler,
					accountID:  "acc-1",
					clientSet:  &clientset.ClientSet{},
					clients:    nil,
				},
			}

			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.InstancesDownload()
			if c.Ctx.Output.Context.ResponseWriter.Status != tt.statusCode {
				t.Errorf("error")
			}
		})
	}

	// 获取节点信息
	controller := beego.Controller{
		Ctx: &beegoctx.Context{
			Request: &http.Request{
				Method: "POST",
				URL: &url.URL{
					Path: "xxx",
				},
			},
			Input: func() *beegoctx.BeegoInput {
				body := &ccesdk.InstancesDownloadRequest{
					ExportAll:                  true,
					CCEInstanceIDs:             []string{"x-1", "x-2"},
					CalculateGPUCountRequested: true,
				}
				requestBody, _ := json.Marshal(body)

				input := &beegoctx.BeegoInput{}
				input.Context = beegoctx.NewContext()
				input.SetParam(":clusterID", "cce-cluster")
				input.Context.Request = &http.Request{}
				input.Context.Request.Header = make(http.Header)
				input.Context.Request.Header.Add("x-region", "bj")
				input.RequestBody = requestBody
				return input
			}(),

			Output: func() *beegoctx.BeegoOutput {
				output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
				output.Context.ResponseWriter = &beegoctx.Response{
					ResponseWriter: httptest.NewRecorder(),
				}
				output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
				output.Context.Request = &http.Request{}
				output.Context.Request.URL = &url.URL{
					Path: "xxxx",
				}

				output.Context.Request.Response = &http.Response{}
				output.Context.Request.Response.Header = make(http.Header)
				output.Context.Request.Response.Header.Add("Content-Type", "application/json")
				return output
			}(),
		},
		Data: make(map[interface{}]interface{}),
	}

	ctrl := gomock.NewController(t)
	modelMock := models.NewMockInterface(ctrl)
	modelMock.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterName: "cluster-name",
		},
		Status: &ccetypes.ClusterStatus{
			ClusterPhase: ccetypes.ClusterPhaseRunning,
		},
	}, nil)

	modelMock.EXPECT().GetAdminKubeConfigCompatibility(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&models.KubeConfig{
		KubeConfigFile: "kube-config-1",
	}, nil)

	var k8sErr = fmt.Errorf("k8sErr")
	patch1 := gomonkey.ApplyFunc(getClientSet, func(ctx context.Context, clusterID string, model models.Interface) (kubernetes.Interface, error) {
		return &kubernetes.Clientset{}, k8sErr
	})
	defer patch1.Reset()

	instancePage := &ccesdk.InstancePage{
		InstanceList: []*ccesdk.Instance{
			{
				Spec: &ccesdk.InstanceSpec{
					CCEInstanceID: "x-1",
					InstanceName:  "x-1",
				},
			},
		},
	}
	instanceListErr := fmt.Errorf("list instance err")
	mockInstance := instanceMock.NewMockInterface(ctrl)
	// var mockInstanceRes instancep.Interface
	mockInstance.EXPECT().WithResourceCache(gomock.Any()).AnyTimes().Return(mockInstance)
	mockInstance.EXPECT().ListInstancesByInstancesDownloadRequest(gomock.Any(), gomock.Any(), gomock.Any(),
		gomock.Any()).AnyTimes().Return(instancePage, instanceListErr)

	// patch2 := gomonkey.ApplyFunc(ListInstancesByInstancesDownloadRequest, func(ctx context.Context, req ccesdk.InstancesDownloadRequest, clusterID string, accountID string) (*ccesdk.InstancePage, error))

	var (
		csvWriteErr       = fmt.Errorf("csv write err")
		csvWriteHeaderErr = fmt.Errorf("csv write header err")
	)
	patch2 := gomonkey.ApplyFunc((*csv.Writer).Write, func(_ *csv.Writer, record []string) error {
		if strings.Contains(strings.Join(record, " "), "支付方式") {
			return csvWriteHeaderErr
		}
		return csvWriteErr
	})
	defer patch2.Reset()

	var csvErr = fmt.Errorf("csv err")
	patch3 := gomonkey.ApplyFunc((*csv.Writer).Error, func(_ *csv.Writer) error {
		return csvErr
	})
	defer patch3.Reset()

	var (
		instanceServiceGetErr = fmt.Errorf("mock err")
		instanceService       = mockInstance
	)
	patch4 := gomonkey.ApplyFunc(instancep.NewService, func(ctx context.Context, accountID string, config *configuration.Config, clients *clients.Clients, models models.Interface, services services.Interface, k8sClient kubernetes.Interface) (instancep.Interface, error) {
		return instanceService, instanceServiceGetErr
	})
	defer patch4.Reset()

	patch5 := gomonkey.ApplyFunc(convertInfosToCSV, func(ctx context.Context, infos *ccesdk.InstancePage) [][]string {
		return [][]string{{"aa"}}
	})
	defer patch5.Reset()

	errHandler := errorcode.NewMockErrorHandler()
	ctx, cancel := context.WithTimeout(context.TODO(), 0)
	c := InstanceController{
		BaseController: BaseController{
			ctx:        ctx,
			Controller: controller,
			models:     modelMock,
			errHandler: errHandler,
			accountID:  "acc-1",
			clientSet:  &clientset.ClientSet{},
			clients:    nil,
			cancel:     cancel,
		},
	}

	// k8s client获取失败
	c.InstancesDownload()
	assert.Equal(t, errHandler.ErrorMsg, k8sErr.Error())

	// 初始化instance interface失败
	k8sErr = nil
	c.InstancesDownload()
	assert.Equal(t, errHandler.ErrorMsg, instanceServiceGetErr.Error())

	// 查询节点列表失败
	instanceServiceGetErr = nil
	c.InstancesDownload()
	assert.Equal(t, errHandler.ErrorMsg, instanceListErr.Error())

	// 写csv表头失败
	mockInstance1 := instanceMock.NewMockInterface(ctrl)
	// var mockInstanceRes instancep.Interface
	mockInstance1.EXPECT().WithResourceCache(gomock.Any()).AnyTimes().Return(mockInstance1)
	mockInstance1.EXPECT().ListInstancesByInstancesDownloadRequest(gomock.Any(), gomock.Any(), gomock.Any(),
		gomock.Any()).AnyTimes().Return(instancePage, nil)
	instanceService = mockInstance1
	c.InstancesDownload()
	assert.True(t, strings.Contains(errHandler.ErrorMsg, csvWriteHeaderErr.Error()))

	// 写csv失败
	csvWriteHeaderErr = nil
	c.InstancesDownload()
	assert.True(t, strings.Contains(errHandler.ErrorMsg, csvWriteErr.Error()))

	// 写csv失败
	csvWriteErr = nil
	c.InstancesDownload()
	assert.True(t, strings.Contains(errHandler.ErrorMsg, csvErr.Error()))

	// 成功
	csvErr = nil
	errHandler.ErrorMsg = ""
	c.InstancesDownload()
	assert.Equal(t, errHandler.ErrorMsg, "")
}

func Test_convertInfosToCSV(t *testing.T) {
	type args struct {
		ctx   context.Context
		infos *ccesdk.InstancePage
	}
	tests := []struct {
		name string
		args args
		want [][]string
	}{
		{
			name: "正常",
			args: args{
				ctx: context.Background(),
				infos: &ccesdk.InstancePage{
					InstanceList: []*ccesdk.Instance{
						{
							Spec: &ccesdk.InstanceSpec{
								InstanceName:         "cce-instance",
								CCEInstanceID:        "cce-instance-id",
								InstanceChargingType: bcc.PaymentTimingPrepaid,
								VPCConfig: ccesdk.VPCConfig{
									AvailableZone: "zoneA",
								},
								InstanceResource: ccetypes.InstanceResource{
									CPU:          2,
									MEM:          4,
									RootDiskSize: 50,
									MachineSpec:  "bcc.c5.large",
									GPUType:      "T4",
									GPUCount:     1,
								},
								InstanceGroupID:   "group-id",
								InstanceGroupName: "group-name",
							},
							Status: &ccesdk.InstanceStatus{
								Machine: ccesdk.Machine{
									K8SNodeName: "k8s-node",
									VPCIP:       "********",
								},
								InstancePhase: ccetypes.InstancePhaseRunning,
								Resources: ccesdk.ResourceList{
									GPUCountRequested: 1,
									GPUCountRemaining: 0,
								},
							},
							K8SNode: &node.NodeDetail{
								AllocatedResources: node.NodeAllocatedResources{
									CPURequestsFraction:    5,
									CPULimitsFraction:      1.0,
									MemoryRequestsFraction: 5,
									MemoryLimitsFraction:   1.0,
									AllocatedPods:          10,
									PodCapacity:            20,
								},
							},
							CreatedAt: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
						},
					},
				},
			},
			want: [][]string{
				{
					"k8s-node",
					"cce-instance",
					"cce-instance-id",
					"可用",
					"预付费",
					"可用区A",
					"********",
					"2核/4GB/50GB/c5",
					"T4",
					`"1/1"`,
					"0",
					"CPU: 5.00% | 1.00% \n内存: 5.00% | 1.00%",
					`"10/20"`,
					"group-name（group-id）",
					"2023-01-01 08:00:00",
				},
			},
		},
		{
			name: "非标机型+无节点组",
			args: args{
				ctx: context.Background(),
				infos: &ccesdk.InstancePage{
					InstanceList: []*ccesdk.Instance{
						{
							Spec: &ccesdk.InstanceSpec{
								InstanceName:         "cce-instance",
								CCEInstanceID:        "cce-instance-id",
								InstanceChargingType: bcc.PaymentTimingPrepaid,
								VPCConfig: ccesdk.VPCConfig{
									AvailableZone: "zoneA",
								},
								InstanceResource: ccetypes.InstanceResource{
									CPU:          2,
									MEM:          4,
									RootDiskSize: 50,
									MachineSpec:  "c5",
									GPUType:      "T4",
									GPUCount:     1,
								},
								InstanceGroupName: "group-name",
							},
							Status: &ccesdk.InstanceStatus{
								Machine: ccesdk.Machine{
									K8SNodeName: "k8s-node",
									VPCIP:       "********",
								},
								InstancePhase: ccetypes.InstancePhaseRunning,
								Resources: ccesdk.ResourceList{
									GPUCountRequested: 1,
									GPUCountRemaining: 0,
								},
							},
							CreatedAt: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
						},
					},
				},
			},
			want: [][]string{
				{
					"k8s-node",
					"cce-instance",
					"cce-instance-id",
					"可用",
					"预付费",
					"可用区A",
					"********",
					"2核/4GB/50GB",
					"T4",
					`"1/1"`,
					"0",
					"CPU: - | - \n内存: - | -",
					"- / -",
					"",
					"2023-01-01 08:00:00",
				},
			},
		},
		{
			name: "空数据",
			args: args{
				ctx:   context.Background(),
				infos: &ccesdk.InstancePage{},
			},
			want: [][]string{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertInfosToCSV(tt.args.ctx, tt.args.infos)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertInfosToCSV() = %v, want %v", got, tt.want)
			}
			if len(got) > 0 && len(got[0]) != len(instanceDownloadCSVHeader) {
				t.Errorf("convertInfosToCSV with wrong data length")
			}
		})
	}
}

func Test_ListInstancesByInstanceGroupID(t *testing.T) {
	var (
		ast         = assert.New(t)
		errHandler  = errorcode.NewMockErrorHandler()
		ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
		c           = &InstanceController{
			BaseController: BaseController{
				ctx:        ctx,
				cancel:     cancel,
				errHandler: errHandler,
				config:     &configuration.Config{},
				clients: &clients.Clients{
					K8SClient: &k8s.Client{},
				},
				models: &models.Client{},
				Controller: beego.Controller{
					Ctx: &beegocontext.Context{
						Request: &http.Request{
							Method: "GET",
							URL:    &url.URL{Path: "/v1"},
							Header: make(http.Header),
						},
						Input: &beegocontext.BeegoInput{},
					},
					Data: make(map[interface{}]interface{}),
				},
				service:     &services.Service{},
				specService: &speccheck.SpecClient{},
			},
		}
	)

	var clusterID, instanceGroupID string
	patch1 := gomonkey.ApplyFunc((*BaseController).GetPathParams, func(_ *BaseController, key string) string {
		if strings.Contains(key, "cluster") {
			return clusterID
		}

		if strings.Contains(key, "instanceGroup") {
			return instanceGroupID
		}
		return ""
	})
	defer patch1.Reset()

	servePatch := gomonkey.ApplyFunc((*beego.Controller).ServeJSON, func(self *beego.Controller, encoding ...bool) {})
	defer servePatch.Reset()

	var cluster *models.Cluster
	var getClusterErr = fmt.Errorf("get cluster failed")
	patch3 := gomonkey.ApplyFunc((*models.Client).GetCluster, func(_ *models.Client, ctx context.Context, clusterID string, accountID string) (*models.Cluster, error) {
		return cluster, getClusterErr
	})
	defer patch3.Reset()

	getPageNoErr := fmt.Errorf("get pageNo failed")
	pageNo, pageSize := -1, -1
	getPageSizeErr := fmt.Errorf("get pageSize failed")
	getIntPatch := gomonkey.ApplyFunc((*beego.Controller).GetInt, func(_ *beego.Controller, key string, def ...int) (int, error) {
		if strings.Contains(key, "pageNo") {
			return pageNo, getPageNoErr
		}

		if strings.Contains(key, "pageSize") {
			return pageSize, getPageSizeErr
		}
		return 0, nil
	})
	defer getIntPatch.Reset()

	getStringPatch := gomonkey.ApplyFunc((*beego.Controller).GetString, func(_ *beego.Controller, key string, def ...string) string {
		if strings.Contains(key, "keywordType") {
			return "k8sNodeName"
		}

		return ""
	})
	defer getStringPatch.Reset()

	enableUpgradeNodeError := fmt.Errorf("enableUpgradeNode failed")
	getBoolPatch := gomonkey.ApplyFunc((*beego.Controller).GetBool, func(_ *beego.Controller, key string, def ...bool) (bool, error) {
		if strings.Contains(key, "enableUpgradeNodeFields") {
			return false, enableUpgradeNodeError
		}
		return false, fmt.Errorf("get bool failed")
	})
	defer getBoolPatch.Reset()

	getClientsErr := fmt.Errorf("get clients err")
	patch4 := gomonkey.ApplyFunc(getClientSet, func(ctx context.Context, clusterID string, model models.Interface) (kubernetes.Interface, error) {
		return nil, getClientsErr
	})
	defer patch4.Reset()

	getServiceErr := fmt.Errorf("get service err")
	ctrl := gomock.NewController(t)
	mockInstance := instanceMock.NewMockInterface(ctrl)
	// var mockInstanceRes instancep.Interface
	mockInstance.EXPECT().WithResourceCache(gomock.Any()).AnyTimes().Return(mockInstance)
	mockInstance.EXPECT().ListInstancesByInstanceGroup(gomock.Any(), gomock.Any()).AnyTimes().Return(&ccesdk.ListInstancesByInstanceGroupIDPage{}, fmt.Errorf("mock list err"))
	patch5 := gomonkey.ApplyFunc(instancep.NewService, func(ctx context.Context, accountID string, config *configuration.Config, clients *clients.Clients, models models.Interface, services services.Interface, k8sClient kubernetes.Interface) (instancep.Interface, error) {
		return mockInstance, getServiceErr
	})
	defer patch5.Reset()

	// clusterID为空
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 节点组 ID为空
	clusterID = "cluster-id"
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 获取页码失败
	instanceGroupID = "instance-group-id"
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 页码<0
	getPageNoErr = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 获取分页大小失败
	pageNo = 1
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// pageSize<0
	getPageSizeErr = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// 获取enableUpgradeNodeFields失败
	pageSize = 10
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInvalidParam().Code)

	// get cluster失败
	enableUpgradeNodeError = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// cluster为空
	getClusterErr = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 获取clients失败
	cluster = &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "cluster-id",
		},
		Status: &ccetypes.ClusterStatus{
			ClusterPhase: ccetypes.ClusterPhaseRunning,
		},
	}
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 获取service失败
	getClientsErr = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// List失败
	getServiceErr = nil
	c.ListInstancesByInstanceGroupID()
	ast.Equal(errHandler.ErrorCode.Code, errorcode.NewInternalServerError().Code)

	// 成功
	ctrl1 := gomock.NewController(t)
	mockInstance = instanceMock.NewMockInterface(ctrl1)
	mockInstance.EXPECT().WithResourceCache(gomock.Any()).AnyTimes().Return(mockInstance)
	mockInstance.EXPECT().ListInstancesByInstanceGroup(gomock.Any(), gomock.Any()).AnyTimes().Return(&ccesdk.ListInstancesByInstanceGroupIDPage{}, nil)
	patch5 = gomonkey.ApplyFunc(instancep.NewService, func(ctx context.Context, accountID string, config *configuration.Config, clients *clients.Clients, models models.Interface, services services.Interface, k8sClient kubernetes.Interface) (instancep.Interface, error) {
		return mockInstance, nil
	})
	defer patch5.Reset()
	errHandler.ErrorCode = nil
	c.ListInstancesByInstanceGroupID()
	ast.Nil(errHandler.ErrorCode)
}

func Test_ListInstancesWithVolumes(t *testing.T) {

	ctx := context.Background()

	type fields struct {
		ctx context.Context

		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		controller beego.Controller
		clients    *clients.Clients
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						body := map[string]string{
							"instanceID1": "zoneF",
						}

						requestBody, err := json.Marshal(body)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						body := map[string]string{
							"instanceID1": "zoneF",
						}

						requestBody, err := json.Marshal(body)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":instanceID", "")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						requestBody, err := json.Marshal(nil)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, nil),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						body := map[string]string{
							"instanceID1":  "zoneF",
							"instanceID2":  "zoneF",
							"instanceID3":  "zoneF",
							"instanceID4":  "zoneF",
							"instanceID5":  "zoneF",
							"instanceID6":  "zoneF",
							"instanceID7":  "zoneF",
							"instanceID9":  "zoneF",
							"instanceID10": "zoneF",
							"instanceID11": "zoneF",
							"instanceID12": "zoneF",
							"instanceID13": "zoneF",
							"instanceID14": "zoneF",
							"instanceID15": "zoneF",
							"instanceID16": "zoneF",
							"instanceID17": "zoneF",
							"instanceID18": "zoneF",
							"instanceID19": "zoneF",
							"instanceID20": "zoneF",
							"instanceID21": "zoneF",
							"instanceID22": "zoneF",
							"instanceID23": "zoneF",
							"instanceID24": "zoneF",
							"instanceID25": "zoneF",
							"instanceID26": "zoneF",
							"instanceID27": "zoneF",
							"instanceID28": "zoneF",
							"instanceID29": "zoneF",
							"instanceID30": "zoneF",
							"instanceID31": "zoneF",
							"instanceID32": "zoneF",
							"instanceID33": "zoneF",
							"instanceID34": "zoneF",
							"instanceID35": "zoneF",
							"instanceID36": "zoneF",
							"instanceID37": "zoneF",
							"instanceID38": "zoneF",
							"instanceID39": "zoneF",
							"instanceID40": "zoneF",
							"instanceID41": "zoneF",
							"instanceID42": "zoneF",
							"instanceID43": "zoneF",
							"instanceID44": "zoneF",
							"instanceID45": "zoneF",
							"instanceID46": "zoneF",
							"instanceID47": "zoneF",
							"instanceID48": "zoneF",
							"instanceID49": "zoneF",
							"instanceID50": "zoneF",
							"instanceID51": "zoneF",
							"instanceID52": "zoneF",
						}

						requestBody, err := json.Marshal(body)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, fmt.Errorf("error")),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						body := map[string]string{
							"instanceID1": "zoneF",
						}

						requestBody, err := json.Marshal(body)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
		{
			name: "test update instance",
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				k8sClient := metamock.NewMockInterface(ctl)
				bccClient := bccmock.NewMockInterface(ctl)
				stsClient := stsmock.NewMockInterface(ctl)
				ctx := ctx
				gomock.InOrder(
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes(),
					bccClient.EXPECT().ListVolumes(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&bcc.ListVolumesResponse{
						Volumes: []bcc.Volume{
							{
								ID:           "volumeID",
								Name:         "test",
								DiskSizeInGB: 60,
								Attachments: []bcc.VolumeAttachment{{
									VolumeID:   "",
									InstanceID: "",
									Device:     "",
								},
								},
							},
						},
					}, fmt.Errorf("error")),
				)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model: model,
					},
				}
				return fields{
					ctx: ctx,
					clients: &clients.Clients{
						K8SClient: k8sClient,
						STSClient: stsClient,
						BCCClient: bccClient,
					},
					controller: func() beego.Controller {
						body := []string{
							"zoneF",
						}

						requestBody, err := json.Marshal(body)
						if err != nil {
							t.Errorf(err.Error())
						}
						return beego.Controller{
							Data: make(map[interface{}]interface{}),
							Ctx: &beegoctx.Context{
								Request: &http.Request{
									Method: "PUT",
									URL: &url.URL{
										Path: "xxx",
									},
								},
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetParam(":instanceID", "cce-instance")
									input.SetData("RequestID", "xxxx")
									input.RequestBody = requestBody

									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}

									//httptest.NewRecorder()
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")
									//output.Context.ResponseWriter.ResponseWriter = beegoctx.NewContext().ResponseWriter
									// output.Context.ResponseWriter = beegoctx.NewContext().ResponseWriter

									output.Context.Request = &http.Request{}
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.StatusCode = 200

									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),

					clientSet:  client,
					accountID:  "account-id",
					statusCode: 200,
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := InstanceController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					ctx:        tt.fields.ctx,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
					models:     tt.fields.clientSet.Model,
					clients:    tt.fields.clients,
					userID:     tt.fields.accountID,
				},
			}
			ctx, cancel := context.WithTimeout(c.ctx, 0)
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			c.Ctx = c.BaseController.Ctx
			defer recoverUserStop()
			c.ListInstancesWithVolumes()
			if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
				t.Errorf("error")
			}
		})
	}
}

func TestInstanceController_getVKNodeDetail(t *testing.T) {
	type fields struct {
		BaseController       BaseController
		instanceGroupService instancegroup.Interface
	}
	type args struct {
		ctx       context.Context
		clusterID string
		nodeName  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: func() fields {
				mocksts := stsmock.NewMockInterface(gomock.NewController(t))
				mocksts.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				return fields{
					BaseController: BaseController{
						clients: &clients.Clients{
							APPServiceClient: &appservice.Client{
								Client: &bce.Client{
									Config: &bce.Config{},
								},
							},
							STSClient: mocksts,
						},
					},
				}
			}(),
			args: args{
				ctx:       context.Background(),
				clusterID: "cce-cluster",
				nodeName:  "test",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &InstanceController{
				BaseController:       tt.fields.BaseController,
				instanceGroupService: tt.fields.instanceGroupService,
			}
			_, err := c.getVKNodeDetail(tt.args.ctx, tt.args.clusterID, tt.args.nodeName)
			assert.Equalf(t, tt.wantErr, err != nil, "getVKNodeDetail(%v, %v, %v)", tt.args.ctx, tt.args.clusterID, tt.args.nodeName)
		})
	}
}

func TestAddInstancePropertiesNotInDB_XPUPropagate_Independent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	meta := pkgmetamock.NewMockInterface(ctrl)
	cluster := &models.Cluster{Spec: &ccetypes.ClusterSpec{ClusterName: "c1"}}
	metaIns := &ccev1.Instance{Spec: ccetypes.InstanceSpec{XPUContainerToolkitVersion: "1.0.5", ScaleDownDisabled: true}}
	meta.EXPECT().GetInstance(gomock.Any(), "default", "cce-id", gomock.Any()).Return(metaIns, nil)

	c := &InstanceController{BaseController: BaseController{clientSet: &clientset.ClientSet{Clients: &clientset.Clients{MetaClient: meta}}}}
	inst := &ccesdk.Instance{Spec: &ccesdk.InstanceSpec{CCEInstanceID: "cce-id"}}
	if err := c.addInstancePropertiesNotInDB(context.TODO(), cluster, inst); err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if inst.Spec.XPUContainerToolkitVersion != "1.0.5" {
		t.Fatalf("expected XPU version copied from meta, got %q", inst.Spec.XPUContainerToolkitVersion)
	}
	if inst.Spec.ScaleDownDisabled == nil || *inst.Spec.ScaleDownDisabled != true {
		t.Fatalf("expected ScaleDownDisabled=true copied from meta")
	}
}

func TestAddInstancePropertiesNotInDB_ReturnsErrorOnNilInstance(t *testing.T) {
	c := &InstanceController{}
	cluster := &models.Cluster{Spec: &ccetypes.ClusterSpec{ClusterName: "c"}}
	if err := c.addInstancePropertiesNotInDB(context.TODO(), cluster, nil); err == nil {
		t.Fatalf("expected error when instance is nil")
	}
}

func TestAddInstancePropertiesNotInDB_ReturnsErrorOnNilCluster(t *testing.T) {
	c := &InstanceController{}
	inst := &ccesdk.Instance{Spec: &ccesdk.InstanceSpec{CCEInstanceID: "id"}}
	if err := c.addInstancePropertiesNotInDB(context.TODO(), nil, inst); err == nil {
		t.Fatalf("expected error when cluster is nil")
	}
}

// TestListInstancesByPage_ChargingTypeFilter - 测试计费方式筛选功能
func TestListInstancesByPage_ChargingTypeFilter(t *testing.T) {
	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 0)
	defer cancel()
	ctx = context.WithValue(ctx, "RequestID", "xxxx")

	type fields struct {
		ctx        context.Context
		controller beego.Controller
		clientSet  *clientset.ClientSet
		accountID  string
		statusCode int
		service    instancep.Interface
		errHandler errorcode.Interface
		models     models.Interface
		k8sClient  kubernetes.Interface
	}
	type args struct {
		ctx          context.Context
		clusterID    string
		chargingType string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.InstancePage
		wantErr bool
	}{
		{
			name: "Given_ValidPrepaidChargingType_When_ListInstancesByPage_Then_Success",
			args: args{
				ctx:          ctx,
				clusterID:    "cce-cluster",
				chargingType: "Prepaid",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := pkgmetamock.NewMockInterface(ctl)

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "cce-cluster", "account-id").Return(&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "clusterID",
						},
						Status: &ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
				)

				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 0,
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetData("RequestID", "xxxx")
									input.SetParam("orderBy", "instanceName")
									input.SetParam("order", "ASC")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									// 🎯 关键：设置完整的HTTP请求上下文，确保c.GetString()能获取到参数
									req, _ := http.NewRequest("GET", "/api/instances?chargingType=Prepaid", nil)
									input.Context.Request = req
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									// 🎯 关键：设置完整的HTTP请求上下文
									req, _ := http.NewRequest("GET", "/api/instances?chargingType=Prepaid", nil)
									output.Context.Request = req
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancep.Interface {
						mockService := instanceMock.NewMockInterface(ctl)
						chargingTypePtr := "Prepaid"
						mockService.EXPECT().ListInstancesByPage(ctx, "cce-cluster", "instanceName", "", "instanceName", "ASC", "", 1, 10, false, ccetypes.ClusterRole(""), "", false, []string(nil), false, "", false, &chargingTypePtr).Return(&ccesdk.InstancePage{
							ClusterID:    "cce-cluster",
							TotalCount:   1,
							InstanceList: []*ccesdk.Instance{},
						}, nil)
						return mockService
					}(),
				}
			}(),
			want:    nil,
			wantErr: false,
		},
		{
			name: "Given_InvalidChargingType_When_ListInstancesByPage_Then_Error",
			args: args{
				ctx:          ctx,
				clusterID:    "cce-cluster",
				chargingType: "InvalidType",
			},
			fields: func() fields {
				ctl := gomock.NewController(t)
				model := models.NewMockInterface(ctl)
				metaClient := pkgmetamock.NewMockInterface(ctl)
				client := &clientset.ClientSet{
					Clients: &clientset.Clients{
						Model:      model,
						MetaClient: metaClient,
					},
				}
				return fields{
					ctx:        ctx,
					accountID:  "account-id",
					statusCode: 400, // 期望返回400错误
					controller: func() beego.Controller {
						return beego.Controller{
							Data: make(map[any]any),
							Ctx: &beegoctx.Context{
								Input: func() *beegoctx.BeegoInput {
									input := &beegoctx.BeegoInput{}
									input.Context = beegoctx.NewContext()
									input.SetParam(":clusterID", "cce-cluster")
									input.SetData("RequestID", "xxxx")
									input.SetParam("orderBy", "instanceName")
									input.SetParam("order", "ASC")
									input.SetParam("pageNo", "1")
									input.SetParam("pageSize", "10")

									// 🎯 关键：设置完整的HTTP请求上下文，确保c.GetString()能获取到参数
									req, _ := http.NewRequest("GET", "/api/instances?chargingType=InvalidType", nil)
									input.Context.Request = req
									return input
								}(),

								Output: func() *beegoctx.BeegoOutput {
									output := &beegoctx.BeegoOutput{Context: beegoctx.NewContext()}
									output.Context.ResponseWriter = &beegoctx.Response{
										ResponseWriter: httptest.NewRecorder(),
									}
									output.Context.ResponseWriter.Header().Add("Content-Type", "application/json")

									// 🎯 关键：设置完整的HTTP请求上下文
									req, _ := http.NewRequest("GET", "/api/instances?chargingType=InvalidType", nil)
									output.Context.Request = req
									output.Context.Request.Response = &http.Response{}
									output.Context.Request.Response.Header = make(http.Header)
									output.Context.Request.Response.Header.Add("Content-Type", "application/json")
									return output
								}(),
							},
						}
					}(),
					clientSet: client,
					models:    model,
					service: func() instancep.Interface {
						mockService := instanceMock.NewMockInterface(ctl)
						// 不设置期望调用，因为应该在参数校验阶段就返回错误
						return mockService
					}(),
				}
			}(),
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 使用gomonkey mock NewService函数
			patches := gomonkey.ApplyFunc(instancep.NewService, func(ctx context.Context, accountID string, config *configuration.Config, clients *clients.Clients, models models.Interface, service services.Interface, k8sClient kubernetes.Interface) (instancep.Interface, error) {
				return tt.fields.service, nil
			})
			defer patches.Reset()

			c := &InstanceController{
				BaseController: BaseController{
					Controller: tt.fields.controller,
					clientSet:  tt.fields.clientSet,
					accountID:  tt.fields.accountID,
				},
			}
			c.errHandler = errorcode.NewErrorHandler(ctx, c.Controller)
			c.cancel = cancel
			defer recoverUserStop()
			c.ListInstancesByPage()

			// 验证状态码
			if tt.fields.statusCode != 0 {
				if c.Ctx.Output.Context.Request.Response.StatusCode != tt.fields.statusCode {
					t.Errorf("Expected status code %d, got %d", tt.fields.statusCode, c.Ctx.Output.Context.Request.Response.StatusCode)
				}
			}
		})
	}
}
