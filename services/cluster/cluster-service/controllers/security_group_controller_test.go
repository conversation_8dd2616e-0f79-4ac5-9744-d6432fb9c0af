// Copyright 2024 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.

package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/astaxie/beego"
	beegocontext "github.com/astaxie/beego/context"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	bccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	bcests "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	vpcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	k8smock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/k8s/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
)

// fakeSTSClient implements sts.Interface for testing
type fakeSTSClient struct{}

func (f *fakeSTSClient) SetDebug(debug bool) {}

func (f *fakeSTSClient) GetCredential(ctx context.Context, accountID string) (*bcests.Credential, error) {
	return &bcests.Credential{}, nil
}

func (f *fakeSTSClient) NewSignOption(ctx context.Context, accountID string) *bce.SignOption {
	return &bce.SignOption{}
}

func (f *fakeSTSClient) NewUserSignOption(ctx context.Context, info sts.CredentialInfo) *bce.SignOption {
	return &bce.SignOption{}
}

func (f *fakeSTSClient) NewSignOptionWithResourceAK(ctx context.Context, userID, resourceAK, resourceSK, resourceAccountID, resourceSource string) *bce.SignOption {
	return &bce.SignOption{}
}

func (f *fakeSTSClient) NewSignOptionWithResourceHeader(ctx context.Context, userID, hexKey, resourceAccountID, resourceSource string) *bce.SignOption {
	return &bce.SignOption{}
}

func (f *fakeSTSClient) NewSignOptionWithBCCResourceAK(ctx context.Context, userID, resourceAccountID, paasApplication, paasAccesskey string) *bce.SignOption {
	return &bce.SignOption{}
}

func (f *fakeSTSClient) GetEndpoint(ctx context.Context, accountID, region, serviceType string) (string, error) {
	return "", nil
}

func (f *fakeSTSClient) GetCredentialFromCache(ctx context.Context, accountID string) (*bcests.Credential, error) {
	return &bcests.Credential{}, nil
}

func (f *fakeSTSClient) UpdateCredentialInCache(ctx context.Context, accountID string, cred *bcests.Credential) error {
	return nil
}

// fakeBCCClient implements bcc.Interface for testing
type fakeBCCClient struct{}

func (f *fakeBCCClient) SetDebug(debug bool) {}

// TODO: 重新设计这个测试，因为CheckSecurityGroupCIDRs方法现在使用集群CR而不是数据库
// 需要mock K8SClient.GetCluster方法
/*
func TestSecurityGroupController_CheckSecurityGroupCIDRs(t *testing.T) {
	// 创建 gomock 控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建 mock 客户端
	mockBCCClient := bccmock.NewMockInterface(ctrl)
	mockVPCClient := vpcmock.NewMockInterface(ctrl)
	mockSTSClient := &fakeSTSClient{}

	// 设置 BCC mock 期望
	ctx := context.Background()

	// 为 VPC 设置 GetSecurityGroups 的期望返回值，包含所有安全组
	mockBCCClient.EXPECT().GetSecurityGroups(ctx, &bcc.GetSecurityGroupsRequest{
		VPCID: "vpc-test",
	}, gomock.Any()).Return(&bcc.GetSecurityGroupsResponse{
		SecurityGroups: []bcc.SecurityGroup{
			{
				ID:    "sg-eni-123",
				Name:  "test-eni-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "22",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
			{
				ID:    "sg-worker-456",
				Name:  "test-worker-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "80",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
			{
				ID:    "sg-master-789",
				Name:  "test-master-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "6443",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
		},
	}, nil).AnyTimes()

	// 为错误场景设置 GetSecurityGroups 失败的期望
	mockBCCClient.EXPECT().GetSecurityGroups(ctx, &bcc.GetSecurityGroupsRequest{
		VPCID: "vpc-error",
	}, gomock.Any()).Return(nil, fmt.Errorf("BCC API error")).AnyTimes()

	// 为 CheckSecurityGroupCIDRs 方法失败场景设置期望
	// 让 GetSecurityGroups 返回 nil response，这会导致后续处理出错
	mockBCCClient.EXPECT().GetSecurityGroups(ctx, &bcc.GetSecurityGroupsRequest{
		VPCID: "vpc-nil-response",
	}, gomock.Any()).Return(nil, nil).AnyTimes()

	// 为所有场景添加 ListEnterpriseSecurityGroup 的mock期望
	mockBCCClient.EXPECT().ListEnterpriseSecurityGroup(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	// 创建基础的 beego controller context
	beegoController := beego.Controller{
		Ctx: &beegocontext.Context{
			Input: &beegocontext.BeegoInput{
				Context: &beegocontext.Context{
					Request: &http.Request{
						URL: &url.URL{
							Path: "/test",
						},
					},
				},
			},
			Request: &http.Request{
				Method: http.MethodPost,
				URL: &url.URL{
					Path: "/test",
				},
			},
			ResponseWriter: &beegocontext.Response{
				ResponseWriter: &httptest.ResponseRecorder{
					HeaderMap: http.Header{},
				},
			},
			Output: &beegocontext.BeegoOutput{
				Context: &beegocontext.Context{
					ResponseWriter: &beegocontext.Response{
						ResponseWriter: &httptest.ResponseRecorder{},
					},
				},
			},
		},
		Data: map[interface{}]interface{}{},
	}

	bc := &BaseController{
		accountID: "test-account-id",
		clients: &clients.Clients{
			STSClient: mockSTSClient,
			BCCClient: mockBCCClient,
			VPCClient: mockVPCClient,
		},
		ctx:        ctx,
		errHandler: errorcode.NewErrorHandler(ctx, beegoController),
		Controller: beegoController,
		config: &configuration.Config{
			Region: "bj",
			ClientConfig: &clients.Config{
				Region: "bj",
			},
			SupportedOSVersions:       []string{"7.3"},
			SupportedOSVersionsForARM: []string{"7.3"},
			SupportedOSVersionsInWL:   []string{"7.3"},
			SupportedK8SVersions:      []string{"1.20.0"},
			AvailableK8SVersions:      []string{"1.20.0"},
			K8SVersionsForServerless:  []string{"1.20.0"},
			K8SVersionsForCloudEdge:   []string{"1.20.0"},
			K8SVersionsForARM:         []string{"1.20.0"},
			UnSupportedInstanceTypes:  []logicbcc.InstanceType{logicbcc.InstanceTypeN1},
			CCEManagedMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			CCECustomMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			CCENodeIPv4SGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 22, PortRangeMax: 22, SourceIP: "0.0.0.0/0"},
			},
			CCENodeIPv6SGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 22, PortRangeMax: 22, SourceIP: "::/0"},
			},
			CCEServerlessMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			DefaultServerlessMasterConfig: configuration.DefaultServerlessMasterConfig{
				Containers: ccetypes.ServerlessMasterContainers{
					APIServer: ccetypes.Container{
						Name:  "apiserver",
						Image: "test-image",
					},
					ControllerManager: ccetypes.Container{
						Name:  "controller-manager",
						Image: "test-image",
					},
					Scheduler: ccetypes.Container{
						Name:  "scheduler",
						Image: "test-image",
					},
					VirtualKubelet: ccetypes.Container{
						Name:  "virtual-kubelet",
						Image: "test-image",
					},
					Debug: ccetypes.Container{
						Name:  "debug",
						Image: "test-image",
					},
				},
			},
			ServerlessMasterServiceController: &ccetypes.Container{
				Name:  "service-controller",
				Image: "test-image",
			},
			ServerlessMasterBECServiceController: &ccetypes.Container{
				Name:  "bec-service-controller",
				Image: "test-image",
			},
			ServerlessMasterNoClusterIPAPIServer: ccetypes.Container{
				Name:  "apiserver-no-clusterip",
				Image: "test-image",
			},
			ServerlessMasterBECVirtualKubelet: ccetypes.Container{
				Name:  "bec-virtual-kubelet",
				Image: "test-image",
			},
		},
		clientSet: &clientset.ClientSet{
			Clients: &clientset.Clients{
				MetaClient: &meta.Client{},
			},
		},
		cancel: func() {},
	}

	sgController := &SecurityGroupController{
		BaseController: *bc,
	}

	// 设置 mock
	modelClient := models.NewMockInterface(ctrl)
	cluster := &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "cce-test-cluster",
			VPCID:     "vpc-test",
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-123"},
			},
			NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-worker-456"},
			},
			MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-master-789"},
			},
		},
	}
	// 创建一个会导致 CheckSecurityGroupCIDR 失败的集群
	errorCluster := &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "error-cluster",
			VPCID:     "vpc-error", // 这个 VPCID 会导致 BCC API 调用失败
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-123"},
			},
		},
	}

	// 创建一个会导致参数验证失败的集群（空的安全组ID）
	invalidSGCluster := &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "invalid-sg-cluster",
			VPCID:     "vpc-test",
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: ""}, // 空的安全组ID会导致参数验证失败
			},
		},
	}

	// 创建一个会导致 GetSecurityGroups 返回 nil response 的集群
	nilResponseCluster := &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "nil-response-cluster",
			VPCID:     "vpc-nil-response", // 这个 VPCID 会导致 GetSecurityGroups 返回 nil response
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-123"},
			},
		},
	}

	// 创建一个用于测试安全组验证失败的集群
	sgValidationFailCluster := &models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			ClusterID: "sg-validation-fail-cluster",
			VPCID:     "vpc-test",
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-real"}, // 与请求中的安全组不匹配
			},
			NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-worker-real"}, // 与请求中的安全组不匹配
			},
			MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-master-real"}, // 与请求中的安全组不匹配
			},
		},
	}

	// 设置不同场景的 mock 期望
	modelClient.EXPECT().GetCluster(gomock.Any(), "cce-test-cluster", gomock.Any()).Return(cluster, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "error-cluster", gomock.Any()).Return(errorCluster, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "invalid-sg-cluster", gomock.Any()).Return(invalidSGCluster, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "nil-response-cluster", gomock.Any()).Return(nilResponseCluster, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "sg-validation-fail-cluster", gomock.Any()).Return(sgValidationFailCluster, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "non-existent-cluster", gomock.Any()).Return(nil, fmt.Errorf("cluster not found")).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "nil-cluster", gomock.Any()).Return(nil, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "nil-spec-cluster", gomock.Any()).Return(&models.Cluster{Spec: nil}, nil).AnyTimes()
	modelClient.EXPECT().GetCluster(gomock.Any(), "", gomock.Any()).Return(nil, fmt.Errorf("clusterID is empty")).AnyTimes()
	sgController.BaseController.models = modelClient

	type args struct {
		reqBytes  []byte
		accountID string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "正常流程",
			args: func() args {
				req := &ccesdk.CheckSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs:     []string{"********/24"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "无效JSON格式",
			args: args{
				reqBytes:  []byte(`{"clusterID": "test-cluster", "cidrs": ["********/24"]`), // 缺少闭合的大括号
				accountID: "test-account-id",
			},
		},
		{
			name: "clusterID is empty (panic)",
			args: func() args {
				req := &ccesdk.CheckSecurityGroupCIDRRequest{
					ClusterID: "",
					CIDRs:     []string{"********/24"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "CIDRs is empty (panic)",
			args: func() args {
				req := &ccesdk.CheckSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs:     []string{},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "CIDR contains empty string (panic)",
			args: func() args {
				req := &ccesdk.CheckSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs:     []string{"********/24", ""},
				}
				reqBytes, _ := json.Marshal(req)
				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "accountID is empty (panic)",
			args: func() args {
				req := &ccesdk.CheckSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs:     []string{"********/24"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "",
				}
			}(),
		},
		{
			name: "models.GetCluster returns error",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "non-existent-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "test-account-id",
			},
		},
		{
			name: "cluster is nil",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "nil-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "test-account-id",
			},
		},
		{
			name: "cluster.Spec is nil",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "nil-spec-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "test-account-id",
			},
		},
		{
			name: "CheckSecurityGroupCIDR fails",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "error-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "test-account-id",
			},
		},
		{
			name: "securityGroupClient is nil",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "cce-test-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "", // 空的 accountID 会导致 securitygroup.NewClient 失败
			},
		},
		{
			name: "GetSecurityGroups returns nil response",
			args: args{
				reqBytes: func() []byte {
					req := &ccesdk.CheckSecurityGroupCIDRRequest{
						ClusterID: "nil-response-cluster",
						CIDRs:     []string{"********/24"},
					}
					reqBytes, _ := json.Marshal(req)
					return reqBytes
				}(),
				accountID: "test-account-id",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					t.Logf("recover from panic: %v", r)
				}
			}()
			sgController.Ctx.Input.RequestBody = tt.args.reqBytes
			sgController.BaseController.accountID = tt.args.accountID
			sgController.CheckSecurityGroupCIDRs()
		})
	}
}
*/

func TestSecurityGroupController_AddSecurityGroupCIDRs(t *testing.T) {
	// 创建 gomock 控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建 mock 客户端
	mockBCCClient := bccmock.NewMockInterface(ctrl)
	mockVPCClient := vpcmock.NewMockInterface(ctrl)
	mockSTSClient := &fakeSTSClient{}
	mockK8SClient := k8smock.NewMockInterface(ctrl)

	// 设置 BCC mock 期望 - 为 GetSecurityGroups 调用
	ctx := context.Background()

	// 为 VPC 设置 GetSecurityGroups 的期望返回值，包含所有安全组
	mockBCCClient.EXPECT().GetSecurityGroups(ctx, &bcc.GetSecurityGroupsRequest{
		VPCID: "vpc-test",
	}, gomock.Any()).Return(&bcc.GetSecurityGroupsResponse{
		SecurityGroups: []bcc.SecurityGroup{
			{
				ID:    "sg-eni-123",
				Name:  "test-eni-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "22",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
			{
				ID:    "sg-worker-456",
				Name:  "test-worker-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "80",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
			{
				ID:    "sg-master-789",
				Name:  "test-master-sg",
				VPCID: "vpc-test",
				Rules: []bcc.SecurityGroupRule{
					{
						Direction:     "ingress",
						Protocol:      "tcp",
						PortRange:     "6443",
						SourceIP:      "10.0.0.0/16",
						SourceGroupID: "",
					},
				},
			},
		},
	}, nil).AnyTimes()

	// 设置 VPC mock 期望 - 为 DescribeSubnet 调用
	mockVPCClient.EXPECT().DescribeSubnet(ctx, "subnet-123", gomock.Any()).Return(&vpc.Subnet{
		SubnetID: "subnet-123",
		CIDR:     "********/24",
		VPCID:    "vpc-test",
	}, nil).AnyTimes()

	// 设置不匹配的子网 mock 期望
	mockVPCClient.EXPECT().DescribeSubnet(ctx, "subnet-mismatch", gomock.Any()).Return(&vpc.Subnet{
		SubnetID: "subnet-mismatch",
		CIDR:     "********/24", // 与请求的 ********/24 不匹配
		VPCID:    "vpc-test",
	}, nil).AnyTimes()

	// 为所有场景添加 ListEnterpriseSecurityGroup 的mock期望
	mockBCCClient.EXPECT().ListEnterpriseSecurityGroup(ctx, gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

	// 设置 BCC mock 期望 - 为 CreateSecurityGroupRule 调用
	mockBCCClient.EXPECT().CreateSecurityGroupRule(ctx, gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	// 创建基础的 beego controller context
	beegoController := beego.Controller{
		Ctx: &beegocontext.Context{
			Input: &beegocontext.BeegoInput{
				Context: &beegocontext.Context{
					Request: &http.Request{
						URL: &url.URL{
							Path: "/test",
						},
					},
				},
			},
			Request: &http.Request{
				Method: http.MethodPost,
			},
			ResponseWriter: &beegocontext.Response{
				ResponseWriter: &httptest.ResponseRecorder{
					HeaderMap: http.Header{},
				},
			},
			Output: &beegocontext.BeegoOutput{
				Context: &beegocontext.Context{
					ResponseWriter: &beegocontext.Response{
						ResponseWriter: &httptest.ResponseRecorder{},
					},
				},
			},
		},
		Data: map[interface{}]interface{}{},
	}

	bc := &BaseController{
		accountID: "test-account-id",
		clients: &clients.Clients{
			STSClient: mockSTSClient,
			BCCClient: mockBCCClient,
			VPCClient: mockVPCClient,
			K8SClient: mockK8SClient,
		},
		ctx:        ctx,
		errHandler: errorcode.NewErrorHandler(ctx, beegoController),
		Controller: beegoController,
		config: &configuration.Config{
			Region: "bj",
			ClientConfig: &clients.Config{
				Region: "bj",
			},
			SupportedOSVersions:       []string{"7.3"},
			SupportedOSVersionsForARM: []string{"7.3"},
			SupportedOSVersionsInWL:   []string{"7.3"},
			SupportedK8SVersions:      []string{"1.20.0"},
			AvailableK8SVersions:      []string{"1.20.0"},
			K8SVersionsForServerless:  []string{"1.20.0"},
			K8SVersionsForCloudEdge:   []string{"1.20.0"},
			K8SVersionsForARM:         []string{"1.20.0"},
			UnSupportedInstanceTypes:  []logicbcc.InstanceType{logicbcc.InstanceTypeN1},
			CCEManagedMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			CCECustomMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			CCENodeIPv4SGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 22, PortRangeMax: 22, SourceIP: "0.0.0.0/0"},
			},
			CCENodeIPv6SGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 22, PortRangeMax: 22, SourceIP: "::/0"},
			},
			CCEServerlessMasterSGRules: []ccetypes.SecurityGroupRule{
				{Protocol: "tcp", PortRangeMin: 6443, PortRangeMax: 6443, SourceIP: "0.0.0.0/0"},
			},
			DefaultServerlessMasterConfig: configuration.DefaultServerlessMasterConfig{
				Containers: ccetypes.ServerlessMasterContainers{
					APIServer: ccetypes.Container{
						Name:  "apiserver",
						Image: "test-image",
					},
					ControllerManager: ccetypes.Container{
						Name:  "controller-manager",
						Image: "test-image",
					},
					Scheduler: ccetypes.Container{
						Name:  "scheduler",
						Image: "test-image",
					},
					VirtualKubelet: ccetypes.Container{
						Name:  "virtual-kubelet",
						Image: "test-image",
					},
					Debug: ccetypes.Container{
						Name:  "debug",
						Image: "test-image",
					},
				},
			},
			ServerlessMasterNoClusterIPAPIServer: ccetypes.Container{
				Name:  "apiserver-no-clusterip",
				Image: "test-image",
			},
			ServerlessMasterBECVirtualKubelet: ccetypes.Container{
				Name:  "bec-virtual-kubelet",
				Image: "test-image",
			},
			ServerlessMasterServiceController: &ccetypes.Container{
				Name:  "service-controller",
				Image: "test-image",
			},
			ServerlessMasterBECServiceController: &ccetypes.Container{
				Name:  "bec-service-controller",
				Image: "test-image",
			},
		},
		clientSet: &clientset.ClientSet{
			Clients: &clientset.Clients{
				MetaClient: &meta.Client{},
			},
		},
		cancel: func() {},
	}

	sgController := &SecurityGroupController{
		BaseController: *bc,
	}

	// 创建 ccev1.Cluster 数据用于 K8SClient Mock
	clusterCR := &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			ClusterID: "cce-test-cluster",
			VPCID:     "vpc-test",
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-123"},
			},
			NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-worker-456"},
			},
			MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-master-789"},
			},
		},
	}

	// 创建用于测试安全组验证失败的 ccev1.Cluster 数据
	sgValidationFailClusterCR := &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			ClusterID: "sg-validation-fail-cluster",
			VPCID:     "vpc-test",
			ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-eni-real"}, // 与请求中的安全组不匹配
			},
			NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-worker-real"}, // 与请求中的安全组不匹配
			},
			MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
				{ID: "sg-master-real"}, // 与请求中的安全组不匹配
			},
		},
	}

	// 设置 K8SClient 的 mock 期望
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "cce-test-cluster").Return(clusterCR, nil).AnyTimes()
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "sg-validation-fail-cluster").Return(sgValidationFailClusterCR, nil).AnyTimes()
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "non-existent-cluster").Return(nil, fmt.Errorf("cluster not found")).AnyTimes()
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "nil-cluster").Return(nil, nil).AnyTimes()
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "nil-spec-cluster").Return(&ccev1.Cluster{}, nil).AnyTimes()
	mockK8SClient.EXPECT().GetCluster(gomock.Any(), "").Return(nil, fmt.Errorf("clusterID is empty")).AnyTimes()

	type args struct {
		reqBytes  []byte
		accountID string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "正常流程",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123", "sg-worker-456", "sg-master-789"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "无效JSON格式",
			args: args{
				reqBytes:  []byte(`{"clusterID": "test-cluster", "cidrs": [{"cidr": "********/24", "subnetID": "subnet-123"}], "securityGroups": {"eni": ["sg-eni-123"]`), // 缺少闭合的大括号
				accountID: "test-account-id",
			},
		},
		{
			name: "clusterID is empty (panic)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "cidrSubnetPairs is empty (panic)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID:      "cce-test-cluster",
					CIDRs:          []ccesdk.CIDRSubnetPair{},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "CIDR is empty in array (panic)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "", // 空的 CIDR
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "SubnetID is empty in array (panic)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "", // 空的 SubnetID
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "models.GetCluster returns error",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "non-existent-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "cluster is nil",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "nil-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "cluster.Spec is nil",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "nil-spec-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "ENI security group validation fails",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "sg-validation-fail-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-not-exist"}, // 这个安全组不在集群的安全组列表中
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "Worker security group validation fails",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "sg-validation-fail-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-worker-not-exist"}, // 这个安全组不在集群的安全组列表中
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "Master security group validation fails",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "sg-validation-fail-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-master-not-exist"}, // 这个安全组不在集群的安全组列表中
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "AddSecurityGroupCIDR fails (CIDR-Subnet mismatch)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-mismatch", // 这个子网的 CIDR 与请求的 CIDR 不匹配
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id",
				}
			}(),
		},
		{
			name: "accountID is empty (panic)",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "",
				}
			}(),
		},
		{
			name: "成功的完整流程",
			args: func() args {
				req := &ccesdk.AddSecurityGroupCIDRRequest{
					ClusterID: "cce-test-cluster",
					CIDRs: []ccesdk.CIDRSubnetPair{
						{
							CIDR:     "********/24",
							SubnetID: "subnet-123",
						},
					},
					SecurityGroups: []string{"sg-eni-123"},
				}
				reqBytes, _ := json.Marshal(req)

				return args{
					reqBytes:  reqBytes,
					accountID: "test-account-id", // 设置正确的accountID
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				if r := recover(); r != nil {
					t.Logf("recover from panic: %v", r)
				}
			}()
			sgController.Ctx.Input.RequestBody = tt.args.reqBytes
			sgController.BaseController.accountID = tt.args.accountID
			sgController.AddSecurityGroupCIDRs()
		})
	}
}

// TestSecurityGroupController_validateSecurityGroupsInCluster 测试集群安全组校验
func TestSecurityGroupController_validateSecurityGroupsInCluster(t *testing.T) {
	tests := []struct {
		name           string
		clusterCR      *ccev1.Cluster
		securityGroups []string
		wantErr        bool
		wantErrMsg     string
	}{
		{
			name: "所有安全组都在集群中",
			clusterCR: &ccev1.Cluster{
				Spec: ccetypes.ClusterSpec{
					ClusterID: "test-cluster",
					ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-eni-1"},
						{ID: "sg-eni-2"},
					},
					NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-worker-1"},
						{ID: "sg-worker-2"},
					},
					MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-master-1"},
						{ID: "sg-master-2"},
					},
				},
			},
			securityGroups: []string{"sg-eni-1", "sg-eni-2", "sg-worker-1", "sg-master-1"},
			wantErr:        false,
		},
		{
			name: "ENI安全组不在集群中",
			clusterCR: &ccev1.Cluster{
				Spec: ccetypes.ClusterSpec{
					ClusterID: "test-cluster",
					ENIDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-eni-1"},
					},
				},
			},
			securityGroups: []string{"sg-eni-not-exist"},
			wantErr:        true,
			wantErrMsg:     "security groups [sg-eni-not-exist] do not belong to cluster test-cluster",
		},
		{
			name: "Worker安全组不在集群中",
			clusterCR: &ccev1.Cluster{
				Spec: ccetypes.ClusterSpec{
					ClusterID: "test-cluster",
					NodeDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-worker-1"},
					},
				},
			},
			securityGroups: []string{"sg-worker-not-exist"},
			wantErr:        true,
			wantErrMsg:     "security groups [sg-worker-not-exist] do not belong to cluster test-cluster",
		},
		{
			name: "Master安全组不在集群中",
			clusterCR: &ccev1.Cluster{
				Spec: ccetypes.ClusterSpec{
					ClusterID: "test-cluster",
					MasterDefaultSecurityGroups: []ccetypes.SecurityGroupV2{
						{ID: "sg-master-1"},
					},
				},
			},
			securityGroups: []string{"sg-master-not-exist"},
			wantErr:        true,
			wantErrMsg:     "security groups [sg-master-not-exist] do not belong to cluster test-cluster",
		},
		{
			name: "空安全组配置",
			clusterCR: &ccev1.Cluster{
				Spec: ccetypes.ClusterSpec{
					ClusterID: "test-cluster",
				},
			},
			securityGroups: []string{},
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock客户端
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockMetaClient := metamock.NewMockInterface(ctrl)
			// Mock ListInstanceGroup 返回空列表
			mockMetaClient.EXPECT().ListInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			controller := &SecurityGroupController{
				BaseController: BaseController{
					clients: &clients.Clients{
						MetaK8SClient: mockMetaClient,
					},
				},
			}
			err := controller.validateSecurityGroupsInCluster(context.Background(), tt.clusterCR, tt.securityGroups)

			if tt.wantErr {
				if err == nil {
					t.Errorf("validateSecurityGroupsInCluster() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if err.Error() != tt.wantErrMsg {
					t.Errorf("validateSecurityGroupsInCluster() error = %v, wantErrMsg %v", err.Error(), tt.wantErrMsg)
				}
			} else {
				if err != nil {
					t.Errorf("validateSecurityGroupsInCluster() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
		})
	}
}

// TestSecurityGroupController_validateSecurityGroupsInVPC 测试VPC安全组校验
// TestSecurityGroupController_validateSecurityGroupsInVPC 测试VPC安全组校验
func TestSecurityGroupController_validateSecurityGroupsInVPC(t *testing.T) {
	tests := []struct {
		name           string
		setupMocks     func(*gomock.Controller) (*SecurityGroupController, context.Context)
		vpcID          string
		securityGroups []string
		wantErr        bool
		wantErrMsg     string
	}{
		{
			name: "空安全组配置",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				return &SecurityGroupController{}, context.Background()
			},
			vpcID:          "vpc-test",
			securityGroups: []string{},
			wantErr:        false,
		},
		{
			name: "所有安全组都在VPC中",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockSTSClient := &fakeSTSClient{}

				// Mock普通安全组查询
				mockBCCClient.EXPECT().GetSecurityGroups(gomock.Any(), &bcc.GetSecurityGroupsRequest{
					VPCID: "vpc-test",
				}, gomock.Any()).Return(&bcc.GetSecurityGroupsResponse{
					SecurityGroups: []bcc.SecurityGroup{
						{ID: "sg-eni-1"},
						{ID: "sg-worker-1"},
					},
				}, nil)

				// Mock企业安全组查询
				mockBCCClient.EXPECT().ListEnterpriseSecurityGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				controller := &SecurityGroupController{
					BaseController: BaseController{
						accountID: "test-account",
						clients: &clients.Clients{
							BCCClient: mockBCCClient,
							STSClient: mockSTSClient,
						},
					},
				}
				return controller, context.Background()
			},
			vpcID:          "vpc-test",
			securityGroups: []string{"sg-eni-1", "sg-worker-1"},
			wantErr:        false,
		},
		{
			name: "安全组不在VPC中",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockSTSClient := &fakeSTSClient{}

				// Mock普通安全组查询 - 返回空结果
				mockBCCClient.EXPECT().GetSecurityGroups(gomock.Any(), &bcc.GetSecurityGroupsRequest{
					VPCID: "vpc-test",
				}, gomock.Any()).Return(&bcc.GetSecurityGroupsResponse{
					SecurityGroups: []bcc.SecurityGroup{},
				}, nil)

				// Mock企业安全组查询 - 返回空结果
				mockBCCClient.EXPECT().ListEnterpriseSecurityGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				controller := &SecurityGroupController{
					BaseController: BaseController{
						accountID: "test-account",
						clients: &clients.Clients{
							BCCClient: mockBCCClient,
							STSClient: mockSTSClient,
						},
					},
				}
				return controller, context.Background()
			},
			vpcID:          "vpc-test",
			securityGroups: []string{"sg-not-exist"},
			wantErr:        true,
			wantErrMsg:     "security group sg-not-exist does not belong to VPC vpc-test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			controller, ctx := tt.setupMocks(ctrl)
			err := controller.validateSecurityGroupsInVPC(ctx, tt.vpcID, tt.securityGroups)

			if tt.wantErr {
				if err == nil {
					t.Errorf("validateSecurityGroupsInVPC() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if err.Error() != tt.wantErrMsg {
					t.Errorf("validateSecurityGroupsInVPC() error = %v, wantErrMsg %v", err.Error(), tt.wantErrMsg)
				}
			} else {
				if err != nil {
					t.Errorf("validateSecurityGroupsInVPC() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
		})
	}
}

// TestSecurityGroupController_validateCIDRSubnetMapping 测试CIDR与子网匹配校验
func TestSecurityGroupController_validateCIDRSubnetMapping(t *testing.T) {
	tests := []struct {
		name       string
		setupMocks func(*gomock.Controller) (*SecurityGroupController, context.Context)
		cidrs      []ccesdk.CIDRSubnetPair
		wantErr    bool
		wantErrMsg string
	}{
		{
			name: "空CIDR列表",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				return &SecurityGroupController{}, context.Background()
			},
			cidrs:   []ccesdk.CIDRSubnetPair{},
			wantErr: false,
		},
		{
			name: "CIDR与子网匹配",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				mockVPCClient := vpcmock.NewMockInterface(ctrl)
				mockSTSClient := &fakeSTSClient{}

				// Mock子网查询
				mockVPCClient.EXPECT().DescribeSubnet(gomock.Any(), "subnet-123", gomock.Any()).Return(&vpc.Subnet{
					CIDR: "********/24",
				}, nil)

				controller := &SecurityGroupController{
					BaseController: BaseController{
						accountID: "test-account",
						clients: &clients.Clients{
							VPCClient: mockVPCClient,
							STSClient: mockSTSClient,
						},
					},
				}
				return controller, context.Background()
			},
			cidrs: []ccesdk.CIDRSubnetPair{
				{CIDR: "********/24", SubnetID: "subnet-123"},
			},
			wantErr: false,
		},
		{
			name: "CIDR与子网不匹配",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				mockVPCClient := vpcmock.NewMockInterface(ctrl)
				mockSTSClient := &fakeSTSClient{}

				// Mock子网查询 - 返回不匹配的CIDR
				mockVPCClient.EXPECT().DescribeSubnet(gomock.Any(), "subnet-123", gomock.Any()).Return(&vpc.Subnet{
					CIDR: "********/24", // 与请求的CIDR不匹配
				}, nil)

				controller := &SecurityGroupController{
					BaseController: BaseController{
						accountID: "test-account",
						clients: &clients.Clients{
							VPCClient: mockVPCClient,
							STSClient: mockSTSClient,
						},
					},
				}
				return controller, context.Background()
			},
			cidrs: []ccesdk.CIDRSubnetPair{
				{CIDR: "********/24", SubnetID: "subnet-123"},
			},
			wantErr:    true,
			wantErrMsg: "CIDR ********/24 does not match subnet subnet-123",
		},
		{
			name: "子网不存在",
			setupMocks: func(ctrl *gomock.Controller) (*SecurityGroupController, context.Context) {
				mockVPCClient := vpcmock.NewMockInterface(ctrl)
				mockSTSClient := &fakeSTSClient{}

				// Mock子网查询 - 返回nil表示子网不存在
				mockVPCClient.EXPECT().DescribeSubnet(gomock.Any(), "subnet-not-exist", gomock.Any()).Return(nil, nil)

				controller := &SecurityGroupController{
					BaseController: BaseController{
						accountID: "test-account",
						clients: &clients.Clients{
							VPCClient: mockVPCClient,
							STSClient: mockSTSClient,
						},
					},
				}
				return controller, context.Background()
			},
			cidrs: []ccesdk.CIDRSubnetPair{
				{CIDR: "********/24", SubnetID: "subnet-not-exist"},
			},
			wantErr:    true,
			wantErrMsg: "CIDR ********/24 does not match subnet subnet-not-exist",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			controller, ctx := tt.setupMocks(ctrl)
			err := controller.validateCIDRSubnetMapping(ctx, tt.cidrs)

			if tt.wantErr {
				if err == nil {
					t.Errorf("validateCIDRSubnetMapping() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if err.Error() != tt.wantErrMsg {
					t.Errorf("validateCIDRSubnetMapping() error = %v, wantErrMsg %v", err.Error(), tt.wantErrMsg)
				}
			} else {
				if err != nil {
					t.Errorf("validateCIDRSubnetMapping() error = %v, wantErr %v", err, tt.wantErr)
				}
			}
		})
	}
}
