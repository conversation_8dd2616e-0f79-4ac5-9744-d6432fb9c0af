/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  instancegroup_controller
 * @Version: 1.0.0
 * @Date: 2020/6/29 5:29 下午
 */
package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	clusterfiller "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec/cluster"

	"github.com/jinzhu/copier"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	bceerror "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/error"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/securitygroup"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/autoscaler"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/instancegroup"
)

var (
	maxAttachInstanceCount = 50
)

// InstanceGroupController instance controller
type InstanceGroupController struct {
	BaseController

	instanceGroupService instancegroup.Interface
}

// Prepare run before HandlerFunc
func (c *InstanceGroupController) Prepare() {
	c.BaseController.Prepare()
	c.specService = c.specService.WithCache(specCache)
	service, err := instancegroup.NewService(c.ctx, c.accountID, c.userID, c.clients, c.models, c.config, c.clientSet)
	if err != nil {
		logger.Errorf(c.ctx, "failed to create instanceGroup instanceGroupService, err: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	c.instanceGroupService = service
}

func (c *InstanceGroupController) VerifyAndPadDefault(ctx context.Context,
	request *ccesdk.CreateInstanceGroupsRequest) (code *errorcode.ErrorCode, level errorcode.Level, format string) {
	request.AccountID = c.accountID
	// 目前cluster service接口仅仅支持创建node类型的节点组
	request.ClusterRole = ccetypes.ClusterRoleNode
	logger.Infof(c.ctx, "Create instanceGroups begin: %v", utils.ToJSON(request))

	for i := range request.InstanceGroups {
		instanceGroupSpec := request.InstanceGroups[i]
		instanceGroupSpec.ClusterRole = request.ClusterRole
		if err := c.instanceGroupService.VerifyAndPadDefault(ctx, request.Cluster.Spec, instanceGroupSpec, ""); err != nil {
			logger.Errorf(ctx, "verifyAndPadDefault failed: %v", err)
			switch {
			case models.ErrIgReplicasMaxLessThanMin.Is(err):
				return errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error()
			case strings.Contains(err.Error(), "Exceeding the number of nodes"):
				return errorcode.NewCCEInstanceNumberExceed(err.Error()), errorcode.LevelByUser, err.Error()
			default:
				return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
			}
		}
		instanceGroupSpec.ClusterRole = request.ClusterRole
	}
	return nil, "", ""
}

func (c *InstanceGroupController) CreateInstanceGroupsCR(ctx context.Context,
	cluster *models.Cluster, instanceGroupSpecs []*ccetypes.InstanceGroupSpec) error {
	for i := range instanceGroupSpecs {
		instanceGroupSpec := instanceGroupSpecs[i]
		instanceGroupID := instanceGroupSpec.CCEInstanceGroupID
		if len(instanceGroupSpec.DefaultSecurityGroups) == 0 && cluster.Spec != nil && len(cluster.Spec.NodeDefaultSecurityGroups) > 0 {
			instanceGroupSpec.DefaultSecurityGroups = cluster.Spec.NodeDefaultSecurityGroups
			instanceGroupSpec.SecurityGroupType = cluster.Spec.NodeDefaultSecurityGroups[0].Type
		}
		err := c.instanceGroupService.CreateInstanceGroupCR(ctx, instanceGroupID, cluster, instanceGroupSpec)
		if err != nil {
			logger.Errorf(ctx, "CreateInstanceGroupCR failed: %v", err)
			return err
		}
	}
	return nil
}

func (c *InstanceGroupController) CreateInstanceGroupsModel(ctx context.Context,
	instanceGroupSpecs []*ccetypes.InstanceGroupSpec) (code *errorcode.ErrorCode, level errorcode.Level, format string) {
	instanceGroupModels := make([]*models.InstanceGroup, 0)
	for i := range instanceGroupSpecs {
		instanceGroupSpec := instanceGroupSpecs[i]
		instanceGroupModel := instancegroup.InstanceGroupSpecToModel(instanceGroupSpec)
		instanceGroupModels = append(instanceGroupModels, instanceGroupModel)
	}
	instanceGroupIDs, err := c.models.CreateInstanceGroups(ctx, instanceGroupModels)
	if err != nil {
		logger.Errorf(ctx, "CreateInstanceGroups failed: %v", err)
		return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
	}
	for i := range instanceGroupSpecs {
		instanceGroupSpec := instanceGroupSpecs[i]
		instanceGroupID := instanceGroupIDs[i]
		instanceGroupSpec.CCEInstanceGroupID = instanceGroupID
	}
	return nil, "", ""
}

func (c *InstanceGroupController) CreateInstanceGroup() {
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}
	var request ccesdk.CreateInstanceGroupRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal CreateInstanceGroupRequest failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	// 节点配置预检查
	checkFunc := func(template ccetypes.InstanceTemplate) error {
		return c.UpdateInstanceSpecPreCheck(template.InstanceSpec)
	}

	err := ccetypes.CheckSpecTemplates(request.InstanceGroupSpec, checkFunc)
	if err != nil {
		logger.Errorf(c.ctx, "CheckSpecTemplates failed: %v", err)
		c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, err.Error())
		return
	}

	request.ClusterID = clusterID
	request.AccountID = c.accountID
	// 目前cluster service接口仅仅支持创建node类型的节点组
	request.ClusterRole = ccetypes.ClusterRoleNode
	logger.Infof(c.ctx, "Create instanceGroup begin: %v", utils.ToJSON(request))

	instanceGroupSpec := &request.InstanceGroupSpec

	//hpas节点组暂时不支持直接创建，所以校验副本书
	var specs []string
	for _, template := range instanceGroupSpec.InstanceTemplates {
		// 收集所需的所有套餐，只对 ebc 和 bcc 进行校验
		if template.MachineType == ccetypes.MachineTypeBCC || template.MachineType == ccetypes.MachineTypeEBC {
			specs = append(specs, template.InstanceResource.MachineSpec)
		}
		if template.MachineType == ccetypes.MachineTypeHPAS && instanceGroupSpec.Replicas > 0 {
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set replicas not implemented"), errorcode.LevelByAdmin, "")
			return
		}
	}
	if instanceGroupSpec.InstanceTemplate.MachineType == ccetypes.MachineTypeHPAS && instanceGroupSpec.Replicas > 0 {
		c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set replicas not implemented"), errorcode.LevelByAdmin, "")
		return
	}

	// 查询 Cluster
	cluster, err := c.clients.K8SClient.GetCluster(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "GetCluster failed : %v", err)
		if meta.ErrClusterNotExist.Is(err) {
			c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser,
				"Cluster %s not exists", clusterID)
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin,
			"GetCluster %s error", clusterID)
		return
	}

	if v, ok := cluster.Annotations[ccev1.AnnotationSkipCheckSpec]; !ok && v != ccev1.AnnotationTrue {
		// 校验套餐是否已经全部适配
		err = c.CheckMachineSpecs(c.ctx, specs)
		if err != nil {
			logger.Errorf(c.ctx, "CheckMachineSpecs(%v) failed: %v", specs, err)
			c.errorHandlerV2(errorcode.NewInvalidMachineSpec(), errorcode.LevelByUser, err.Error())
			return
		}
	}

	// 安全组 V2：兼容存量集群，新建节点组时，需要自动配置正确的安全组
	if err := c.setDefaultSecurityGroupForIg(c.ctx, instanceGroupSpec); err != nil {
		logger.Errorf(c.ctx, "set defaultSecurityGroup for new InstanceGroupError")
		if apierrors.IsNotFound(err) {
			c.errorHandlerV2(errorcode.NewClusterNotFound(), errorcode.LevelByUser, err.Error())
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		return
	}

	// 校验DeployCustomConfig 需要cluster字段，放在这里校验，前面的只校验前后置脚本等不依赖cluster的字段
	f := fillspec.BaseFiller{}
	listInstanceTemplate := make([]ccetypes.InstanceTemplate, 0)
	listInstanceTemplate = append(listInstanceTemplate, instanceGroupSpec.InstanceTemplate)
	listInstanceTemplate = append(listInstanceTemplate, instanceGroupSpec.InstanceTemplates...)
	for _, instanceTemplate := range listInstanceTemplate {
		if cluster.Spec.K8SVersion != "" {
			template := instanceTemplate
			if template.RuntimeType == ccetypes.RuntimeTypeContainerd && template.RuntimeVersion != "" {
				if err := clusterfiller.ValidateK8sContainerdVersion(
					cluster.Spec.K8SVersion, template.RuntimeVersion); err != nil {
					c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
					return
				}
			} else if template.RuntimeType == ccetypes.RuntimeTypeDocker && template.RuntimeVersion != "" {
				if err := clusterfiller.ValidateK8sDockerVersion(
					cluster.Spec.K8SVersion, template.RuntimeVersion); err != nil {
					c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
					return
				}
			}
		}
		err = fillspec.SetAndCheckDataDir(instanceTemplate.InstanceSpec.DeployCustomConfig, instanceTemplate.InstanceSpec.RuntimeType)
		if err != nil {
			logger.Errorf(c.ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
			c.errorHandlerV2(errorcode.NewInvalidDataRootDir(), errorcode.LevelByUser, "invalid data Dir, err is: %s", err.Error())
			return
		}
		_, err = f.DeployCustomConfig(c.ctx, &cluster.Spec, &instanceTemplate.InstanceSpec)
		if err != nil {
			logger.Errorf(c.ctx, "invalid DeployCustomConfig %s, InstanceTemplateID is %s, err is %s",
				utils.ToJSON(&instanceTemplate.InstanceSpec), instanceTemplate.InstanceTemplateID, err.Error())
			c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, "invalid DeployCustomConfig. err is %s", err.Error())
			return
		}
	}

	instanceGroupID, err := c.instanceGroupService.Create(c.ctx, instanceGroupSpec)
	if err != nil {
		logger.Errorf(c.ctx, "Create InstanceGroup failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		case strings.Contains(err.Error(), "AutoRenewTimeUnit"):
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
		case models.ErrIgReplicasMaxLessThanMin.Is(err):
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
		case strings.Contains(err.Error(), "Exceeding the number of nodes"):
			c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(err.Error()), errorcode.LevelByAdmin, err.Error())
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	// 更新集群 CA 节点组
	if instanceGroupSpec.ClusterAutoscalerSpec != nil && instanceGroupSpec.ClusterAutoscalerSpec.Enabled {
		if err := c.updateCAGroups(clusterID); err != nil {
			logger.Errorf(c.ctx, "updateCAGroups failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}

	logger.Infof(c.ctx, "InstanceGroup created: %s", utils.ToJSON(instanceGroupID))

	c.Data["json"] = ccesdk.CreateInstanceGroupResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
		InstanceGroupID: instanceGroupID,
	}
	c.ServeJSON()
}

// setDefaultSecurityGroupForIg 为新建的节点组自动配置默认安全组
// 针对存量集群，兼容算法如下
//  1. 判断集群级别是否有配置 Worker 安全组，如果有，则以集群配置为准
//  2. 使用 CCE-Worker-默认安全组
func (c *InstanceGroupController) setDefaultSecurityGroupForIg(ctx context.Context, igSpec *ccetypes.InstanceGroupSpec) error {
	if len(igSpec.DefaultSecurityGroups) != 0 {
		logger.Infof(ctx, "default securityGroups has been set")
		return nil
	}

	// 判断集群级别是否有配置 Worker 安全组
	cluster, err := c.clients.MetaK8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, igSpec.ClusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get cluster node securityGroup err: %v", err)
		return err
	}
	clusterNodeSecurityGroup := cluster.Spec.NodeDefaultSecurityGroups

	if len(clusterNodeSecurityGroup) != 0 {
		igSpec.DefaultSecurityGroups = append(igSpec.DefaultSecurityGroups, clusterNodeSecurityGroup...)
		logger.Infof(ctx, "use NodeDefaultSecurityGroups in cluster as default SecurityGroup")
		return nil
	}

	// 从集群中获取安全组配置失败，则默认使用 CCE-Worker 默认安全组
	// 此处不用配置 igSpec.DefaultSecurityGroups;
	// 极端情况下集群所在 VPC 内无“CCE-Worker 默认安全组”，需要在扩容时，由 InstanceController 创建；
	// 在查询节点组详情时可顺利展示。
	// 配置 EnableCCERequiredSecurityGroup = true；instance-controller 在将自动为节点绑定 CCE-Worker 默认安全组；
	// 注：目前新建集群一定会在 Cluster 级别配置 Worker 安全组
	igSpec.InstanceTemplate.VPCConfig.SecurityGroup.EnableCCERequiredSecurityGroup = true
	for i := range igSpec.InstanceTemplates {
		igSpec.InstanceTemplates[i].VPCConfig.SecurityGroup.EnableCCERequiredSecurityGroup = true
	}

	return nil
}

func (c *InstanceGroupController) ListInstanceGroup() {
	ctx := c.ctx
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	accountID := c.accountID

	// 如果pageNo和pageSize不传，表示不分页，返回全部结果
	pageNo, err := c.GetInt("pageNo", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid pageNo, %v", err).Error()), errorcode.LevelByUser, "")
	}

	if pageNo < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid pageNo"), errorcode.LevelByUser, "")
	}

	pageSize, err := c.GetInt("pageSize", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid pageSize, %v", err).Error()), errorcode.LevelByUser, "")
	}

	if pageSize < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid pageSize"), errorcode.LevelByUser, "")
	}

	keywordType := c.GetString("keywordType")
	keyword := c.GetString("keyword")

	var instanceGroupName, instanceGroupID string
	if keywordType == "instanceGroupName" {
		instanceGroupName = keyword
	} else if keywordType == "instanceGroupID" {
		instanceGroupID = keyword
	}

	// 新增：计费方式参数解析
	chargingType := c.GetString("chargingType")
	var chargingTypePtr *string
	if chargingType != "" {
		// 参数校验
		if !isValidChargingType(chargingType) {
			c.errorHandlerV2(errorcode.NewInvalidParam("invalid chargingType"), errorcode.LevelByUser, "")
			return
		}
		chargingTypePtr = &chargingType
	}

	opt := instancegroup.ListOptions{
		AccountID:         accountID,
		ClusterID:         clusterID,
		Role:              ccetypes.ClusterRoleNode,
		PageNo:            pageNo,
		PageSize:          pageSize,
		InstanceGroupName: instanceGroupName,
		InstanceGroupID:   instanceGroupID,
		ChargingType:      chargingTypePtr, // 新增：计费方式筛选
	}

	// 查询 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, c.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	// 查询workflow
	workflows, err := c.getUpgradeNodesWorkflows(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "getWorkflows failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	logger.Infof(ctx, "ListInstanceGroup workflows: %s", utils.ToJSON(workflows))

	logger.Infof(c.ctx, "list InstanceGroup begin, list opt: %s", utils.ToJSON(opt))

	list, err := c.instanceGroupService.List(c.ctx, opt)
	if err != nil {
		logger.Errorf(c.ctx, "list InstanceGroup failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}
	// 初始化用户集群 K8S Client, 仅支持这三种状态是因为其他状态集群可能访问 APIServer 失败
	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

		logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			//c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}
	igs := make([]*ccesdk.InstanceGroup, 0)
	igsStatus := make([]*ccesdk.InstanceGroupStatus, len(list.Items))

	var wg sync.WaitGroup
	wg.Add(len(list.Items))
	for i := 0; i < len(list.Items); i++ {
		go func(instance *models.InstanceGroup, i int) {
			defer wg.Done()
			igsStatus[i], err = instancegroup.GetInstanceGroupStatus(ctx, k8sClient, list.Items[i].Spec.CCEInstanceGroupID)
			if err != nil {
				logger.Errorf(ctx, "GetInstanceGroupStatus err, err: %s", err.Error())
			}
		}(list.Items[i], i)
	}
	wg.Wait()

	for i := 0; i < len(list.Items); i++ {
		igs = append(igs, c.instanceGroupModelToSDK(ctx, list.Items[i], igsStatus[i]))
	}

	c.addInstanceGroupsWorkflowID(ctx, igs, workflows)

	// logger.Infof(c.ctx, "list InstanceGroups succeed: %s", utils.ToJSON(igs))

	c.Data["json"] = ccesdk.ListInstanceGroupResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
		Page: ccesdk.ListInstanceGroupPage{
			PageNo:     pageNo,
			PageSize:   pageSize,
			TotalCount: list.TotalCount,
			List:       igs,
		},
	}

	c.ServeJSON()
}

func (c *InstanceGroupController) getInstanceGroupUpgradeNodesRecord(
	instanceGroupId string, workflows map[string][]ccev1.Workflow) []ccetypes.UpgradeNodesRecord {
	var records []ccetypes.UpgradeNodesRecord
	if w, ok := workflows[instanceGroupId]; ok {
		for _, workflow := range w {
			if workflow.DeletionTimestamp != nil {
				continue
			}
			records = append(records, ccetypes.UpgradeNodesRecord{
				WorkflowID:    workflow.Spec.WorkflowID,
				WorkflowType:  workflow.Spec.WorkflowType,
				WorkflowPhase: workflow.Status.WorkflowPhase,
				CreateTime:    workflow.CreationTimestamp.UTC().Format("2006-01-02T15:04:05Z"),
			})
		}
	}
	return records
}

func (c *InstanceGroupController) addInstanceGroupsWorkflowID(ctx context.Context,
	instanceGroups []*ccesdk.InstanceGroup, workflows map[string][]ccev1.Workflow) {
	for i, ig := range instanceGroups {
		instanceGroups[i].UpgradeWorkflowRecords = c.getInstanceGroupUpgradeNodesRecord(ig.Spec.CCEInstanceGroupID, workflows)
	}
}

// isValidChargingType - 校验计费方式参数是否有效
func isValidChargingType(chargingType string) bool {
	validTypes := []string{string(bcc.PaymentTimingPrepaid), string(bcc.PaymentTimingPostpaid), string(bcc.PaymentTimingBid)}
	for _, validType := range validTypes {
		if chargingType == validType {
			return true
		}
	}
	return false
}

func (c *InstanceGroupController) getUpgradeNodesWorkflows(ctx context.Context, clusterID string) (map[string][]ccev1.Workflow, error) {
	// 获取用户所有 Workflow
	workflowList, err := c.clientSet.MetaClient.ListWorkflows(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s,%s=%s", ccev1.LabelWorkflowAccountID, c.accountID, ccev1.LabelWorkflowClusterID, clusterID),
		Limit:         500,
	})
	if err != nil {
		logger.Errorf(ctx, "ListWorkflows failed: %s", err)
		return nil, err
	}

	// 构建 instanceGroupID -> workflow 映射
	workflows := map[string][]ccev1.Workflow{}
	for _, w := range workflowList.Items {
		workflowID := w.GetName()
		if w.Spec.WorkflowType == ccetypes.WorkflowTypeUpgradeNodes ||
			w.Spec.WorkflowType == ccetypes.WorkflowTypeUpgradeNodesPreCheck {
			config := w.Spec.WorkflowConfig.UpgradeNodesWorkflowConfig
			if config == nil {
				logger.Warnf(ctx, "getWorkflows UpgradeNodesWorkflowConfig nil, clusterID:%s, workflowID:%s",
					clusterID, workflowID)
				continue
			}
			if w.Spec.ClusterID != clusterID {
				continue
			}

			instanceGroupID := config.InstanceGroupID
			workflows[instanceGroupID] = append(workflows[instanceGroupID], w)

		} else if w.Spec.WorkflowType == ccetypes.WorkflowTypeUpgradeKubeletConfig {
			config := w.Spec.WorkflowConfig.UpgradeKubeletConfig
			if config == nil {
				logger.Warnf(ctx, "getWorkflows UpgradeKubeletConfig nil, clusterID:%s, workflowID:%s",
					clusterID, workflowID)
				continue
			}
			if w.Spec.ClusterID != clusterID {
				continue
			}

			instanceGroupID := config.InstanceGroupID
			workflows[instanceGroupID] = append(workflows[instanceGroupID], w)
		}
	}

	// workflow列表按时间排序,最新的排在最前。
	for instanceGroupID, w := range workflows {
		sort.Slice(w, func(i, j int) bool {
			return w[i].CreationTimestamp.Time.After(w[j].CreationTimestamp.Time)
		})
		workflows[instanceGroupID] = w
	}

	return workflows, nil
}

func (c *InstanceGroupController) GetInstanceGroup() {
	ctx := c.ctx
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}
	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}
	accountID := c.accountID
	logger.Infof(c.ctx, "Get InstanceGroup begin, instanceGroupID: %s", instanceGroupID)
	ig, err := c.instanceGroupService.Get(c.ctx, accountID, instanceGroupID)
	if err != nil {
		logger.Errorf(c.ctx, "get InstanceGroup failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		case models.IsNotExist(err):
			c.errorHandlerV2(errorcode.NewInstanceGroupNotFound(err.Error()), errorcode.LevelByUser, "")
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}
	// 查询 Cluster
	cluster, err := c.models.GetCluster(ctx, clusterID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "cluster %s not exist", clusterID)
	}

	workflows, err := c.getUpgradeNodesWorkflows(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "getWorkflows failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	}

	var k8sClient kubernetes.Interface
	if cluster.Status.ClusterPhase == ccetypes.ClusterPhaseRunning ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgrading ||
		cluster.Status.ClusterPhase == ccetypes.ClusterPhaseUpgradeFailed {

		logger.Infof(ctx, "ClusterPhase = %s, try to get K8S Nodes", cluster.Status.ClusterPhase)

		k8sClient, err = getClientSet(ctx, clusterID, c.models)
		if err != nil {
			logger.Errorf(ctx, "get k8s client failed: %v", err)
			//c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}
	igStatus, err := instancegroup.GetInstanceGroupStatus(ctx, k8sClient, ig.Spec.CCEInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroupStatus err, err: %s", err.Error())
	}
	sdkIG := c.instanceGroupModelToSDK(ctx, ig, igStatus)
	sdkIG.UpgradeWorkflowRecords = c.getInstanceGroupUpgradeNodesRecord(sdkIG.Spec.CCEInstanceGroupID, workflows)

	logger.Infof(c.ctx, "Get InstanceGroup succeed: %s", utils.ToJSON(sdkIG))
	c.Data["json"] = ccesdk.GetInstanceGroupResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
		InstanceGroup: sdkIG,
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) getSSHKeyName(ctx context.Context, sshKeyID string) (string, error) {
	kp, err := c.clientSet.OpenBCCClient.GetKeyPair(ctx, sshKeyID, c.clientSet.STSClient.NewSignOption(ctx, c.accountID))
	if err != nil {
		logger.Errorf(ctx, "GetKeyPair err: %v", err)
		return "", err
	}
	return kp.Keypair.Name, nil
}

//func (c *InstanceGroupController) getDeploySetName(ctx context.Context, deploySetID string) (string, error) {
//	ds, err := c.clientSet.OpenBCCClient.GetDeploySet(ctx, deploySetID, c.clientSet.STSClient.NewSignOption(ctx, c.accountID))
//	if err != nil {
//		logger.Errorf(ctx, "GetDeploySet err: %v", err)
//		return "", err
//	}
//	return ds.Name, nil
//}

func (c *InstanceGroupController) getAspName(ctx context.Context, aspID string) (string, error) {
	asp, err := c.clientSet.OpenBCCClient.GetAutoSnapshot(ctx, aspID, c.clientSet.STSClient.NewSignOption(ctx, c.accountID))
	if err != nil {
		logger.Errorf(ctx, "GetAutoSnapshot err: %v", err)
		return "", err
	}
	return asp.AutoSnapshotPolicy.Name, nil
}

func (c *InstanceGroupController) UpdateInstanceGroupReplicas() {
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}
	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}
	accountID := c.accountID

	var request ccesdk.UpdateInstanceGroupReplicasRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal UpdateInstanceGroupReplicasReuqest failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}
	if request.Replicas < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("negative replicas"), errorcode.LevelByUser, "")
	}

	logger.Infof(c.ctx, "Update InstanceGroup replicas begin, InstanceGroupID: %s, request: %s", instanceGroupID, utils.ToJSON(request))
	spec := &ccetypes.InstanceGroupSpec{
		ClusterID:          clusterID,
		CCEInstanceGroupID: instanceGroupID,
		AccountID:          accountID,
		Replicas:           request.Replicas,
	}
	opt := instancegroup.UpdateOptions{
		Fields:                 []string{instancegroup.UpdateFieldReplicas},
		ScaleShrinkInstanceIDs: request.InstanceIDs,
		ShrinkCleanPolicy:      ccetypes.RemainCleanPolicy,
		InstanceDeleteOption:   request.DeleteOption,
	}
	if request.DeleteInstance {
		opt.ShrinkCleanPolicy = ccetypes.DeleteCleanPolicy
	}

	// 检查是否存在冲突任务
	ig, err := c.clientSet.MetaClient.GetInstanceGroup(c.ctx, ccetypes.NamespaceDefault, instanceGroupID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(c.ctx, "GetInstanceGroup failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByUser, "")
	}

	if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeHPAS {
		c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set replicas not implemented"), errorcode.LevelByAdmin, "")
	}

	// 不允许缩容，允许扩容
	if ig.Status.ActualReplicas > spec.Replicas {
		err = c.exitConflictWorkflowByInstanceGroup(c.ctx, instanceGroupID)
		if err != nil {
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
		}
	}

	//检查是否有正在执行扩缩容task
	if err := c.existConflictTaskByInstanceGroup(c.ctx, instanceGroupID); err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
	}

	if err := c.instanceGroupService.Update(c.ctx, spec, opt); err != nil {
		logger.Errorf(c.ctx, "Update InstanceGroup replicas failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		case strings.Contains(err.Error(), "could not scale down to"):
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
		case strings.Contains(err.Error(), "Exceeding the number of nodes"):
			c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(err.Error()), errorcode.LevelByAdmin, err.Error())
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "Update InstanceGroup replicas succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupReplicasResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) UpdateInstanceGroupClusterAutoscalerSpec() {
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}
	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}
	accountID := c.accountID

	var request ccesdk.ClusterAutoscalerSpec
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal ClusterAutoscalerSpec failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	if request.MinReplicas < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid minReplicas"), errorcode.LevelByUser, "")
	}
	if request.MaxReplicas < request.MinReplicas {
		c.errorHandlerV2(errorcode.NewInvalidParam("最大节点数应大于等于最小节点数"), errorcode.LevelByUser, "")
	}

	if request.ScalingGroupPriority < 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid scalingGroupPriority"), errorcode.LevelByUser, "")
	}

	logger.Infof(c.ctx, "Update InstanceGroup clusterAutoscalerSpec begin")

	var clusterAutoscalerSpec ccetypes.ClusterAutoscalerSpec
	if err := copier.Copy(&clusterAutoscalerSpec, &request); err != nil {
		logger.Errorf(c.ctx, "copy clusterAutoscalerSpec from sdk obj to internal failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}

	ok, err := c.checkCAInit(clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "checkCAInit failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}
	// 开启节点组自动伸缩确保ca已经初始化
	if !ok {
		logger.Errorf(c.ctx, "%v clusterAutoscaler not init", clusterID)
		c.errorHandlerV2(errorcode.NewAddonStatusNotAllowed("autoscaler not init"), errorcode.LevelByUser, "autoscaler not init")
	}

	spec := &ccetypes.InstanceGroupSpec{
		ClusterID:             clusterID,
		CCEInstanceGroupID:    instanceGroupID,
		AccountID:             accountID,
		ClusterAutoscalerSpec: &clusterAutoscalerSpec,
	}
	if err := c.instanceGroupService.Update(c.ctx, spec, instancegroup.UpdateOptions{Fields: []string{instancegroup.UpdateFieldAutoscalerSpec}}); err != nil {
		logger.Errorf(c.ctx, "Update InstanceGroup clusterAutoscalerSpec failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		case models.IsErrWorkflowConflict(err):
			c.errorHandlerV2(errorcode.NewMultiWorkflowExistInCluster(err.Error()), errorcode.LevelByUser, "")
		case models.ErrHPASNotImplemented.Is(err):
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set autoscaler not implemented"), errorcode.LevelByAdmin, "")
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	// 更新集群 CA 节点组
	//if err := c.updateCAGroups(clusterID); err != nil {
	//	logger.Errorf(c.ctx, "updateCAGroups failed: %v", err)
	//	c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
	//}

	logger.Infof(c.ctx, "Update InstanceGroup autoscaler spec succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupClusterAutoscalerSpecResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) UpdateInstanceGroupInstanceTemplate() {

	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}
	instanceGroupID := c.GetPathParams(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
		return
	}
	accountID := c.accountID

	var request ccesdk.InstanceTemplate
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal InstanceTemplate failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	var instanceTemplate ccetypes.InstanceTemplate
	if err := copier.Copy(&instanceTemplate, &request); err != nil {
		logger.Errorf(c.ctx, "copy instanceTemplate from sdk obj to internal failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		return
	}

	// 实例模板预检查
	err := c.UpdateInstanceSpecPreCheck(instanceTemplate.InstanceSpec)
	if err != nil {
		logger.Errorf(c.ctx, "CheckSpecTemplates failed: %v", err)
		c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, err.Error())
		return
	}
	// kubelet 和 容器运行时数据目录校验
	err = fillspec.SetAndCheckDataDir(instanceTemplate.InstanceSpec.DeployCustomConfig, instanceTemplate.InstanceSpec.RuntimeType)
	if err != nil {
		logger.Errorf(c.ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
		c.errorHandlerV2(errorcode.NewInvalidDataRootDir(), errorcode.LevelByUser, "invalid data Dir, err is: %s", err.Error())
		return
	}

	spec := &ccetypes.InstanceGroupSpec{
		ClusterID:          clusterID,
		CCEInstanceGroupID: instanceGroupID,
		AccountID:          accountID,
		InstanceTemplate:   instanceTemplate,
	}
	if err := c.instanceGroupService.Update(c.ctx, spec, instancegroup.UpdateOptions{Fields: []string{instancegroup.UpdateFieldInstanceTemplate}}); err != nil {
		logger.Errorf(c.ctx, "Update InstanceGroup instanceTemplate failed: %v", err)
		if models.ErrTaintsInvalid.Is(err) {
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		return
	}

	logger.Infof(c.ctx, "Update InstanceGroup instanceTemplate succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupInstanceTemplateResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) UpdateInstanceGroupSecurityGroups() {
	ctx := c.ctx

	clusterID := c.Ctx.Input.Param(":clusterID")
	if err := utils.ValidateClusterID(clusterID); err != nil {
		logger.Errorf(ctx, "checkClusterID failed: %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "clusterID %s format error", clusterID)
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	_, err := c.clients.MetaK8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "MetaK8SClient GetCluster failed: %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
	}

	var req ccesdk.UpdateInstanceGroupDefaultSecurityGroupRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &req); err != nil {
		logger.Errorf(ctx, "Unmarshal UpdateDefaultSecurityGroupsRequest failed: %v", err)
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "request parameter not valid")
	}

	if len(req.SecurityGroups) == 0 {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "securityGroups is empty")
	}

	if err := c.validateDefaultSecurityGroupsV2Type(req.SecurityGroups); err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
	}

	ig, err := c.instanceGroupService.GetCRD(c.ctx, c.accountID, instanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "Get InstanceGroup CRD failed: %v", err)
		if apierrors.IsNotFound(err) {
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}

	ig.Spec.DefaultSecurityGroups = req.SecurityGroups

	if err := c.instanceGroupService.UpdateCRD(ctx, ig); err != nil {
		logger.Errorf(ctx, "Update InstanceGroup CRD failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}

	logger.Infof(c.ctx, "Update InstanceGroup securityGroup succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupDefaultSecurityGroupResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) validateSecurityGroupsV2Type(sgtype ccetypes.SecurityGroupType) error {
	if sgtype != ccetypes.SecurityGroupTypeNormal &&
		sgtype != ccetypes.SecurityGroupTypeEnterprise {
		return fmt.Errorf("unknown securitygroup type")
	}
	return nil
}

func (c *InstanceGroupController) validateDefaultSecurityGroupsV2Type(sgs []ccetypes.SecurityGroupV2) error {
	if len(sgs) > 0 {
		firstType := sgs[0].Type
		for _, sg := range sgs {
			if err := c.validateSecurityGroupsV2Type(sg.Type); err != nil {
				return err
			}
			if sg.Type != firstType {
				return fmt.Errorf("type of node DefaultSecurityGroups " +
					"in the same slice must be consistent")
			}
		}
	}
	return nil
}

// 修改节点配置，修改主机/备选机型或者添加备选机型
func (c *InstanceGroupController) UpdateInstanceGroupConfigure() {
	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}
	instanceGroupID := c.GetPathParams(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
		return
	}

	var request ccesdk.UpdateInstanceGroupRequest
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal InstanceTemplate failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	// 节点配置预检查
	checkFunc := func(template ccetypes.InstanceTemplate) error {
		return c.UpdateInstanceSpecPreCheck(template.InstanceSpec)
	}
	err := ccetypes.CheckSpecTemplates(request.InstanceGroupSpec, checkFunc)
	if err != nil {
		logger.Errorf(c.ctx, "CheckSpecTemplates failed: %v", err)
		c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, err.Error())
		return
	}

	// 获取 Cluster
	cluster, err := c.clients.K8SClient.GetCluster(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "GetCluster failed : %v", err)
		if meta.ErrClusterNotExist.Is(err) {
			c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser,
				"Cluster %s not exists", clusterID)
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin,
			"GetCluster %s error", clusterID)
		return
	}

	request.AccountID = c.accountID
	request.ClusterID = clusterID
	request.CCEInstanceGroupID = instanceGroupID

	fields := []string{instancegroup.UpdateFieldConfigure}

	// 获取当前的instancegroup信息
	ig, err := c.instanceGroupService.Get(c.ctx, c.accountID, instanceGroupID)
	if err != nil {
		logger.Errorf(c.ctx, "get InstanceGroup failed: %v", err)
		switch {
		case models.IsNotExist(err):
			c.errorHandlerV2(errorcode.NewInstanceGroupNotFound(err.Error()), errorcode.LevelByUser, "")
			return
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
			return
		}
	}

	if ig.Spec.InstanceTemplate.MachineType == ccetypes.MachineTypeHPAS {
		if request.InstanceGroupSpec.Replicas != ig.Spec.Replicas {
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set replicas not implemented"), errorcode.LevelByAdmin, "")
		}
		if request.ClusterAutoscalerSpec != nil && request.ClusterAutoscalerSpec.Enabled {
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS set autoscaler not implemented"), errorcode.LevelByAdmin, "")
		}
		if len(request.InstanceTemplates) > 1 {
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS multi instance template not implemented"), errorcode.LevelByAdmin, "")
		}
	}

	var specs []string
	for _, template := range request.InstanceTemplates {
		specs = append(specs, template.InstanceResource.MachineSpec)
	}

	if skip, ok := cluster.Annotations[ccev1.AnnotationSkipCheckSpec]; skip != ccev1.AnnotationTrue && !ok {
		// 校验套餐是否已经全部适配
		err = c.CheckMachineSpecs(c.ctx, specs)
		if err != nil {
			logger.Errorf(c.ctx, "CheckMachineSpecs(%v) failed: %v", specs, err)
			c.errorHandlerV2(errorcode.NewInvalidMachineSpec(), errorcode.LevelByUser, err.Error())
			return
		}
	}

	if len(request.InstanceTemplates) > 0 && request.InstanceTemplates[0].AdminPassword == "" && request.InstanceTemplates[0].SSHKeyID == "" {
		/*
		   1.若本次传入的主模板指定密码，则直接使用密码
		   2.若本次传入的主模板未指定密码，则对应如下:
		     (1)修改密码: 随机生成密码 ，指定 PasswordNeedUpdate 为true
		     (2)未修改密码：指定 PasswordNeedUpdate 为false。系统默认将此前密码赋值。
		*/
		if !request.PasswordNeedUpdate {
			password := ""
			sshKey := ""
			// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
			if len(ig.Spec.InstanceTemplates) > 0 {
				password = ig.Spec.InstanceTemplates[0].AdminPassword
				sshKey = ig.Spec.InstanceTemplates[0].SSHKeyID
			} else {
				password = ig.Spec.InstanceTemplate.AdminPassword
				sshKey = ig.Spec.InstanceTemplate.SSHKeyID
			}
			for i := 0; i < len(request.InstanceTemplates); i++ {
				request.InstanceTemplates[i].AdminPassword = password
				request.InstanceTemplates[i].SSHKeyID = sshKey
			}
		}
	}

	// 校验DeployCustomConfig 需要cluster字段，放在这里校验，前面的只校验前后置脚本等不依赖cluster的字段
	f := fillspec.BaseFiller{}
	listInstanceTemplate := make([]ccetypes.InstanceTemplate, 0)
	listInstanceTemplate = append(listInstanceTemplate, request.InstanceTemplate)
	listInstanceTemplate = append(listInstanceTemplate, request.InstanceTemplates...)
	for _, instanceTemplate := range listInstanceTemplate {
		// kubelet 和 容器运行时数据目录校验
		err = fillspec.SetAndCheckDataDir(instanceTemplate.InstanceSpec.DeployCustomConfig, instanceTemplate.InstanceSpec.RuntimeType)
		if err != nil {
			logger.Errorf(c.ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
			c.errorHandlerV2(errorcode.NewInvalidDataRootDir(), errorcode.LevelByUser, "invalid data Dir, err is: %s", err.Error())
			return
		}

		_, err = f.DeployCustomConfig(c.ctx, &cluster.Spec, &instanceTemplate.InstanceSpec)
		if err != nil {
			logger.Errorf(c.ctx, "invalid DeployCustomConfig %s, InstanceTemplateID is %s, err is %s",
				utils.ToJSON(&instanceTemplate.InstanceSpec), instanceTemplate.InstanceTemplateID, err.Error())
			c.errorHandlerV2(errorcode.NewInstanceSpecPreCheckFailed(), errorcode.LevelByUser, "invalid DeployCustomConfig. err is %s", err.Error())
			return
		}
	}

	// 更新节点组配置
	if err := c.instanceGroupService.Update(c.ctx, &request.InstanceGroupSpec, instancegroup.UpdateOptions{Fields: fields}); err != nil {
		logger.Errorf(c.ctx, "Update InstanceGroup instanceTemplate failed: %v", err)
		if models.ErrTaintsInvalid.Is(err) {
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
			return
		}
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		return
	}

	if request.SyncMeta {
		if err = c.createTaskToSyncMeta(c.ctx, ig.Spec.CCEInstanceGroupID); err != nil {
			logger.Errorf(c.ctx, "create sync meta task failed: instanceGroupId:%v, err:%v", ig.Spec.CCEInstanceGroupID, err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	// 对比clusterautoscaler配置，判断是否更新集群 CA 节点组配置
	if ig.Spec.ClusterAutoscalerSpec != nil && request.InstanceGroupSpec.ClusterAutoscalerSpec != nil && *ig.Spec.ClusterAutoscalerSpec != *request.InstanceGroupSpec.ClusterAutoscalerSpec {
		if err := c.updateCAGroups(clusterID); err != nil {
			logger.Errorf(c.ctx, "updateCAGroups failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
			return
		}
	}

	logger.Infof(c.ctx, "Update InstanceGroup instanceTemplate succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupInstanceTemplateResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (r *InstanceGroupController) createTaskToSyncMeta(ctx context.Context, instanceGroupId string) error {
	log := logger.WithValues("method", "InstanceGroupReconciler.createTaskToCreateMachines").
		WithValues("instancegroup", instanceGroupId)

	ig, err := r.clients.MetaK8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupId, &metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get InstanceGroup from meta cluster, err: %v", err)
		return err
	}
	igSpec := ig.Spec

	randName := utils.RandString(8)
	var backoffLimit int32 = 30 // 30次尝试

	instanceTemplate := igSpec.InstanceTemplate
	if len(igSpec.InstanceTemplates) > 0 {
		instanceTemplate = igSpec.InstanceTemplates[0]
	}

	task := &ccev1.Task{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Namespace: consts.MetaClusterDefaultNamespace,
			Name:      "task-" + igSpec.CCEInstanceGroupID + "-sync-" + randName,
			Labels: map[string]string{
				ccetypes.ClusterIDLabelKey:                          igSpec.ClusterID,
				ccetypes.InstanceGroupIDLabelKey:                    igSpec.CCEInstanceGroupID,
				ccetypes.InstanceGroupSyncMetaTaskOperationLabelKey: igSpec.CCEInstanceGroupID,
			},
			Finalizers: []string{ccev1.DBSyncFinalizer},
		},
		Spec: ccetypes.TaskSpec{
			Handler:      "default",
			TaskType:     ccetypes.TaskTypeInstanceSyncMeta,
			UserID:       igSpec.UserID,
			AccountID:    igSpec.AccountID,
			CreatedTime:  utils.Now().Format(time.RFC3339Nano),
			BackoffLimit: &backoffLimit,
			TargetRef: &ccetypes.TaskTargetReference{
				Name:      igSpec.CCEInstanceGroupID,
				Namespace: consts.MetaClusterDefaultNamespace,
			},
			AntiAffinity: ccetypes.TaskAntiAffinity{
				ccetypes.InstanceGroupSyncMetaTaskOperationLabelKey: igSpec.CCEInstanceGroupID,
			},
			Operation: runtime.RawExtension{
				Object: &ccev1.InstanceGroupSyncMetaOperation{
					TypeMeta: metav1.TypeMeta{},
					Spec: ccetypes.InstanceGroupSyncMetaOperationSpec{
						Labels:      instanceTemplate.Labels,
						Taints:      instanceTemplate.Taints,
						Annotations: instanceTemplate.Annotations,
					},
				},
			},
		},
	}

	if _, err := r.clients.MetaK8SClient.CreateTask(ctx, task); err != nil {
		log.Errorf(ctx, "failed to create sync instanceGroup meta task, err: %v", err)
		return err
	}
	log.Infof(ctx, "sync meta task created, name: %s, instance group: %v", task.Name, igSpec.CCEInstanceGroupID)
	return nil
}

func (c *InstanceGroupController) UpdateInstanceGroupPausedStatus() {
	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}
	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}
	accountID := c.accountID

	var request ccesdk.PauseDetail
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &request); err != nil {
		logger.Errorf(c.ctx, "Unmarshal PauseDetail failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	var pauseDetail ccetypes.PauseDetail
	if err := copier.Copy(&pauseDetail, &request); err != nil {
		logger.Errorf(c.ctx, "copy pauseDetail from sdk obj to internal failed: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}

	spec := &ccetypes.InstanceGroupSpec{
		ClusterID:          clusterID,
		CCEInstanceGroupID: instanceGroupID,
		AccountID:          accountID,
	}
	if err := c.instanceGroupService.Update(c.ctx, spec, instancegroup.UpdateOptions{
		Fields:       []string{instancegroup.UpdateFieldPausedStatus},
		PausedStatus: &pauseDetail,
	}); err != nil {
		logger.Errorf(c.ctx, "Update InstanceGroup pausedStatus failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "Update InstanceGroup pausedStatus succeed")
	c.Data["json"] = ccesdk.UpdateInstanceGroupPausedStatusResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) DeleteInstanceGroup() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	var deleteInstances *bool
	deleteInstancesString := c.GetString("deleteInstances", "")
	if deleteInstancesString != "" {
		del, err := strconv.ParseBool(deleteInstancesString)
		if err != nil {
			c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid deleteInstances, %v", err).Error()), errorcode.LevelByUser, "")
		}
		deleteInstances = &del
	}

	var releaseAllResource *bool
	releaseAllResourceString := c.GetString("releaseAllResources", "")
	if releaseAllResourceString != "" {
		release, err := strconv.ParseBool(releaseAllResourceString)
		if err != nil {
			c.errorHandlerV2(errorcode.NewInvalidParam(fmt.Errorf("invalid releaseAllResource, %v", err).Error()), errorcode.LevelByUser, "")
		}
		releaseAllResource = &release
	}

	// 检查节点组是否存在, 是否用于 CA
	ig, err := c.instanceGroupService.Get(c.ctx, accountID, instanceGroupID)
	if err != nil {
		logger.Errorf(c.ctx, "get InstanceGroup failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		case models.IsNotExist(err):
			c.errorHandlerV2(errorcode.NewInstanceGroupNotFound(err.Error()), errorcode.LevelByUser, "")
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	if ig.Deleted {
		// 节点组 CRD 已经不存在 但数据库记录未标记删除的情况
		logger.Warnf(c.ctx, "instance group %s CR is already not exists in meta-cluster, but item in DB have not set to deleted")
		err := c.models.DeleteInstanceGroups(c.ctx, accountID, clusterID, []string{instanceGroupID})
		if err != nil {
			logger.Errorf(c.ctx, "DeleteInstanceGroup %s failed: %s", instanceGroupID, err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}

		logger.Infof(c.ctx, "Set InstanceGroup DB record to deleted succeed")

		c.Data["json"] = ccesdk.DeleteInstanceGroupResponse{
			CommonResponse: ccesdk.CommonResponse{
				RequestID: logger.GetRequestID(c.ctx),
			},
		}
		c.ServeJSON()
	}

	caEnable := false
	if ig.Spec != nil && ig.Spec.ClusterAutoscalerSpec != nil {
		caEnable = ig.Spec.ClusterAutoscalerSpec.Enabled
	}

	// 更新集群 CA 节点组, 仅会获取 caEnable = true 且 deleted = false 的节点组
	if caEnable == true {
		logger.Infof(c.ctx, "Disable ca before delete instanceGroupID: %s", instanceGroupID)

		// 节点组删除存在延迟, 需要先设置 caEnable=false, 以便能及时将该节点组从 CA 插件剔除
		spec := &ccetypes.InstanceGroupSpec{
			ClusterID:             clusterID,
			CCEInstanceGroupID:    instanceGroupID,
			AccountID:             accountID,
			ClusterAutoscalerSpec: ig.Spec.ClusterAutoscalerSpec, // caEnable == true 时此项必不为 nil
		}
		spec.ClusterAutoscalerSpec.Enabled = false

		if err := c.instanceGroupService.Update(c.ctx, spec, instancegroup.UpdateOptions{Fields: []string{instancegroup.UpdateFieldAutoscalerSpec}}); err != nil {
			logger.Errorf(c.ctx, "Update InstanceGroup clusterAutoscalerSpec failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}

		if err := c.updateCAGroups(clusterID); err != nil {
			logger.Errorf(c.ctx, "updateCAGroups failed: %v", err)
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error())
		}
	}

	// 检查是否存在冲突任务
	err = c.exitConflictWorkflowByInstanceGroup(c.ctx, instanceGroupID)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
	}

	logger.Infof(c.ctx, "Delete InstanceGroup begin, clusterID: %s, instanceGroupID: %s", clusterID, instanceGroupID)
	opt := instancegroup.DeleteOption{}
	if deleteInstances != nil {
		if *deleteInstances {
			opt.CleanPolicy = ccetypes.DeleteCleanPolicy
		} else {
			opt.CleanPolicy = ccetypes.RemainCleanPolicy
		}
	}
	if releaseAllResource != nil {
		opt.InstanceDeleteOption = &ccetypes.DeleteOption{
			MoveOut:           !*releaseAllResource,
			DeleteResource:    *releaseAllResource,
			DeleteCDSSnapshot: *releaseAllResource,
		}
	}

	//检查是否有正在执行扩缩容task
	if err := c.existConflictTaskByInstanceGroup(c.ctx, instanceGroupID); err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
	}

	if err := c.instanceGroupService.Delete(c.ctx, accountID, instanceGroupID, opt); err != nil {
		logger.Errorf(c.ctx, "Delete InstanceGroup failed: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		case strings.Contains(err.Error(), "not exists") || models.IsNotExist(err):
			c.errorHandlerV2(errorcode.NewInstancegroupNotExist(err.Error()), errorcode.LevelByUser, "")
			return
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}
	logger.Infof(c.ctx, "Delete InstanceGroup succeed")

	c.Data["json"] = ccesdk.DeleteInstanceGroupResponse{
		CommonResponse: ccesdk.CommonResponse{
			RequestID: logger.GetRequestID(c.ctx),
		},
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) GetInstanceGroupCRD() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	instanceGroup, err := c.instanceGroupService.GetCRD(c.ctx, accountID, instanceGroupID)
	if err != nil {
		logger.Errorf(c.ctx, "get instanceGroup CRD failed, err: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "get instanceGroup CRD succeed, instanceGroup: %s", utils.ToJSON(instanceGroup))
	c.Data["json"] = map[string]interface{}{
		"requestID":     logger.GetRequestID(c.ctx),
		"instanceGroup": instanceGroup,
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) UpdateInstanceGroupCRD() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	var instanceGroup ccev1.InstanceGroup
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &instanceGroup); err != nil {
		logger.Errorf(c.ctx, "Unmarshal InstanceGroup failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	if instanceGroup.Spec.AccountID != accountID ||
		instanceGroup.Spec.CCEInstanceGroupID != instanceGroupID ||
		instanceGroup.Name != instanceGroup.Spec.CCEInstanceGroupID ||
		instanceGroup.Spec.ClusterID != clusterID {

		logger.WithValues("accountID", accountID).
			WithValues("clusterID", clusterID).
			WithValues("instanceGroupID", instanceGroupID).
			Errorf(c.ctx, "params conflict")
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Params conflict")
	}

	listInstanceTemplate := make([]ccetypes.InstanceTemplate, 0)
	listInstanceTemplate = append(listInstanceTemplate, instanceGroup.Spec.InstanceTemplate)
	listInstanceTemplate = append(listInstanceTemplate, instanceGroup.Spec.InstanceTemplates...)
	for _, instanceTemplate := range listInstanceTemplate {
		// kubelet 和 容器运行时数据目录校验
		err := fillspec.SetAndCheckDataDir(instanceTemplate.InstanceSpec.DeployCustomConfig, instanceTemplate.InstanceSpec.RuntimeType)
		if err != nil {
			logger.Errorf(c.ctx, "SetAndCheckDataDir failed, err is: %s", err.Error())
			c.errorHandlerV2(errorcode.NewInvalidDataRootDir(), errorcode.LevelByUser, "invalid data Dir, err is: %s", err.Error())
			return
		}
	}

	if err := c.instanceGroupService.UpdateCRD(c.ctx, &instanceGroup); err != nil {
		logger.WithValues("instanceGroup", utils.ToJSON(instanceGroup)).
			Errorf(c.ctx, "failed to update instanceGroup CRD, err: %v", err)
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "update instanceGroup CRD succeed, instanceGroup: %s", utils.ToJSON(instanceGroup))
	c.Data["json"] = map[string]interface{}{
		"requestID": logger.GetRequestID(c.ctx),
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) ScaleUpInstanceGroup() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	upToReplicas, err := c.GetInt("upToReplicas", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid upToReplicas"), errorcode.LevelByUser, "")
	}

	upReplicas, err := c.GetInt("upReplicas", 0)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam("invalid upReplicas"), errorcode.LevelByUser, "")
	}

	err = checkScaleUpParams(upToReplicas, upReplicas)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, "")
	}

	taskID, err := c.instanceGroupService.ScaleUp(c.ctx, accountID, instanceGroupID, upToReplicas, upReplicas)
	if err != nil {
		logger.WithValues("instanceGroup", instanceGroupID).
			Errorf(c.ctx, "failed to scale up instanceGroup to %d, err: %v", upToReplicas, err)
		switch {
		case models.ErrHPASNotImplemented.Is(err):
			c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS scaleup not implemented"), errorcode.LevelByAdmin, "")
		case strings.Contains(err.Error(), "Exceeding the number of nodes"):
			c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(err.Error()), errorcode.LevelByAdmin, err.Error())
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "instanceGroup scaling up task created, taskID: %s", taskID)
	c.Data["json"] = map[string]interface{}{
		"requestID": logger.GetRequestID(c.ctx),
		"taskID":    taskID,
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) AttachInstancesToInstanceGroup() {
	accountID := c.accountID

	clusterID := c.GetPathParams(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
		return
	}

	instanceGroupID := c.GetPathParams(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
		return
	}

	var param ccesdk.ScaleUpExistInstanceGroupOption
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &param); err != nil {
		logger.Errorf(c.ctx, "Unmarshal ScaleUpExistInstanceGroupOption failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
		return
	}

	var instanceCount int
	if param.Incluster {
		if len(param.ExistedInstancesInCluster) == 0 {
			c.errorHandlerV2(errorcode.NewInvalidParam("empty ExistedInstancesInCluster"), errorcode.LevelByUser, "")
			return
		}
	} else {
		if len(param.ExistedInstances) == 0 {
			c.errorHandlerV2(errorcode.NewInvalidParam("empty ExistedInstances"), errorcode.LevelByUser, "")
			return
		}
		if len(param.ExistedInstances) > maxAttachInstanceCount {
			c.errorHandlerV2(errorcode.NewInstancesCountBeyondLimit(), errorcode.LevelByUser,
				"InstanceCount should be less than %d, use InstanceGroup instead", maxAttachInstanceCount)
			return
		}
		// 只有不在集群中时，才需要进行配额校验
		instanceCount = len(param.ExistedInstances)

		// 检查是否配置弱密码
		for _, instance := range param.ExistedInstances {
			if err := c.CheckPasswordLength(instance.InstanceSpec.AdminPassword); err != nil {
				c.errorHandlerV2(errorcode.NewTooLongPassword("password length should be less than 44"), errorcode.LevelByUser, "")
				return
			}
			if instance.InstanceSpec.AdminPassword != "" && c.CheckInternalUserWeakPassword(c.ctx, instance.InstanceSpec.AdminPassword) {
				c.errorHandlerV2(errorcode.NewInternalWeakPassword("weak password is not allowed"), errorcode.LevelByUser, "")
				return
			}
		}
	}
	//检查是否有正在执行扩缩容task
	if err := c.existConflictTaskByInstanceGroup(c.ctx, instanceGroupID); err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
		return
	}

	taskID, err := c.instanceGroupService.ScaleUpExistNode(c.ctx, c.service, accountID, clusterID, instanceGroupID, param, instanceCount)
	if err != nil {
		logger.WithValues("instanceGroup", instanceGroupID).
			Errorf(c.ctx, "failed to scale up instanceGroup, err: %v", err)

		// 使用系统化的错误处理
		c.handleScaleUpError(err)
		return
	}

	logger.Infof(c.ctx, "instanceGroup scaling up task created, taskID: %s", taskID)
	c.Data["json"] = map[string]interface{}{
		"requestID": logger.GetRequestID(c.ctx),
		"taskID":    taskID,
	}
	c.ServeJSON()
}

// UpgradeInstanceGroup 获取节点组可升级组件列表
func (c *InstanceGroupController) UpgradeInstanceGroup() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}
	result, err := c.instanceGroupService.GetUpgradeComponentVersions(c.ctx, accountID, clusterID, instanceGroupID)
	if err != nil {
		logger.WithValues("instanceGroup", instanceGroupID).
			Errorf(c.ctx, "failed to get upgrade componentVersions, err: %v", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
	}
	c.Data["json"] = map[string]interface{}{
		"requestID": logger.GetRequestID(c.ctx),
		"result":    result,
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) ScaleDownInstanceGroup() {
	accountID := c.accountID

	clusterID := c.Ctx.Input.Param(":clusterID")
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty clusterID"), errorcode.LevelByUser, "")
	}

	instanceGroupID := c.Ctx.Input.Param(":instanceGroupID")
	if instanceGroupID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam("empty instanceGroupID"), errorcode.LevelByUser, "")
	}

	var param ccesdk.RemoveInstancesFromInstanceGroup
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, &param); err != nil {
		logger.Errorf(c.ctx, "Unmarshal RemoveInstancesFromInstanceGroup failed: %v", err)
		c.errorHandlerV2(errorcode.NewMalformedJSON(), errorcode.LevelByUser, "Param format not json")
	}

	opt := instancegroup.ScaleDownOption{
		CleanPolicy:          ccetypes.CleanPolicy(param.CleanPolicy),
		InstanceDeleteOption: nil,
	}
	if param.DeleteOption != nil {
		opt.InstanceDeleteOption = &ccetypes.DeleteOption{
			DrainNode:         param.DeleteOption.DrainNode,
			MoveOut:           param.DeleteOption.MoveOut,
			DeleteResource:    param.DeleteOption.DeleteResource,
			DeleteCDSSnapshot: param.DeleteOption.DeleteCDSSnapshot,
			Rebuild:           param.DeleteOption.Rebuild,
		}
	}

	// 检查是否存在冲突任务
	err := c.exitConflictWorkflowByInstanceGroup(c.ctx, instanceGroupID)
	if err != nil {
		c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
	}

	// 伸缩中的节点组不允许删除节点
	if errCode, logLevel, format := c.existConflictWithInstanceGroupScaling(c.ctx, clusterID, accountID, instanceGroupID, param.InstancesToBeRemoved, param.K8sNodesToBeRemoved); errCode != nil {
		c.errorHandlerV2(errCode, logLevel, format)
	}

	taskID, err := c.instanceGroupService.ScaleDown(c.ctx, accountID, instanceGroupID, param.InstancesToBeRemoved, param.K8sNodesToBeRemoved, opt)
	if err != nil {
		logger.WithValues("instanceGroup", instanceGroupID).
			Errorf(c.ctx, "failed to remove instances: %v from instanceGroup, err: %v", param.InstancesToBeRemoved, err)
		if len(param.K8sNodesToBeRemoved) > 0 {
			logger.WithValues("instanceGroup", instanceGroupID).
				Errorf(c.ctx, "failed to remove k8sNodes: %v from instanceGroup, err: %v", param.K8sNodesToBeRemoved, err)
		}
		switch {
		// TODO: 错误类型判断，需要枚举一下所有可能的错误
		//case models.IsAlreadyExists(err):
		//case models.IsNotExist(err):
		case models.ErrIgInstanceRemovalInProgress.Is(err):
			c.errorHandlerV2(errorcode.NewInvalidParam(err.Error()), errorcode.LevelByUser, err.Error())
		default:
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
		}
	}

	logger.Infof(c.ctx, "instanceGroup scaling down task created, taskID: %s", taskID)
	c.Data["json"] = map[string]interface{}{
		"requestID": logger.GetRequestID(c.ctx),
		"taskID":    taskID,
	}
	c.ServeJSON()
}

func (c *InstanceGroupController) existConflictWithInstanceGroupScaling(ctx context.Context,
	clusterID, accountID, instanceGroupID string, instancesToBeRemoved, k8sNodesToBeRemoved []string) (code *errorcode.ErrorCode, level errorcode.Level, format string) {
	cceInstanceIDs, err := c.getInstanceToBeRemoved(ctx, clusterID, accountID, instanceGroupID, instancesToBeRemoved, k8sNodesToBeRemoved)
	if err != nil {
		logger.Errorf(c.ctx, "get instance to be removed failed, err: %v", err)
		return errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, err.Error()
	}
	instanceIdToInstanceGroupId := make(map[string]string)
	for _, instanceID := range cceInstanceIDs {
		instanceIdToInstanceGroupId[instanceID] = instanceGroupID
	}
	return existConflictWithInstanceGroupScaling(ctx, c.clientSet.MetaClient, clusterID, instanceIdToInstanceGroupId)
}

func (c *InstanceGroupController) getInstanceToBeRemoved(ctx context.Context,
	clusterID, accountID, instanceGroupID string, instancesToBeRemoved, k8sNodesToBeRemoved []string) ([]string, error) {
	if len(instancesToBeRemoved) == 0 && len(k8sNodesToBeRemoved) == 0 {
		return nil, nil
	}
	cceInstanceIDs := make([]string, 0)
	for _, instanceID := range instancesToBeRemoved {
		cceInstanceIDs = append(cceInstanceIDs, instanceID)
	}

	if len(k8sNodesToBeRemoved) > 0 {
		// 查询 Cluster
		cluster, err := c.clientSet.Model.GetCluster(ctx, clusterID, accountID)
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %v", err)
			return nil, err
		}
		if cluster == nil {
			logger.Errorf(ctx, "cluster %s not exist", clusterID)
			return nil, fmt.Errorf("cluster %s not exist", clusterID)
		}
		// 数据库查询全量 Instance
		list, err := c.clientSet.Model.GetInstancesByInstanceGroupID(ctx, accountID, clusterID, instanceGroupID,
			"", "", "", "", 100000, 1)
		if err != nil {
			logger.Errorf(ctx, "GetInstancesByInstanceGroupID failed: %v", err)
			return nil, err
		}
		for i := 0; i < len(k8sNodesToBeRemoved); i++ {
			find := false
			for j := 0; j < len(list.Items); j++ {
				k8sNodeName := utils.GetNodeName(ctx, list.Items[j].Status.Machine.VPCIP, list.Items[j].Status.Machine.Hostname, list.Items[j].Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)
				if k8sNodeName == k8sNodesToBeRemoved[i] {
					find = true
					cceInstanceIDs = append(cceInstanceIDs, list.Items[j].Spec.CCEInstanceID)
				}
			}
			if !find {
				return nil, fmt.Errorf("k8sNode %s not exist", k8sNodesToBeRemoved[i])
			}
		}
	}
	return cceInstanceIDs, nil
}

func (c *InstanceGroupController) existConflictTaskByInstanceGroup(ctx context.Context, instanceGroupID string) error {
	logger.Infof(ctx, "check instanceGroup %s exist conflict task", instanceGroupID)
	if len(instanceGroupID) <= 0 {
		return nil
	}
	labelSelector := metav1.LabelSelector{
		MatchLabels: map[string]string{
			ccetypes.InstanceGroupIDLabelKey:                          instanceGroupID,
			ccetypes.InstanceGroupReplicasTaskRepairOperationLabelKey: instanceGroupID,
		},
	}
	labelSelectorStr := metav1.FormatLabelSelector(&labelSelector)

	taskList, err := c.clientSet.MetaClient.ListTask(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: labelSelectorStr,
	})
	if err != nil {
		logger.Errorf(ctx, "list tasks failed: %v", err)
		return err
	}
	for _, task := range taskList.Items {
		// 校验是否有正在运行的任务
		if task.Status.Phase == ccetypes.TaskPhaseFinished || task.Status.Phase == ccetypes.TaskPhaseAborted {
			logger.Infof(ctx, "task %s, status is %s, skip check", task.Name, task.Status.Phase)
			continue
		} else {
			return fmt.Errorf("instanceGroup %s exit task %s is %s, please wait for it to finish", instanceGroupID, task.Name, task.Status.Phase)
		}
	}
	return nil
}

// exitConflictWorkflowByInstanceGroup 检查节点组是否存在正在运行中的workflow
func (c *InstanceGroupController) exitConflictWorkflowByInstanceGroup(ctx context.Context, instanceGroupID string) error {
	if len(instanceGroupID) <= 0 {
		return nil
	}
	workflowList, err := c.clientSet.MetaClient.ListWorkflows(ctx, ccetypes.NamespaceDefault, &metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, instanceGroupID),
	})
	if err != nil {
		logger.Errorf(ctx, "list workflows failed: %v", err)
		return err
	}
	for _, workflow := range workflowList.Items {
		// 校验是否存在冲突任务
		if workflow.Spec.WorkflowType != ccetypes.WorkflowTypeUpgradeNodes {
			continue
		}
		// 处于暂停、升级中、pending、确认中和删除中这几个状态时，不允许删除节点组内节点
		if workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePaused || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseUpgrading ||
			workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePending || workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseVerifying ||
			workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseDeleting {
			return fmt.Errorf("instanceGroup %s exit workflow %s is %s", instanceGroupID, workflow.Name, workflow.Status.WorkflowPhase)
		}
	}

	return nil
}

func (c *InstanceGroupController) checkCAInit(clusterID string) (bool, error) {
	k8sClient, err := getClientSet(c.ctx, clusterID, c.models)
	if err != nil {
		logger.Errorf(c.ctx, "getClientSet failed: %v", err)
		return false, err
	}

	pluginClient, err := getPluginClient(c.ctx, clusterID, c.models, c.clients.K8SClient, c.config.PluginConfig)
	if err != nil {
		logger.Errorf(c.ctx, "getPluginClient failed: %v", err)
		return false, err
	}

	client, err := autoscaler.NewClient(c.ctx, c.accountID, c.userID, c.models, c.clients, k8sClient, pluginClient, c.config, c.clientSet)
	if err != nil {
		logger.Errorf(c.ctx, "new autoscaler client failed: %v", err)
		return false, err
	}

	caConfig, err := client.GetAutoscaler(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "get autoscaler failed: %v", err)
		return false, err
	}
	if caConfig == nil {
		return false, nil
	}

	return true, nil
}

// updateCAGroups - 更新 CA 节点组
func (c *InstanceGroupController) updateCAGroups(clusterID string) error {
	k8sClient, err := getClientSet(c.ctx, clusterID, c.models)
	if err != nil {
		logger.Errorf(c.ctx, "getClientSet failed: %v", err)
		return err
	}

	pluginClient, err := getPluginClient(c.ctx, clusterID, c.models, c.clients.K8SClient, c.config.PluginConfig)
	if err != nil {
		logger.Errorf(c.ctx, "getPluginClient failed: %v", err)
		return err
	}

	client, err := autoscaler.NewClient(c.ctx, c.accountID, c.userID, c.models, c.clients, k8sClient, pluginClient, c.config, c.clientSet)
	if err != nil {
		logger.Errorf(c.ctx, "new autoscaler client failed: %v", err)
		return err
	}

	err = client.UpdateCAGroups(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "UpdateCAGroups failed: %v", err)
		return err
	}

	return nil
}

// generateInstanceSpec
// 注意：！！！2025-03-06 ！！！ 该方法会将meta集群instancegroup CR中的 InstanceTemplate中的DeployCustomConfig的一些参数进行前端兼容展示。
// 目前调用该方法的地方都是前端返回，如果该方法用在其他地方需要考虑该改动
//
//	例如：kubeReserved，原来CR中写成 "1",该方法中会修改为 1000m，单位统一为m和Mi。
//
// 2025-03-06 统一修改的后端字段：kubeReserved 和 systemReserved
func (c *InstanceGroupController) generateInstanceSpec(ctx context.Context, instanceTemplate ccetypes.InstanceTemplate) ccesdk.InstanceTemplate {
	// 前端使用categoryKey做分类，内部镜像的imageType为System，前端没办法区分，所以这里为保证回显正确，判断下内部镜像
	if instanceTemplate.InstanceOS.SpecialVersion == bccimage.SpecialVersionInternal {
		instanceTemplate.InstanceOS.ImageType = bccimage.ImageTypeInternal
	}
	scaleDown := instanceTemplate.ScaleDownDisabled
	instanceSpecSdk := ccesdk.InstanceSpec{
		InstanceTemplateID: instanceTemplate.InstanceTemplateID,
		CCEInstanceID:      instanceTemplate.CCEInstanceID,
		InstanceName:       instanceTemplate.InstanceName,
		RuntimeType:        instanceTemplate.RuntimeType,
		RuntimeVersion:     instanceTemplate.RuntimeVersion,
		ClusterID:          instanceTemplate.ClusterID,
		ClusterRole:        instanceTemplate.ClusterRole,
		UserID:             instanceTemplate.UserID,
		InstanceGroupID:    instanceTemplate.InstanceGroupID,
		InstanceGroupName:  instanceTemplate.InstanceGroupName,
		MachineType:        instanceTemplate.MachineType,
		InstanceType:       instanceTemplate.InstanceType,
		BBCOption:          &instanceTemplate.BBCOption,
		VPCConfig: ccesdk.VPCConfig{
			VPCID:             instanceTemplate.VPCConfig.VPCID,
			VPCSubnetID:       instanceTemplate.VPCConfig.VPCSubnetID,
			SecurityGroupID:   instanceTemplate.VPCConfig.SecurityGroupID,
			SecurityGroupType: string(instanceTemplate.VPCConfig.SecurityGroupType),
			SecurityGroup: ccesdk.SecurityGroup{
				EnableCCERequiredSecurityGroup: instanceTemplate.SecurityGroup.EnableCCERequiredSecurityGroup,
				EnableCCEOptionalSecurityGroup: instanceTemplate.SecurityGroup.EnableCCEOptionalSecurityGroup,
				CustomSecurityGroupIDs:         instanceTemplate.SecurityGroup.CustomSecurityGroupIDs,
			},
			VPCSubnetType:     instanceTemplate.VPCConfig.VPCSubnetType,
			VPCSubnetCIDR:     instanceTemplate.VPCSubnetCIDR,
			VPCSubnetCIDRIPv6: instanceTemplate.VPCSubnetCIDRIPv6,
			AvailableZone:     string(instanceTemplate.AvailableZone),
			SecurityGroups:    instanceTemplate.SecurityGroups,
		},
		Bid:                           instanceTemplate.Bid,
		BidOption:                     instanceTemplate.BidOption,
		InstanceResource:              instanceTemplate.InstanceResource,
		DeployCustomConfig:            instanceTemplate.DeployCustomConfig,
		ImageID:                       instanceTemplate.ImageID,
		InstanceOS:                    instanceTemplate.InstanceOS,
		UserData:                      instanceTemplate.UserData,
		CheckGPUDriver:                instanceTemplate.CheckGPUDriver,
		NeedEIP:                       instanceTemplate.NeedEIP,
		EIPOption:                     &instanceTemplate.EIPOption,
		SSHKeyID:                      instanceTemplate.SSHKeyID,
		InstanceChargingType:          instanceTemplate.InstanceChargingType,
		InstancePreChargingOption:     instanceTemplate.InstancePreChargingOption,
		DeleteOption:                  instanceTemplate.DeleteOption,
		Tags:                          instanceTemplate.Tags,
		Labels:                        instanceTemplate.Labels,
		Taints:                        instanceTemplate.Taints,
		Annotations:                   instanceTemplate.Annotations,
		CCEInstancePriority:           instanceTemplate.CCEInstancePriority,
		DeploySetID:                   instanceTemplate.DeploySetID,
		DeploySetIDs:                  instanceTemplate.DeploySetIDs,
		AutoSnapshotID:                instanceTemplate.AutoSnapshotID,
		RelationTag:                   instanceTemplate.RelationTag,
		IsOpenHostnameDomain:          instanceTemplate.IsOpenHostnameDomain,
		NvidiaContainerToolkitVersion: instanceTemplate.NvidiaContainerToolkitVersion,
		XPUContainerToolkitVersion:    instanceTemplate.XPUContainerToolkitVersion,
		EhcClusterID:                  instanceTemplate.EhcClusterID,
		ScaleDownDisabled:             &scaleDown,
		HPASOption:                    instanceTemplate.HPASOption,
	}

	// DeployCustomConfig 字段前端展示兼容，目前适配字段 kubeReserved 和 systemReserved，单位统一m和Mi
	if len(instanceSpecSdk.DeployCustomConfig.KubeReserved) > 0 {
		kubeReserved, err := fillspec.AdaptReservedList(instanceSpecSdk.DeployCustomConfig.KubeReserved)
		if err != nil {
			// 兼容异常情况，这里目前只打印错误信息，暂不报错
			logger.Errorf(context.TODO(), fmt.Sprintf("fillspec.AdaptReservedList KubeReserved failed: %v", err))
		} else {
			instanceSpecSdk.DeployCustomConfig.KubeReserved = kubeReserved
		}
	}
	if len(instanceSpecSdk.DeployCustomConfig.SystemReserved) > 0 {
		systemReserved, err := fillspec.AdaptReservedList(instanceSpecSdk.DeployCustomConfig.SystemReserved)
		if err != nil {
			// 兼容异常情况，这里目前只打印错误信息，暂不报错
			logger.Errorf(context.TODO(), fmt.Sprintf("fillspec.AdaptReservedList SystemReserved failed: %v", err))
		} else {
			instanceSpecSdk.DeployCustomConfig.SystemReserved = systemReserved
		}
	}
	var err error
	if instanceSpecSdk.SSHKeyID != "" {
		instanceSpecSdk.SSHKeyName, err = c.getSSHKeyName(ctx, instanceSpecSdk.SSHKeyID)
		if err != nil {
			logger.Errorf(c.ctx, "generate instance spec getSSHKeyName failed: %v", err)
		}
	}
	if instanceSpecSdk.AutoSnapshotID != "" {
		instanceSpecSdk.AutoSnapshotName, err = c.getAspName(ctx, instanceSpecSdk.AutoSnapshotID)
		if err != nil {
			logger.Errorf(c.ctx, "generate instance spec getAspName failed: %v", err)
		}
	}

	// 这些用户改不了的默认标签不显示
	delete(instanceSpecSdk.Labels, ccetypes.ClusterIDLabelKey)
	delete(instanceSpecSdk.Labels, ccetypes.InstanceGroupIDLabelKey)
	delete(instanceSpecSdk.Labels, ccetypes.ClusterRoleLabelKey)
	delete(instanceSpecSdk.Labels, ccetypes.ClusterAutoscalerEnabledLabelKey)
	delete(instanceSpecSdk.Labels, ccetypes.InstanceKubeletDirLabelKey)
	return ccesdk.InstanceTemplate{InstanceSpec: instanceSpecSdk}
}

func (c *InstanceGroupController) instanceGroupModelToSDK(ctx context.Context, ig *models.InstanceGroup, igStatus *ccesdk.InstanceGroupStatus) *ccesdk.InstanceGroup {
	igSDK := &ccesdk.InstanceGroup{
		Spec: &ccesdk.InstanceGroupSpec{
			CCEInstanceGroupID:    ig.Spec.CCEInstanceGroupID,
			InstanceGroupName:     ig.Spec.InstanceGroupName,
			ClusterID:             ig.Spec.ClusterID,
			ClusterRole:           ig.Spec.ClusterRole,
			UserID:                ig.Spec.UserID,
			AccountID:             ig.Spec.AccountID,
			ShrinkPolicy:          ccesdk.ShrinkPolicy(ig.Spec.ShrinkPolicy),
			UpdatePolicy:          ccesdk.UpdatePolicy(ig.Spec.UpdatePolicy),
			CleanPolicy:           ccesdk.CleanPolicy(ig.Spec.CleanPolicy),
			Replicas:              ig.Spec.Replicas,
			DefaultSecurityGroups: ig.Spec.DefaultSecurityGroups,
			SecurityGroupType:     ig.Spec.SecurityGroupType,
		},
		Status: &ccesdk.InstanceGroupStatus{
			ActualReplicas:   ig.Status.ActualReplicas,
			ReadyReplicas:    ig.Status.ReadyReplicas,
			ScalingReplicas:  ig.Status.ScalingReplicas,
			DeletingReplicas: ig.Status.DeletingReplicas,
			OtherReplicas:    ig.Status.OtherReplicas,
		},
		CreatedAt: ig.CreatedAt,
		DeletedAt: ig.DeletedAt,
		Deleted:   ig.Deleted,
	}
	if igStatus != nil {
		igSDK.Status.ReadyReplicas = igStatus.ReadyReplicas
		igSDK.Status.NotReadyReplicas = igStatus.NotReadyReplicas
	}
	// TODO 使用ccetypes.HandleSpecTemplatesAndResetTemplate方法处理，减少重复代码
	igSDK.Spec.InstanceTemplate = c.generateInstanceSpec(ctx, ig.Spec.InstanceTemplate)
	if len(ig.Spec.InstanceTemplates) > 0 {
		igSDK.Spec.InstanceTemplates = []ccesdk.InstanceTemplate{}
		for i := 0; i < len(ig.Spec.InstanceTemplates); i++ {
			instanceTemplate := ig.Spec.InstanceTemplates[i]
			instanceTemplateSdk := c.generateInstanceSpec(ctx, instanceTemplate)
			if instanceTemplateSdk.VPCConfig.SecurityGroupType == "" {
				instanceTemplateSdk.VPCConfig.SecurityGroupType = "normal"
			}
			igSDK.Spec.InstanceTemplates = append(igSDK.Spec.InstanceTemplates, instanceTemplateSdk)
		}
	} else {
		if igSDK.Spec.InstanceTemplate.VPCConfig.SecurityGroupType == "" {
			igSDK.Spec.InstanceTemplate.VPCConfig.SecurityGroupType = "normal"
		}
	}

	if ig.Spec.ClusterAutoscalerSpec != nil {
		igSDK.Spec.ClusterAutoscalerSpec = &ccesdk.ClusterAutoscalerSpec{
			Enabled:              ig.Spec.ClusterAutoscalerSpec.Enabled,
			MinReplicas:          ig.Spec.ClusterAutoscalerSpec.MinReplicas,
			MaxReplicas:          ig.Spec.ClusterAutoscalerSpec.MaxReplicas,
			ScalingGroupPriority: ig.Spec.ClusterAutoscalerSpec.ScalingGroupPriority,
		}
	}

	if ig.Status.Pause != nil {
		igSDK.Status.Pause = &ccesdk.PauseDetail{
			Paused: ig.Status.Pause.Paused,
			Reason: ig.Status.Pause.Reason,
		}
	}

	if ig.Spec.RemedyRulesBinding != nil {
		igSDK.Spec.RemedyRulesBinding = &ccetypes.RemedyRulesBinding{
			EnableCheckANDRemedy: ig.Spec.RemedyRulesBinding.EnableCheckANDRemedy,
			RemedyRuleID:         ig.Spec.RemedyRulesBinding.RemedyRuleID,
		}
	}
	if ig.Spec.IAMRole != nil {
		igSDK.Spec.IAMRole = ig.Spec.IAMRole
	}

	return igSDK
}

func checkScaleUpParams(upToReplicas int, upReplicas int) error {
	if upToReplicas == 0 && upReplicas == 0 {
		return fmt.Errorf("invalid upReplicas and upToReplicas: one and only one of upReplicas/upToReplicas must be zero")
	}
	if upToReplicas != 0 && upReplicas != 0 {
		return fmt.Errorf("invalid upReplicas and upToReplicas: one and only one of upReplicas/upToReplicas must be zero")
	}
	if upToReplicas < 0 || upReplicas < 0 {
		return fmt.Errorf("invalid upReplicas and upToReplicas: cannot be negative")
	}
	return nil
}

// handleScaleUpError 系统化处理扩容相关错误
func (c *InstanceGroupController) handleScaleUpError(err error) {
	// 1. 优先处理 errorcode 包装的错误
	var e *errorcode.ErrorCode
	if errors.As(err, &e) {
		c.errorHandlerV2(e, errorcode.LevelByUser, "")
		return
	}

	// 2. 处理安全组相关错误
	if c.handleSecurityGroupErrors(err) {
		return
	}

	// 3. 处理配额和限制错误
	if c.handleQuotaAndLimitErrors(err) {
		return
	}

	// 4. 处理权限和资源不存在错误
	if c.handlePermissionAndResourceErrors(err) {
		return
	}

	// 5. 处理业务逻辑错误
	if c.handleScaleUpBusinessErrors(err) {
		return
	}

	// 6. 处理 BCE SDK 错误
	if c.handleBCEError(err) {
		return
	}

	// 7. 默认处理：内部服务器错误
	c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
}

// handleBCEError 处理 BCE SDK 相关错误
func (c *InstanceGroupController) handleBCEError(err error) bool {
	// 导入 BCE 错误包
	if bceErr, ok := err.(*bceerror.Error); ok {
		switch bceErr.StatusCode {
		case 404:
			// 404 错误：资源不存在
			switch bceErr.Code {
			case "NoSuchObject", "NotFound":
				c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, err.Error())
				return true
			}
		case 403:
			// 403 错误：权限不足
			c.errorHandlerV2(errorcode.NewAccessDenied(), errorcode.LevelByUser, err.Error())
			return true
		case 400:
			// 400 错误：请求参数错误
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
			return true
		case 409:
			// 409 错误：资源冲突
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
			return true
		case 429:
			// 429 错误：请求过于频繁
			c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
			return true
		case 500, 502, 503, 504:
			// 5xx 错误：服务端错误
			c.errorHandlerV2(errorcode.NewInternalServerError(err.Error()), errorcode.LevelByAdmin, "")
			return true
		}
	}

	// 使用 BCE 包的 IsNotFound 函数进行检查
	if bce.IsNotFound(err) {
		c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, err.Error())
		return true
	}

	return false
}

// handleSecurityGroupErrors 处理安全组相关错误
func (c *InstanceGroupController) handleSecurityGroupErrors(err error) bool {
	// 1. 安全组数量超限错误
	if errors.Is(err, securitygroup.NewSgCountLimitError()) {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, err.Error())
		return true
	}

	errMsg := err.Error()
	// 2. 安全组获取失败
	if strings.Contains(errMsg, "GetSecurityGroups failed") {
		c.errorHandlerV2(errorcode.NewInternalServerError(errMsg), errorcode.LevelByAdmin, "")
		return true
	}

	// 3. 安全组类型错误
	if strings.Contains(errMsg, "invalid security group type") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, errMsg)
		return true
	}

	// 4. 安全组ID为空
	if strings.Contains(errMsg, "security group ids is empty") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, errMsg)
		return true
	}

	return false
}

// handleQuotaAndLimitErrors 处理配额和限制错误
func (c *InstanceGroupController) handleQuotaAndLimitErrors(err error) bool {
	errMsg := err.Error()

	// 1. 节点配额超限
	if strings.Contains(errMsg, "exceed cluster node quota") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, errMsg)
		return true
	}

	// 2. 集群规格限制
	if strings.Contains(errMsg, "Exceeding the number of nodes by the cluster flavor limits") {
		c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(errMsg), errorcode.LevelByAdmin, errMsg)
		return true
	}

	// 3. 检查集群节点数量限制失败
	if strings.Contains(errMsg, "check cce cluster node num limit failed") {
		c.errorHandlerV2(errorcode.NewInternalServerError(errMsg), errorcode.LevelByAdmin, "")
		return true
	}

	return false
}

// handlePermissionAndResourceErrors 处理权限和资源不存在错误
func (c *InstanceGroupController) handlePermissionAndResourceErrors(err error) bool {
	errMsg := err.Error()

	// 1. 权限拒绝
	if strings.Contains(errMsg, "permision deny") {
		c.errorHandlerV2(errorcode.NewAccessDenied(), errorcode.LevelByUser, "权限不足")
		return true
	}

	// 2. 集群不存在
	if strings.Contains(errMsg, "GetCluster failed") {
		c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, "集群不存在")
		return true
	}

	// 3. 节点组不存在
	if strings.Contains(errMsg, "failed to get instanceGroup from meta cluster") {
		c.errorHandlerV2(errorcode.NewInstanceGroupNotFound(errMsg), errorcode.LevelByUser, "")
		return true
	}

	// 4. 实例不存在
	if strings.Contains(errMsg, "not found") || strings.Contains(errMsg, "do not exist") {
		c.errorHandlerV2(errorcode.NewNoSuchObject(), errorcode.LevelByUser, "指定的实例不存在")
		return true
	}

	return false
}

// handleScaleUpBusinessErrors 处理扩容特定的业务逻辑错误
func (c *InstanceGroupController) handleScaleUpBusinessErrors(err error) bool {
	errMsg := err.Error()

	// 1. 自动伸缩冲突
	if strings.Contains(errMsg, "ClusterAutoscaler is enabled") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "节点组已开启自动伸缩，无法手动扩容")
		return true
	}

	// 2. 没有可加入的实例
	if strings.Contains(errMsg, "not exist instance to attacch") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, "没有可加入的实例")
		return true
	}

	// 3. 角色绑定失败
	if strings.Contains(errMsg, "bind role") && strings.Contains(errMsg, "failed") {
		c.errorHandlerV2(errorcode.NewInternalServerError(errMsg), errorcode.LevelByAdmin, "")
		return true
	}

	// 4. 部分成功错误
	if models.ErrPartialSuccess.Is(err) {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, errMsg)
		return true
	}

	// 5. 任务创建失败
	if strings.Contains(errMsg, "failed to create scaling up task") {
		c.errorHandlerV2(errorcode.NewInternalServerError(errMsg), errorcode.LevelByAdmin, "")
		return true
	}

	return false
}

// handleKnownBusinessErrors 处理已知的业务错误模式
func (c *InstanceGroupController) handleKnownBusinessErrors(err error) bool {
	errMsg := err.Error()

	// 1. 处理节点数量超限错误
	if strings.Contains(errMsg, "Exceeding the number of nodes") {
		c.errorHandlerV2(errorcode.NewCCEInstanceNumberExceed(errMsg), errorcode.LevelByAdmin, errMsg)
		return true
	}

	// 2. 处理缩容相关错误
	if strings.Contains(errMsg, "could not scale down to") {
		c.errorHandlerV2(errorcode.NewInvalidParam(errMsg), errorcode.LevelByUser, errMsg)
		return true
	}

	// 3. 处理自动续费时间单位错误
	if strings.Contains(errMsg, "AutoRenewTimeUnit") {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByUser, errMsg)
		return true
	}

	// 4. 处理工作流冲突错误
	if models.IsErrWorkflowConflict(err) {
		c.errorHandlerV2(errorcode.NewMultiWorkflowExistInCluster(errMsg), errorcode.LevelByUser, "")
		return true
	}

	// 5. 处理 HPAS 不支持的操作
	if models.ErrHPASNotImplemented.Is(err) {
		c.errorHandlerV2(errorcode.NewHPASNotImplemented("HPAS operation not implemented"), errorcode.LevelByAdmin, "")
		return true
	}

	// 6. 处理污点格式错误
	if models.ErrTaintsInvalid.Is(err) {
		c.errorHandlerV2(errorcode.NewInvalidParam(errMsg), errorcode.LevelByUser, errMsg)
		return true
	}

	// 7. 处理节点组副本数配置错误
	if models.ErrIgReplicasMaxLessThanMin.Is(err) {
		c.errorHandlerV2(errorcode.NewInvalidParam(errMsg), errorcode.LevelByUser, errMsg)
		return true
	}

	// 8. 处理实例移除中的错误
	if models.ErrIgInstanceRemovalInProgress.Is(err) {
		c.errorHandlerV2(errorcode.NewInvalidParam(errMsg), errorcode.LevelByUser, errMsg)
		return true
	}

	// 9. 处理权限相关错误
	if models.IsNoPermissions(err) {
		c.errorHandlerV2(errorcode.NewAccessDenied(), errorcode.LevelByUser, errMsg)
		return true
	}

	// 10. 处理资源不存在错误
	if models.IsNotExist(err) {
		c.errorHandlerV2(errorcode.NewInstanceGroupNotFound(errMsg), errorcode.LevelByUser, "")
		return true
	}

	return false
}
