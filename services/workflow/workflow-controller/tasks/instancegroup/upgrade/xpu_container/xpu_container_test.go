package xpu_container

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup/upgrade"
)

func TestNew(t *testing.T) {
	testCases := []struct {
		name    string
		config  *Config
		task    *upgrade.Task
		wantErr bool
		errMsg  string
	}{
		{
			name: "TestNew_GivenValidConfig_WhenCreating_ThenReturnInstance",
			config: &Config{
				TargetVersion: "1.0.5",
			},
			task:    &upgrade.Task{},
			wantErr: false,
		},
		{
			name:    "TestNew_GivenNilConfig_WhenCreating_ThenReturnError",
			config:  nil,
			task:    &upgrade.Task{},
			wantErr: true,
			errMsg:  "config is nil",
		},
		{
			name: "TestNew_GivenEmptyVersion_WhenCreating_ThenReturnInstance",
			config: &Config{
				TargetVersion: "",
			},
			task:    &upgrade.Task{},
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := New(ctx, tc.config, tc.task)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				upgrader := result.(*UpgradeXPUContainer)
				assert.Equal(t, tc.config, upgrader.config)
				assert.Equal(t, tc.task, upgrader.Task)
			}
		})
	}
}

func TestUpgradeXPUContainer_checkNeedXPU(t *testing.T) {
	testCases := []struct {
		name     string
		instance *ccev1.Instance
		want     bool
	}{
		{
			name:     "TestCheckNeedXPU_GivenNilInstance_WhenChecking_ThenReturnFalse",
			instance: nil,
			want:     false,
		},
		{
			name: "TestCheckNeedXPU_GivenKunlunInstance_WhenChecking_ThenReturnTrue",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
				},
			},
			want: true,
		},
		{
			name: "TestCheckNeedXPU_GivenNonKunlunInstance_WhenChecking_ThenReturnFalse",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeN1,
				},
			},
			want: false,
		},
		{
			name: "TestCheckNeedXPU_GivenZeroInstanceType_WhenChecking_ThenReturnFalse",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceType("0"),
				},
			},
			want: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			upgrader := &UpgradeXPUContainer{}
			result := upgrader.checkNeedXPU(tc.instance)
			assert.Equal(t, tc.want, result)
		})
	}
}

func TestGenerateUpgradeCommands(t *testing.T) {
	testCases := []struct {
		name          string
		osName        string
		osVersion     string
		xpuPath       string
		targetVersion string
		wantCommands  int
		checkContent  []string
	}{
		{
			name:          "TestGenerateUpgradeCommands_GivenUbuntu2004_WhenGenerating_ThenReturnCorrectCommands",
			osName:        string(bccimage.OSNameUbuntu),
			osVersion:     "20.04",
			xpuPath:       "http://example.com/xpu.tar.gz",
			targetVersion: "1.0.5",
			wantCommands:  11, // 检查版本(2) + 创建目录(1) + 删除文件(1) + 下载(1) + 卸载(3) + 安装(1) + 验证(2) = 11
			checkContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz",
				"wget -q -O /deploy/nvidia-container/nvidia-container-toolkit.tar.gz http://example.com/xpu.tar.gz",
				"dpkg --purge",
				"tar zxf nvidia-container-toolkit.tar.gz",
				"cd ../../../xpu-container-toolkit-1.0.5/ubuntu20.04/amd64",
			},
		},
		{
			name:          "TestGenerateUpgradeCommands_GivenCentOS7_WhenGenerating_ThenReturnCorrectCommands",
			osName:        string(bccimage.OSNameCentOS),
			osVersion:     "7.6",
			xpuPath:       "http://example.com/xpu.tar.gz",
			targetVersion: "1.0.5",
			wantCommands:  11, // 检查版本(2) + 创建目录(1) + 删除文件(1) + 下载(1) + 卸载(3) + 安装(1) + 验证(2) = 11
			checkContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz",
				"wget -q -O /deploy/nvidia-container/nvidia-container-toolkit.tar.gz http://example.com/xpu.tar.gz",
				"rpm -e --nodeps",
				"tar zxf nvidia-container-toolkit.tar.gz",
				"cd ../../../xpu-container-toolkit-1.0.5/centos7/x86_64",
			},
		},
		{
			name:          "TestGenerateUpgradeCommands_GivenBaiduLinux_WhenGenerating_ThenReturnCorrectCommands",
			osName:        string(bccimage.OSNameBaiduLinux),
			osVersion:     "3.2",
			xpuPath:       "http://example.com/xpu.tar.gz",
			targetVersion: "1.0.5",
			wantCommands:  11,
			checkContent: []string{
				"rpm -e --nodeps",
				"tar zxf nvidia-container-toolkit.tar.gz",
				"cd ../../../xpu-container-toolkit-1.0.5/centos7/x86_64", // BaiduLinux uses centos7 path
			},
		},
		{
			name:          "TestGenerateUpgradeCommands_GivenRockyLinux_WhenGenerating_ThenReturnCorrectCommands",
			osName:        string(bccimage.OSNameRocky),
			osVersion:     "8.5",
			xpuPath:       "http://example.com/xpu.tar.gz",
			targetVersion: "1.0.5",
			wantCommands:  11,
			checkContent: []string{
				"rpm -e --nodeps",
				"tar zxf nvidia-container-toolkit.tar.gz",
				"cd ../../../xpu-container-toolkit-1.0.5/centos8/x86_64", // Rocky uses centos8 path
			},
		},
		{
			name:          "TestGenerateUpgradeCommands_GivenUnsupportedOS_WhenGenerating_ThenReturnEmptyCommands",
			osName:        "unknown",
			osVersion:     "1.0",
			xpuPath:       "http://example.com/xpu.tar.gz",
			targetVersion: "1.0.5",
			wantCommands:  7, // 只有基础命令：检查版本(2) + 创建目录(1) + 删除文件(1) + 下载(1) + 验证(2) = 7
			checkContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz",
				"wget -q -O /deploy/nvidia-container/nvidia-container-toolkit.tar.gz http://example.com/xpu.tar.gz",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			commands := GenerateUpgradeCommands(tc.osName, tc.osVersion, tc.xpuPath, tc.targetVersion)

			assert.Equal(t, tc.wantCommands, len(commands))

			// 检查命令内容
			allCommands := ""
			for _, cmd := range commands {
				allCommands += cmd.Command + " "
			}

			for _, content := range tc.checkContent {
				assert.Contains(t, allCommands, content)
			}

			// 检查超时设置 - 找到wget命令
			var downloadCmd *exectypes.Command
			for _, cmd := range commands {
				if strings.Contains(cmd.Command, "wget") {
					downloadCmd = cmd
					break
				}
			}
			assert.NotNil(t, downloadCmd)
			assert.Equal(t, 240*time.Second, downloadCmd.Timeout)
			assert.Equal(t, 5, downloadCmd.Retry)
		})
	}
}

func TestGenerateUbuntuInstallCommand(t *testing.T) {
	testCases := []struct {
		name         string
		osVersion    string
		version      string
		arch         string
		expectedPath string
		expectedArch string
	}{
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenUbuntu1604_WhenGenerating_ThenReturnUbuntu1604Path",
			osVersion:    "16.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu16.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenUbuntu1804_WhenGenerating_ThenReturnUbuntu1804Path",
			osVersion:    "18.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu18.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenUbuntu2004_WhenGenerating_ThenReturnUbuntu2004Path",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu20.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenUbuntu2204_WhenGenerating_ThenReturnUbuntu2204Path",
			osVersion:    "22.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu22.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenUnknownVersion_WhenGenerating_ThenReturnDefaultUbuntu1804Path",
			osVersion:    "24.04", // 未知版本，应该使用默认的ubuntu18.04
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu18.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenArm64_WhenGenerating_ThenReturnArm64Arch",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "arm64",
			expectedPath: "ubuntu20.04",
			expectedArch: "arm64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommand_GivenDefaultArch_WhenGenerating_ThenReturnAmd64Arch",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "unknown", // 未知架构，应该使用默认的amd64
			expectedPath: "ubuntu20.04",
			expectedArch: "amd64",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			upgrader := &UpgradeXPUContainer{}
			command := upgrader.generateUbuntuInstallCommand(tc.osVersion, tc.version, tc.arch)

			// 检查路径
			assert.Contains(t, command, fmt.Sprintf("cd kunlun/libxpu-container-1.0.2/%s/%s", tc.expectedPath, tc.expectedArch))
			// 检查架构
			assert.Contains(t, command, fmt.Sprintf("libxpu-container1_1.0.2-1_%s.deb", tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("libxpu-container-tools_1.0.2-1_%s.deb", tc.expectedArch))
			// 检查版本和架构
			assert.Contains(t, command, fmt.Sprintf("cd ../../../xpu-container-toolkit-%s/%s/%s", tc.version, tc.expectedPath, tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("xpu-container-toolkit_%s-1_%s.deb", tc.version, tc.expectedArch))
		})
	}
}

func TestGenerateCentOSInstallCommand(t *testing.T) {
	testCases := []struct {
		name         string
		osName       string
		osVersion    string
		version      string
		arch         string
		expectedPath string
		expectedArch string
	}{
		{
			name:         "TestGenerateCentOSInstallCommand_GivenCentOS7_WhenGenerating_ThenReturnCentOS7Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenCentOS8_WhenGenerating_ThenReturnCentOS8Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "8.4",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos8",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenBaiduLinux_WhenGenerating_ThenReturnCentOS7Path",
			osName:       string(bccimage.OSNameBaiduLinux),
			osVersion:    "3.2",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenRocky_WhenGenerating_ThenReturnCentOS8Path",
			osName:       string(bccimage.OSNameRocky),
			osVersion:    "8.5",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos8",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenUnknownOS_WhenGenerating_ThenReturnDefaultCentOS7Path",
			osName:       "unknown",
			osVersion:    "1.0",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenARM64_WhenGenerating_ThenReturnAarch64Arch",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "arm64",
			expectedPath: "centos7",
			expectedArch: "aarch64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenAarch64_WhenGenerating_ThenReturnAarch64Arch",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "aarch64",
			expectedPath: "centos7",
			expectedArch: "aarch64",
		},
		{
			name:         "TestGenerateCentOSInstallCommand_GivenCentOSUnknownVersion_WhenGenerating_ThenReturnDefaultCentOS7Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "9.0", // 未知版本，应该使用默认的centos7
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			upgrader := &UpgradeXPUContainer{}
			command := upgrader.generateCentOSInstallCommand(tc.osName, tc.osVersion, tc.version, tc.arch)

			// 检查路径
			assert.Contains(t, command, fmt.Sprintf("cd kunlun/libxpu-container-1.0.2/%s/%s", tc.expectedPath, tc.expectedArch))
			// 检查架构
			assert.Contains(t, command, fmt.Sprintf("libxpu-container1-1.0.2-1.%s.rpm", tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("libxpu-container-tools-1.0.2-1.%s.rpm", tc.expectedArch))
			// 检查版本和架构
			assert.Contains(t, command, fmt.Sprintf("cd ../../../xpu-container-toolkit-%s/%s/%s", tc.version, tc.expectedPath, tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("xpu-container-toolkit-%s-1.%s.rpm", tc.version, tc.expectedArch))
		})
	}
}

func TestUpgradeXPUContainer_targetVersion(t *testing.T) {
	upgrader := &UpgradeXPUContainer{
		config: &Config{
			TargetVersion: "1.0.5",
		},
	}

	version := upgrader.targetVersion()
	assert.Equal(t, "1.0.5", version)
}

func TestUpgradeXPUContainer_GetAgentConfigMap(t *testing.T) {
	upgrader := &UpgradeXPUContainer{}
	ctx := context.Background()

	configMap, err := upgrader.GetAgentConfigMap(ctx, "test-instance")
	assert.NoError(t, err)
	assert.Nil(t, configMap)
}

func TestGenerateUbuntuInstallCommandForAgent(t *testing.T) {
	testCases := []struct {
		name         string
		osVersion    string
		version      string
		arch         string
		expectedPath string
		expectedArch string
	}{
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenUbuntu1604_WhenGenerating_ThenReturnUbuntu1604Path",
			osVersion:    "16.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu16.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenUbuntu1804_WhenGenerating_ThenReturnUbuntu1804Path",
			osVersion:    "18.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu18.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenUbuntu2004_WhenGenerating_ThenReturnUbuntu2004Path",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu20.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenUbuntu2204_WhenGenerating_ThenReturnUbuntu2204Path",
			osVersion:    "22.04",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu22.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenUnknownVersion_WhenGenerating_ThenReturnDefaultUbuntu1804Path",
			osVersion:    "24.04", // 未知版本，应该使用默认的ubuntu18.04
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "ubuntu18.04",
			expectedArch: "amd64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenArm64_WhenGenerating_ThenReturnArm64Arch",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "arm64",
			expectedPath: "ubuntu20.04",
			expectedArch: "arm64",
		},
		{
			name:         "TestGenerateUbuntuInstallCommandForAgent_GivenDefaultArch_WhenGenerating_ThenReturnAmd64Arch",
			osVersion:    "20.04",
			version:      "1.0.5",
			arch:         "unknown", // 未知架构，应该使用默认的amd64
			expectedPath: "ubuntu20.04",
			expectedArch: "amd64",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			command := generateUbuntuInstallCommandForAgent(tc.osVersion, tc.version, tc.arch)

			// 检查路径
			assert.Contains(t, command, fmt.Sprintf("cd kunlun/libxpu-container-1.0.2/%s/%s", tc.expectedPath, tc.expectedArch))
			// 检查架构
			assert.Contains(t, command, fmt.Sprintf("libxpu-container1_1.0.2-1_%s.deb", tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("libxpu-container-tools_1.0.2-1_%s.deb", tc.expectedArch))
			// 检查版本和架构
			assert.Contains(t, command, fmt.Sprintf("cd ../../../xpu-container-toolkit-%s/%s/%s", tc.version, tc.expectedPath, tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("xpu-container-toolkit_%s-1_%s.deb", tc.version, tc.expectedArch))
		})
	}
}

func TestGenerateCentOSInstallCommandForAgent(t *testing.T) {
	testCases := []struct {
		name         string
		osName       string
		osVersion    string
		version      string
		arch         string
		expectedPath string
		expectedArch string
	}{
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenCentOS7_WhenGenerating_ThenReturnCentOS7Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenCentOS8_WhenGenerating_ThenReturnCentOS8Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "8.4",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos8",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenBaiduLinux3_WhenGenerating_ThenReturnCentOS7Path",
			osName:       string(bccimage.OSNameBaiduLinux),
			osVersion:    "3.2",
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenUnknownVersion_WhenGenerating_ThenReturnDefaultCentOS7Path",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "9.0", // 未知版本，应该使用默认的centos7
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenARM64_WhenGenerating_ThenReturnAarch64Arch",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "arm64",
			expectedPath: "centos7",
			expectedArch: "aarch64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenAarch64_WhenGenerating_ThenReturnAarch64Arch",
			osName:       string(bccimage.OSNameCentOS),
			osVersion:    "7.9",
			version:      "1.0.5",
			arch:         "aarch64",
			expectedPath: "centos7",
			expectedArch: "aarch64",
		},
		{
			name:         "TestGenerateCentOSInstallCommandForAgent_GivenBaiduLinuxOtherVersion_WhenGenerating_ThenReturnDefaultCentOS7Path",
			osName:       string(bccimage.OSNameBaiduLinux),
			osVersion:    "4.0", // 不是3开头的版本，应该使用默认的centos7
			version:      "1.0.5",
			arch:         "amd64",
			expectedPath: "centos7",
			expectedArch: "x86_64",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			command := generateCentOSInstallCommandForAgent(tc.osName, tc.osVersion, tc.version, tc.arch)

			// 检查路径
			assert.Contains(t, command, fmt.Sprintf("cd kunlun/libxpu-container-1.0.2/%s/%s", tc.expectedPath, tc.expectedArch))
			// 检查架构
			assert.Contains(t, command, fmt.Sprintf("libxpu-container1-1.0.2-1.%s.rpm", tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("libxpu-container-tools-1.0.2-1.%s.rpm", tc.expectedArch))
			// 检查版本和架构
			assert.Contains(t, command, fmt.Sprintf("cd ../../../xpu-container-toolkit-%s/%s/%s", tc.version, tc.expectedPath, tc.expectedArch))
			assert.Contains(t, command, fmt.Sprintf("xpu-container-toolkit-%s-1.%s.rpm", tc.version, tc.expectedArch))
		})
	}
}

func TestNeedUpgradeComponent(t *testing.T) {
	ctx := context.Background()

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMeta := metamock.NewMockInterface(ctl)

	testCases := []struct {
		name         string
		instanceId   string
		config       *Config
		mockInstance *ccev1.Instance
		mockError    error
		wantNeed     bool
		wantReason   string
		wantErr      bool
		errMsg       string
	}{
		{
			name:       "TestNeedUpgradeComponent_GivenEmptyInstanceId_WhenChecking_ThenReturnError",
			instanceId: "",
			config:     &Config{TargetVersion: "1.0.5"},
			wantNeed:   false,
			wantReason: "",
			wantErr:    true,
			errMsg:     "instanceId is empty",
		},
		{
			name:       "TestNeedUpgradeComponent_GivenGetInstanceCRFails_WhenChecking_ThenReturnError",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockError:  fmt.Errorf("instance not found"),
			wantNeed:   false,
			wantReason: "",
			wantErr:    true,
			errMsg:     "GetInstanceCR failed",
		},
		{
			name:       "TestNeedUpgradeComponent_GivenSameVersion_WhenChecking_ThenReturnFalse",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					XPUContainerToolkitVersion: "1.0.5",
				},
			},
			wantNeed:   false,
			wantReason: "test-instance xpu-container-toolkit already 1.0.5, skip upgrade",
			wantErr:    false,
		},
		{
			name:       "TestNeedUpgradeComponent_GivenDifferentVersion_WhenChecking_ThenReturnTrue",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					XPUContainerToolkitVersion: "1.0.4",
				},
			},
			wantNeed:   true,
			wantReason: "",
			wantErr:    false,
		},
		{
			name:       "TestNeedUpgradeComponent_GivenEmptyCurrentVersion_WhenChecking_ThenInferAndCompare",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					XPUContainerToolkitVersion: "",
					RuntimeType:                "docker",
					RuntimeVersion:             "19.03",
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "18.04",
					},
				},
			},
			wantNeed:   true, // 假设推断的版本与目标版本不同
			wantReason: "",
			wantErr:    false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置mock期望
			if tc.instanceId != "" {
				if tc.mockError != nil {
					mockMeta.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Eq(tc.instanceId), gomock.Any()).Return(nil, tc.mockError)
				} else if tc.mockInstance != nil {
					mockMeta.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Eq(tc.instanceId), gomock.Any()).Return(tc.mockInstance, nil)
				}
			}

			upgrader := &UpgradeXPUContainer{
				config: tc.config,
				Task: &upgrade.Task{
					BaseTask: &tasks.BaseTask{
						Clients: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMeta,
							},
						},
						Cluster: &ccev1.Cluster{
							Spec: ccetypes.ClusterSpec{
								K8SVersion: "1.20.0",
							},
						},
					},
				},
			}

			// Mock GetInstanceDefaultInstallXPUContainerToolkitVersion if needed
			var patches *gomonkey.Patches
			if tc.mockInstance != nil && tc.mockInstance.Spec.XPUContainerToolkitVersion == "" {
				patches = gomonkey.ApplyFunc(ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion, func(k8sVersion ccetypes.K8SVersion, runtimeType ccetypes.RuntimeType, runtimeVersion, osName, osVersion string) string {
					return "1.0.4" // 返回一个与目标版本不同的默认版本
				})
			}
			if patches != nil {
				defer patches.Reset()
			}

			needUpgrade, reason, err := upgrader.NeedUpgradeComponent(ctx, tc.instanceId)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
				assert.Equal(t, tc.wantNeed, needUpgrade)
				assert.Equal(t, tc.wantReason, reason)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.wantNeed, needUpgrade)
				assert.Equal(t, tc.wantReason, reason)
			}
		})
	}
}

func TestGetCommands(t *testing.T) {
	ctx := context.Background()

	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockMeta := metamock.NewMockInterface(ctl)

	testCases := []struct {
		name                string
		instanceId          string
		config              *Config
		mockInstance        *ccev1.Instance
		mockInstanceError   error
		wantCommands        int
		wantErr             bool
		errMsg              string
		checkCommandContent []string
	}{
		{
			name:         "TestGetCommands_GivenEmptyInstanceId_WhenGetting_ThenReturnError",
			instanceId:   "",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 0,
			wantErr:      true,
			errMsg:       "prepareUpgradeXPUContainer failed",
		},
		{
			name:              "TestGetCommands_GivenGetInstanceFails_WhenGetting_ThenReturnError",
			instanceId:        "test-instance",
			config:            &Config{TargetVersion: "1.0.5"},
			mockInstanceError: fmt.Errorf("instance not found"),
			wantCommands:      0,
			wantErr:           true,
			errMsg:            "prepareUpgradeXPUContainer failed",
		},
		{
			name:       "TestGetCommands_GivenNonXPUInstance_WhenGetting_ThenReturnNil",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeN1, // 非昆仑芯节点
				},
			},
			wantCommands: 0,
			wantErr:      false,
		},
		{
			name:       "TestGetCommands_GivenXPUInstanceUbuntu_WhenGetting_ThenReturnCommands",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "20.04",
						OSArch:    "amd64",
					},
				},
			},
			wantCommands: 11, // 2(版本检查) + 1(创建目录) + 1(删除文件) + 1(下载) + 3(卸载) + 1(安装) + 2(验证)
			wantErr:      false,
			checkCommandContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz",
				"wget",
				"dpkg --purge",
				"dpkg -i",
			},
		},
		{
			name:       "TestGetCommands_GivenXPUInstanceCentOS_WhenGetting_ThenReturnCommands",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "7.9",
						OSArch:    "x86_64",
					},
				},
			},
			wantCommands: 11, // 2(版本检查) + 1(创建目录) + 1(删除文件) + 1(下载) + 3(卸载) + 1(安装) + 2(验证)
			wantErr:      false,
			checkCommandContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz",
				"wget",
				"rpm -e --nodeps",
				"rpm -ivh",
			},
		},
		{
			name:       "TestGetCommands_GivenNilDeployerConfig_WhenGetting_ThenReturnError",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "20.04",
						OSArch:    "amd64",
					},
				},
			},
			wantCommands: 0,
			wantErr:      true,
			errMsg:       "prepareUpgradeXPUContainer failed",
		},
		{
			name:       "TestGetCommands_GivenEmptyXPUPath_WhenGetting_ThenReturnError",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "20.04",
						OSArch:    "amd64",
					},
				},
			},
			wantCommands: 0,
			wantErr:      true,
			errMsg:       "prepareUpgradeXPUContainer failed",
		},
		{
			name:       "TestGetCommands_GivenUnsupportedOS_WhenGetting_ThenReturnError",
			instanceId: "test-instance",
			config:     &Config{TargetVersion: "1.0.5"},
			mockInstance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    "unknown",
						OSVersion: "1.0",
						OSArch:    "amd64",
					},
				},
			},
			wantCommands: 0,
			wantErr:      true,
			errMsg:       "generateUpgradeCommands failed",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置mock期望
			if tc.instanceId != "" {
				if tc.mockInstanceError != nil {
					mockMeta.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Eq(tc.instanceId), gomock.Any()).Return(nil, tc.mockInstanceError)
				} else if tc.mockInstance != nil {
					mockMeta.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Eq(tc.instanceId), gomock.Any()).Return(tc.mockInstance, nil)
				}
			}

			// 创建mock DeployerConfig
			var mockDeployerConfig *dptypes.Config
			// 检查测试用例是否有 mockDeployerConfig 字段设置
			hasCustomConfig := false
			// 通过检查测试用例名称来判断是否需要特殊处理配置
			if strings.Contains(tc.name, "NilDeployerConfig") {
				mockDeployerConfig = nil
				hasCustomConfig = true
			} else if strings.Contains(tc.name, "EmptyXPUPath") {
				mockDeployerConfig = &dptypes.Config{
					NvidiaContainerToolkitPath: "", // 空路径
					CCEAgentImage:              "cce-agent:latest",
				}
				hasCustomConfig = true
			}

			if !hasCustomConfig {
				// 使用默认配置
				mockDeployerConfig = &dptypes.Config{
					NvidiaContainerToolkitPath: "http://example.com/xpu.tar.gz",
					CCEAgentImage:              "cce-agent:latest",
				}
			}

			upgrader := &UpgradeXPUContainer{
				config: tc.config,
				Task: &upgrade.Task{
					BaseTask: &tasks.BaseTask{
						Clients: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMeta,
							},
						},
						DeployerConfig: mockDeployerConfig,
						Cluster: &ccev1.Cluster{
							Spec: ccetypes.ClusterSpec{
								K8SVersion: "1.20.0",
								K8SCustomConfig: ccetypes.K8SCustomConfig{
									EnableHostname: false,
								},
							},
						},
						CCEInstanceIDToVPCIP: map[string]string{
							"test-instance": "*************",
						},
					},
				},
			}

			commands, err := upgrader.GetCommands(ctx, tc.instanceId)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
				assert.Nil(t, commands)
			} else {
				assert.NoError(t, err)
				if tc.wantCommands == 0 {
					assert.Nil(t, commands)
				} else {
					assert.NotNil(t, commands)
					assert.Equal(t, tc.wantCommands, len(commands))

					// 检查命令内容
					if len(tc.checkCommandContent) > 0 {
						commandText := ""
						for _, cmd := range commands {
							commandText += cmd.Command + " "
						}
						for _, expectedContent := range tc.checkCommandContent {
							assert.Contains(t, commandText, expectedContent)
						}
					}
				}
			}
		})
	}
}

func TestGetXPUContainerToolkitPath(t *testing.T) {
	testCases := []struct {
		name           string
		deployerConfig *dptypes.Config
		wantPath       string
		wantErr        bool
		errMsg         string
	}{
		{
			name:           "TestGetXPUContainerToolkitPath_GivenNilDeployerConfig_WhenGetting_ThenReturnError",
			deployerConfig: nil,
			wantPath:       "",
			wantErr:        true,
			errMsg:         "DeployerConfig is nil",
		},
		{
			name: "TestGetXPUContainerToolkitPath_GivenEmptyPath_WhenGetting_ThenReturnError",
			deployerConfig: &dptypes.Config{
				NvidiaContainerToolkitPath: "",
			},
			wantPath: "",
			wantErr:  true,
			errMsg:   "XPU Container Toolkit path is empty",
		},
		{
			name: "TestGetXPUContainerToolkitPath_GivenValidPath_WhenGetting_ThenReturnPath",
			deployerConfig: &dptypes.Config{
				NvidiaContainerToolkitPath: "http://example.com/xpu.tar.gz",
			},
			wantPath: "http://example.com/xpu.tar.gz",
			wantErr:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			upgrader := &UpgradeXPUContainer{
				config: &Config{TargetVersion: "1.0.5"},
				Task: &upgrade.Task{
					BaseTask: &tasks.BaseTask{
						DeployerConfig: tc.deployerConfig,
					},
				},
			}

			path, err := upgrader.getXPUContainerToolkitPath()

			if tc.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
				assert.Equal(t, tc.wantPath, path)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.wantPath, path)
			}
		})
	}
}

func TestUpgradeXPUContainer_generateUpgradeCommands(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testCases := []struct {
		name                string
		instance            *ccev1.Instance
		xpuPath             string
		config              *Config
		wantCommands        int
		wantErr             bool
		errMsg              string
		checkCommandContent []string
	}{
		{
			name: "TestGenerateUpgradeCommands_GivenUbuntuWithArm64_WhenGenerating_ThenReturnCorrectCommands",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "20.04",
						OSArch:    "arm64",
					},
				},
			},
			xpuPath:      "http://example.com/xpu.tar.gz",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 11,
			wantErr:      false,
			checkCommandContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"wget",
				"dpkg --purge",
				"ubuntu20.04/arm64", // 检查arm64架构
				"dpkg -i",
			},
		},
		{
			name: "TestGenerateUpgradeCommands_GivenCentOSWithAarch64_WhenGenerating_ThenReturnCorrectCommands",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "7.9",
						OSArch:    "aarch64",
					},
				},
			},
			xpuPath:      "http://example.com/xpu.tar.gz",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 11,
			wantErr:      false,
			checkCommandContent: []string{
				"mkdir -p /deploy/nvidia-container/",
				"wget",
				"rpm -e --nodeps",
				"centos7/aarch64", // 检查aarch64架构
				"rpm -ivh",
			},
		},
		{
			name: "TestGenerateUpgradeCommands_GivenUbuntuWithX86_64_WhenGenerating_ThenReturnCorrectCommands",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "18.04",
						OSArch:    "x86_64",
					},
				},
			},
			xpuPath:      "http://example.com/xpu.tar.gz",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 11,
			wantErr:      false,
			checkCommandContent: []string{
				"ubuntu18.04/amd64", // x86_64应该映射为amd64
			},
		},
		{
			name: "TestGenerateUpgradeCommands_GivenEmptyArch_WhenGenerating_ThenUseDefaultAmd64",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    bccimage.OSNameUbuntu,
						OSVersion: "20.04",
						OSArch:    "", // 空架构
					},
				},
			},
			xpuPath:      "http://example.com/xpu.tar.gz",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 11,
			wantErr:      false,
			checkCommandContent: []string{
				"ubuntu20.04/amd64", // 默认应该使用amd64
			},
		},
		{
			name: "TestGenerateUpgradeCommands_GivenUnsupportedOS_WhenGenerating_ThenReturnError",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					InstanceType: bcc.InstanceTypeKunlun,
					InstanceOS: ccetypes.InstanceOS{
						OSName:    "unknown",
						OSVersion: "1.0",
						OSArch:    "amd64",
					},
				},
			},
			xpuPath:      "http://example.com/xpu.tar.gz",
			config:       &Config{TargetVersion: "1.0.5"},
			wantCommands: 0,
			wantErr:      true,
			errMsg:       "unsupported OS: unknown",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()
			upgrader := &UpgradeXPUContainer{
				config: tc.config,
			}

			commands, err := upgrader.generateUpgradeCommands(ctx, tc.instance, tc.xpuPath)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errMsg)
				assert.Nil(t, commands)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, commands)
				assert.Equal(t, tc.wantCommands, len(commands))

				// 检查命令内容
				if len(tc.checkCommandContent) > 0 {
					commandText := ""
					for _, cmd := range commands {
						commandText += cmd.Command + " "
					}
					for _, expectedContent := range tc.checkCommandContent {
						assert.Contains(t, commandText, expectedContent)
					}
				}
			}
		})
	}
}

func TestUpgradeXPUContainer_UpdateInstanceCRDComponentVersion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name     string
		instance *ccev1.Instance
		success  bool
		wantErr  string
	}{
		{
			name: "success",
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					XPUContainerToolkitVersion: "1.0.4",
				},
				Status: ccetypes.InstanceStatus{},
			},
			success: true,
		},
	}

	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			mockMeta := metamock.NewMockInterface(ctrl)
			mockMeta.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.instance, nil).AnyTimes()
			mockMeta.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

			upgrader := &UpgradeXPUContainer{
				config: &Config{TargetVersion: "1.0.5"},
				Task: &upgrade.Task{
					BaseTask: &tasks.BaseTask{
						Clients: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMeta,
							},
						},
					},
				},
			}

			err := upgrader.UpdateInstanceCRDComponentVersion(context.TODO(), "ins-1")
			if tt.success {
				assert.NoError(t, err)
			} else {
				assert.Contains(t, err.Error(), tt.wantErr)
			}
		})
	}
}
