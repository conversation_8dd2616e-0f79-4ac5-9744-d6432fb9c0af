package xpu_container

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup/upgrade"
)

// UpgradeXPUContainer 实现了 upgrade.Interface
type UpgradeXPUContainer struct {
	config *Config // 升级xpu-container-toolkit组件的参数

	*upgrade.Task // 里面封装了一些不同组件升级的通用函数
}

// Config 升级XPU Container Toolkit配置
type Config struct {
	TargetVersion string
}

// New 构造函数
// 单测要点：
// 1. 正常场景：传入有效的config和task参数，返回UpgradeXPUContainer实例
//   - 输入：config包含TargetVersion，task包含升级通用函数
//   - 输出：正确初始化的UpgradeXPUContainer实例
//
// 2. 异常场景：
//   - config为nil时，返回错误"config is nil"
//   - task为nil时，应能正确处理（取决于upgrade.Task的实现）
//
// 3. 边界条件：
//   - config.TargetVersion为空字符串时的处理
//   - 并发调用New方法的线程安全性
func New(ctx context.Context, config *Config, task *upgrade.Task) (upgrade.Interface, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	return &UpgradeXPUContainer{
		config: config,
		Task:   task,
	}, nil
}

// 单测要点：
// 1. 正常场景：昆仑芯节点需要升级
//   - 输入：instanceId对应昆仑芯节点（InstanceType=25），当前版本与目标版本不同
//   - 输出：(true, "", nil)
//
// 2. 跳过场景：
//   - 非昆仑芯节点：返回(false, "非昆仑芯节点，跳过XPU升级", nil)
//   - 版本已匹配：返回(false, "xpu-container-toolkit already xxx, skip upgrade", nil)
//
// 3. 异常场景：
//   - instanceId为空：返回(false, "", "instanceId is empty")
//   - GetInstanceCR失败：返回(false, "", "GetInstanceCR failed: xxx")
//
// 4. 边界条件：
//   - 当前版本为空字符串时的比较逻辑
//   - 目标版本为空字符串时的处理
//
// 5. 并发安全：多个goroutine同时调用时的线程安全性
func (t *UpgradeXPUContainer) NeedUpgradeComponent(ctx context.Context, instanceId string) (bool, string, error) {
	if instanceId == "" {
		return false, "", fmt.Errorf("instanceId is empty")
	}
	instance, err := t.GetInstanceCR(ctx, instanceId)
	if err != nil {
		return false, "", fmt.Errorf("GetInstanceCR failed: %v", err)
	}

	currentVersion := instance.Spec.XPUContainerToolkitVersion
	// 处理存量昆仑芯节点XPU版本字段为空的场景
	if currentVersion == "" {
		// 根据节点信息推断默认安装的XPU版本
		currentVersion = ccetypes.GetInstanceDefaultInstallXPUContainerToolkitVersion(
			t.Cluster.Spec.K8SVersion,
			instance.Spec.RuntimeType,
			instance.Spec.RuntimeVersion,
			string(instance.Spec.InstanceOS.OSName),
			instance.Spec.InstanceOS.OSVersion)
		logger.Infof(ctx, "instance %s XPU version is empty, inferred default version: %s", instanceId, currentVersion)
	}

	if currentVersion == t.config.TargetVersion {
		return false, fmt.Sprintf("%s xpu-container-toolkit already %v, skip upgrade", instanceId, currentVersion), nil
	}

	return true, "", nil
}

// 单测要点：
// 1. 正常场景：生成不同操作系统的升级命令
//   - Ubuntu 16.04：生成3个删除命令+1个安装命令，使用ubuntu1604InstallXPUCommand模板
//   - Ubuntu 18.04+：生成3个删除命令+1个安装命令，使用ubuntu1804InstallXPUCommand模板
//   - CentOS/BaiduLinux/Rocky：生成1个安装命令，使用centosInstallXPUCommand模板
//
// 2. 跳过场景：
//   - 非昆仑芯节点：返回(nil, nil)，记录跳过日志
//
// 3. 异常场景：
//   - prepareUpgradeXPUContainer失败：返回错误
//   - 不支持的操作系统：返回"unsupported OS: xxx"错误
//
// 4. 边界条件：
//   - 操作系统名称大小写不敏感比较
//   - 版本号格式化到命令中的正确性
//
// 5. 命令内容验证：
//   - Ubuntu命令包含dpkg --purge卸载逻辑
//   - CentOS命令包含rpm -ivh安装逻辑
//   - 命令超时时间设置为240秒
//
// 6. 并发安全：多个实例同时生成命令时的线程安全性
func (t *UpgradeXPUContainer) GetCommands(ctx context.Context, instanceId string) ([]*exectypes.Command, error) {
	// 1、前期准备
	xpuContainerToolkitPath, instance, needXPU, err := t.prepareUpgradeXPUContainer(ctx, instanceId)
	if err != nil {
		return nil, fmt.Errorf("prepareUpgradeXPUContainer failed: %v", err)
	}
	// 非 XPU 机器跳过升级
	if !needXPU {
		logger.Infof(ctx, "InstanceType %s != XPU, skip upgrade xpu-container-toolkit", instance.Spec.InstanceType)
		return nil, nil
	}

	// 2、生成升级命令
	commands, err := t.generateUpgradeCommands(ctx, instance, xpuContainerToolkitPath)
	if err != nil {
		return nil, fmt.Errorf("generateUpgradeCommands failed: %v", err)
	}

	return commands, nil
}

// prepareUpgradeXPUContainer 准备升级XPU Container Toolkit
func (t *UpgradeXPUContainer) prepareUpgradeXPUContainer(ctx context.Context, instanceId string) (string, *ccev1.Instance, bool, error) {
	// 1、获取instance
	instance, err := t.GetInstanceCR(ctx, instanceId)
	if err != nil {
		return "", nil, false, fmt.Errorf("GetInstanceCR failed: %v", err)
	}

	// 2、检查是否需要XPU
	needXPU := t.checkNeedXPU(instance)

	// 3、获取XPU Container Toolkit下载地址
	xpuContainerToolkitPath, err := t.getXPUContainerToolkitPath()
	if err != nil {
		return "", nil, false, fmt.Errorf("getXPUContainerToolkitPath failed: %v", err)
	}

	return xpuContainerToolkitPath, instance, needXPU, nil
}

// checkNeedXPU 检查是否需要XPU
// 单测要点：
// 1. 正常场景：昆仑芯节点判断
//   - 输入：instance.Spec.InstanceType = bcc.InstanceTypeKunlun (25)
//   - 输出：true
//
// 2. 非昆仑芯节点：
//   - 输入：instance.Spec.InstanceType = 其他值（如GPU节点、普通节点）
//   - 输出：false
//
// 3. 边界条件：
//   - instance为nil时的处理（虽然调用方应保证不为nil）
//   - InstanceType为0或未设置时的处理
//
// 4. 并发安全：多个goroutine同时调用的线程安全性
func (t *UpgradeXPUContainer) checkNeedXPU(instance *ccev1.Instance) bool {
	// 防止空指针
	if instance == nil {
		return false
	}
	// 只有昆仑芯节点才需要升级xpu-container-toolkit
	return instance.Spec.InstanceType == bcc.InstanceTypeKunlun
}

// getXPUContainerToolkitPath 获取XPU Container Toolkit下载地址
func (t *UpgradeXPUContainer) getXPUContainerToolkitPath() (string, error) {
	if t.DeployerConfig == nil {
		return "", fmt.Errorf("DeployerConfig is nil")
	}

	// 从配置中获取XPU Container Toolkit路径，复用NvidiaContainerToolkitPath字段
	// 实际使用时可以新增XPUContainerToolkitPath字段
	xpuPath := t.DeployerConfig.NvidiaContainerToolkitPath
	if xpuPath == "" {
		return "", fmt.Errorf("XPU Container Toolkit path is empty")
	}

	return xpuPath, nil
}

// generateUpgradeCommands 生成升级命令
func (t *UpgradeXPUContainer) generateUpgradeCommands(ctx context.Context, instance *ccev1.Instance, xpuContainerToolkitPath string) ([]*exectypes.Command, error) {
	targetXPUContainerVersion := t.config.TargetVersion
	osName := string(instance.Spec.InstanceOS.OSName)
	osVersion := instance.Spec.InstanceOS.OSVersion
	osArch := instance.Spec.InstanceOS.OSArch

	// 处理架构信息，默认为amd64
	arch := "amd64"
	if osArch != "" {
		if strings.Contains(strings.ToLower(osArch), "x86_64") || strings.Contains(strings.ToLower(osArch), "amd64") {
			arch = "amd64"
		} else if strings.Contains(strings.ToLower(osArch), "arm64") || strings.Contains(strings.ToLower(osArch), "aarch64") {
			arch = "arm64"
		}
	}

	var commands []*exectypes.Command

	// 1. 显示当前版本信息
	commands = append(commands,
		&exectypes.Command{
			Command: `echo 'before upgrade xpu-container-toolkit, checking current version...'`,
		},
		&exectypes.Command{
			Command: `dpkg -l | grep xpu-container-toolkit || rpm -qa | grep xpu-container-toolkit || echo 'No XPU Container Toolkit installed'`,
		},
	)

	// 2. 创建目录并下载软件包
	commands = append(commands,
		&exectypes.Command{
			Command: `mkdir -p /deploy/nvidia-container/`,
		},
		&exectypes.Command{
			Command: `rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz`,
		},
		&exectypes.Command{
			Command: fmt.Sprintf("wget -q -O /deploy/nvidia-container/nvidia-container-toolkit.tar.gz %s", xpuContainerToolkitPath),
			Timeout: 240 * time.Second,
			Retry:   5,
		},
	)

	// 3. Ubuntu 机器安装XPU Container Toolkit
	if strings.EqualFold(osName, string(bccimage.OSNameUbuntu)) {
		logger.Infof(ctx, "InstanceOS: %s %s %s, start install xpu-container-toolkit", osName, osVersion, arch)

		// 删除现有版本
		commands = append(commands,
			&exectypes.Command{
				Command: `echo "Starting XPU Container Toolkit uninstallation..." && dpkg -l | grep "xpu-container-toolkit" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled xpu-container-toolkit packages"`,
			},
			&exectypes.Command{
				Command: `dpkg -l | grep "libxpu-container-tools" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled libxpu-container-tools packages"`,
			},
			&exectypes.Command{
				Command: `dpkg -l | grep "libxpu-container1" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled libxpu-container1 packages"`,
			})

		// 生成安装命令
		installCmd := t.generateUbuntuInstallCommand(osVersion, targetXPUContainerVersion, arch)
		commands = append(commands,
			&exectypes.Command{
				Command: installCmd,
				Timeout: 300 * time.Second,
			},
		)

	} else if strings.EqualFold(osName, string(bccimage.OSNameCentOS)) ||
		strings.EqualFold(osName, string(bccimage.OSNameBaiduLinux)) ||
		strings.EqualFold(osName, string(bccimage.OSNameRocky)) {

		logger.Infof(ctx, "InstanceOS: %s %s %s, start install xpu-container-toolkit", osName, osVersion, arch)

		// CentOS需要先卸载旧版本
		commands = append(commands,
			&exectypes.Command{
				Command: `echo "Starting XPU Container Toolkit uninstallation..." && rpm -qa | grep xpu-container-toolkit | xargs -r rpm -e --nodeps && echo "Uninstalled xpu-container-toolkit packages"`,
			},
			&exectypes.Command{
				Command: `rpm -qa | grep libxpu-container-tools | xargs -r rpm -e --nodeps && echo "Uninstalled libxpu-container-tools packages"`,
			},
			&exectypes.Command{
				Command: `rpm -qa | grep libxpu-container1 | xargs -r rpm -e --nodeps && echo "Uninstalled libxpu-container1 packages"`,
			})

		// 生成安装命令
		installCmd := t.generateCentOSInstallCommand(osName, osVersion, targetXPUContainerVersion, arch)
		commands = append(commands,
			&exectypes.Command{
				Command: installCmd,
				Timeout: 300 * time.Second,
			},
		)
	} else {
		return nil, fmt.Errorf("unsupported OS: %s", osName)
	}

	// 4. 验证安装结果
	commands = append(commands,
		&exectypes.Command{
			Command: `echo 'after upgrade xpu-container-toolkit, checking new version...'`,
		},
		&exectypes.Command{
			Command: `dpkg -l | grep xpu-container-toolkit || rpm -qa | grep xpu-container-toolkit || echo 'XPU Container Toolkit not found'`,
		},
	)

	return commands, nil
}

// GenerateUpgradeCommands 生成XPU升级命令，供cce-agent使用
func GenerateUpgradeCommands(osName, osVersion, xpuContainerToolkitPath, targetVersion string) []*exectypes.Command {
	var commands []*exectypes.Command

	// 处理架构信息，默认为amd64
	arch := "amd64"

	// 1. 显示当前版本信息
	commands = append(commands,
		&exectypes.Command{
			Command: `echo 'before upgrade xpu-container-toolkit, checking current version...'`,
		},
		&exectypes.Command{
			Command: `dpkg -l | grep xpu-container-toolkit || rpm -qa | grep xpu-container-toolkit || echo 'No XPU Container Toolkit installed'`,
		},
	)

	// 2. 创建目录并下载软件包
	commands = append(commands,
		&exectypes.Command{
			Command: `mkdir -p /deploy/nvidia-container/`,
		},
		&exectypes.Command{
			Command: `rm -f /deploy/nvidia-container/nvidia-container-toolkit.tar.gz`,
		},
		&exectypes.Command{
			Command: fmt.Sprintf("wget -q -O /deploy/nvidia-container/nvidia-container-toolkit.tar.gz %s", xpuContainerToolkitPath),
			Timeout: 240 * time.Second,
			Retry:   5,
		},
	)

	// 3. 根据操作系统生成不同的升级命令
	if strings.EqualFold(osName, string(bccimage.OSNameUbuntu)) {
		// Ubuntu系统的升级逻辑
		// 删除现有版本
		commands = append(commands,
			&exectypes.Command{
				Command: `echo "Starting XPU Container Toolkit uninstallation..." && dpkg -l | grep "xpu-container-toolkit" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled xpu-container-toolkit packages"`,
			},
			&exectypes.Command{
				Command: `dpkg -l | grep "libxpu-container-tools" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled libxpu-container-tools packages"`,
			},
			&exectypes.Command{
				Command: `dpkg -l | grep "libxpu-container1" | awk '{print $2}' | xargs -r dpkg --purge && echo "Uninstalled libxpu-container1 packages"`,
			})

		// 生成安装命令
		installCmd := generateUbuntuInstallCommandForAgent(osVersion, targetVersion, arch)
		commands = append(commands,
			&exectypes.Command{
				Command: installCmd,
				Timeout: 300 * time.Second,
			},
		)

	} else if strings.EqualFold(osName, string(bccimage.OSNameCentOS)) ||
		strings.EqualFold(osName, string(bccimage.OSNameBaiduLinux)) ||
		strings.EqualFold(osName, string(bccimage.OSNameRocky)) {

		// CentOS需要先卸载旧版本
		commands = append(commands,
			&exectypes.Command{
				Command: `echo "Starting XPU Container Toolkit uninstallation..." && rpm -qa | grep xpu-container-toolkit | xargs -r rpm -e --nodeps && echo "Uninstalled xpu-container-toolkit packages"`,
			},
			&exectypes.Command{
				Command: `rpm -qa | grep libxpu-container-tools | xargs -r rpm -e --nodeps && echo "Uninstalled libxpu-container-tools packages"`,
			},
			&exectypes.Command{
				Command: `rpm -qa | grep libxpu-container1 | xargs -r rpm -e --nodeps && echo "Uninstalled libxpu-container1 packages"`,
			})

		// 生成安装命令
		installCmd := generateCentOSInstallCommandForAgent(osName, osVersion, targetVersion, arch)
		commands = append(commands,
			&exectypes.Command{
				Command: installCmd,
				Timeout: 300 * time.Second,
			},
		)
	}

	// 4. 验证安装结果
	commands = append(commands,
		&exectypes.Command{
			Command: `echo 'after upgrade xpu-container-toolkit, checking new version...'`,
		},
		&exectypes.Command{
			Command: `dpkg -l | grep xpu-container-toolkit || rpm -qa | grep xpu-container-toolkit || echo 'XPU Container Toolkit not found'`,
		},
	)

	return commands
}

// generateUbuntuInstallCommand 生成Ubuntu系统的XPU安装命令
func (t *UpgradeXPUContainer) generateUbuntuInstallCommand(osVersion, version, arch string) string {
	// 根据操作系统版本确定路径
	var osPath string
	if strings.HasPrefix(osVersion, "16.04") {
		osPath = "ubuntu16.04"
	} else if strings.HasPrefix(osVersion, "18.04") {
		osPath = "ubuntu18.04"
	} else if strings.HasPrefix(osVersion, "20.04") {
		osPath = "ubuntu20.04"
	} else if strings.HasPrefix(osVersion, "22.04") {
		osPath = "ubuntu22.04"
	} else {
		// 默认使用18.04路径
		osPath = "ubuntu18.04"
	}

	// 根据架构确定包名后缀
	var archSuffix string
	if arch == "arm64" {
		archSuffix = "arm64"
	} else {
		archSuffix = "amd64"
	}

	// 构建安装命令，精简输出
	return fmt.Sprintf(`set -e && set -o pipefail \
&& cd /deploy/nvidia-container/ \
&& apt-get update && apt-get install -y libseccomp2 \
&& rm -rf kunlun \
&& tar zxf nvidia-container-toolkit.tar.gz \
&& cd kunlun/libxpu-container-1.0.2/%s/%s \
&& dpkg -i libxpu-container1_1.0.2-1_%s.deb libxpu-container-tools_1.0.2-1_%s.deb \
&& cd ../../../xpu-container-toolkit-%s/%s/%s \
&& dpkg -i xpu-container-toolkit_%s-1_%s.deb`,
		osPath, archSuffix,
		archSuffix, archSuffix,
		version, osPath, archSuffix,
		version, archSuffix)
}

// generateCentOSInstallCommand 生成CentOS/BaiduLinux/Rocky系统的XPU安装命令
func (t *UpgradeXPUContainer) generateCentOSInstallCommand(osName, osVersion, version, arch string) string {
	// 根据操作系统确定路径
	var osPath string
	if strings.EqualFold(osName, string(bccimage.OSNameCentOS)) {
		if strings.HasPrefix(osVersion, "7.") {
			osPath = "centos7"
		} else if strings.HasPrefix(osVersion, "8.") {
			osPath = "centos8"
		} else {
			osPath = "centos7" // 默认
		}
	} else if strings.EqualFold(osName, string(bccimage.OSNameBaiduLinux)) {
		osPath = "centos7" // BaiduLinux使用centos7路径
	} else if strings.EqualFold(osName, string(bccimage.OSNameRocky)) {
		osPath = "centos8" // Rocky使用centos8路径
	} else {
		osPath = "centos7" // 默认
	}

	// 根据架构确定包名后缀
	var archSuffix string
	if arch == "arm64" || strings.Contains(strings.ToLower(arch), "aarch64") {
		archSuffix = "aarch64"
	} else {
		archSuffix = "x86_64"
	}

	// 构建安装命令，精简输出
	return fmt.Sprintf(`set -e && set -o pipefail \
&& cd /deploy/nvidia-container/ \
&& rm -rf kunlun \
&& tar zxf nvidia-container-toolkit.tar.gz \
&& cd kunlun/libxpu-container-1.0.2/%s/%s \
&& rpm -ivh libxpu-container1-1.0.2-1.%s.rpm libxpu-container-tools-1.0.2-1.%s.rpm \
&& cd ../../../xpu-container-toolkit-%s/%s/%s \
&& rpm -ivh xpu-container-toolkit-%s-1.%s.rpm`,
		osPath, archSuffix,
		archSuffix, archSuffix,
		version, osPath, archSuffix,
		version, archSuffix)
}

// generateUbuntuInstallCommandForAgent 生成Ubuntu系统的XPU安装命令（供Agent使用）
func generateUbuntuInstallCommandForAgent(osVersion, version, arch string) string {
	// 根据操作系统版本确定路径
	var osPath string
	if strings.HasPrefix(osVersion, "16.04") {
		osPath = "ubuntu16.04"
	} else if strings.HasPrefix(osVersion, "18.04") {
		osPath = "ubuntu18.04"
	} else if strings.HasPrefix(osVersion, "20.04") {
		osPath = "ubuntu20.04"
	} else if strings.HasPrefix(osVersion, "22.04") {
		osPath = "ubuntu22.04"
	} else {
		// 默认使用18.04路径
		osPath = "ubuntu18.04"
	}

	// 根据架构确定包名后缀
	var archSuffix string
	if arch == "arm64" {
		archSuffix = "arm64"
	} else {
		archSuffix = "amd64"
	}

	// 构建安装命令，精简输出
	return fmt.Sprintf(`set -e && set -o pipefail \
&& cd /deploy/nvidia-container/ \
&& apt-get update && apt-get install -y libseccomp2 \
&& rm -rf kunlun \
&& tar zxf nvidia-container-toolkit.tar.gz \
&& cd kunlun/libxpu-container-1.0.2/%s/%s \
&& dpkg -i libxpu-container1_1.0.2-1_%s.deb libxpu-container-tools_1.0.2-1_%s.deb \
&& cd ../../../xpu-container-toolkit-%s/%s/%s \
&& dpkg -i xpu-container-toolkit_%s-1_%s.deb`,
		osPath, archSuffix,
		archSuffix, archSuffix,
		version, osPath, archSuffix,
		version, archSuffix)
}

// generateCentOSInstallCommandForAgent 生成CentOS系统的XPU安装命令（供Agent使用）
func generateCentOSInstallCommandForAgent(osName, osVersion, version, arch string) string {
	// 根据操作系统版本确定路径
	var osPath string
	if strings.HasPrefix(osVersion, "7") {
		osPath = "centos7"
	} else if strings.HasPrefix(osVersion, "8") {
		osPath = "centos8"
	} else if strings.HasPrefix(osVersion, "3") {
		osPath = "centos7" // BaiduLinux3使用centos7路径
	} else {
		// 默认使用centos7路径
		osPath = "centos7"
	}

	// 根据架构确定包名后缀
	var archSuffix string
	if arch == "arm64" || strings.Contains(strings.ToLower(arch), "aarch64") {
		archSuffix = "aarch64"
	} else {
		archSuffix = "x86_64"
	}

	// 构建安装命令，精简输出
	return fmt.Sprintf(`set -e && set -o pipefail \
&& cd /deploy/nvidia-container/ \
&& rm -rf kunlun \
&& tar zxf nvidia-container-toolkit.tar.gz \
&& cd kunlun/libxpu-container-1.0.2/%s/%s \
&& rpm -ivh libxpu-container1-1.0.2-1.%s.rpm libxpu-container-tools-1.0.2-1.%s.rpm \
&& cd ../../../xpu-container-toolkit-%s/%s/%s \
&& rpm -ivh xpu-container-toolkit-%s-1.%s.rpm`,
		osPath, archSuffix,
		archSuffix, archSuffix,
		version, osPath, archSuffix,
		version, archSuffix)
}

//gocover:ignore
func (t *UpgradeXPUContainer) GetAgentPodAndConfigMap(ctx context.Context, instanceId string) (*corev1.Pod, *corev1.ConfigMap, error) {
	// 1、前期准备
	xpuContainerToolkitPath, instance, needXPU, err := t.prepareUpgradeXPUContainer(ctx, instanceId)
	if err != nil {
		return nil, nil, fmt.Errorf("prepareUpgradeXPUContainer failed: %v", err)
	}
	// 非 XPU 机器跳过升级
	if !needXPU {
		logger.Infof(ctx, "InstanceType %s != XPU, skip upgrade xpu-container-toolkit", instance.Spec.InstanceType)
		return nil, nil, nil
	}

	// 2、构造参数
	nodeName, err := t.GetK8sNodeName(ctx, instanceId)
	if err != nil {
		return nil, nil, fmt.Errorf("GetK8sNodeName failed: %v", err)
	}
	podName := fmt.Sprintf("upgrade-xpu-container-%v-%v", t.config.TargetVersion, nodeName)

	agentImage, err := t.GetAgentImage()
	if err != nil {
		return nil, nil, fmt.Errorf("GetAgentImage failed: %v", err)
	}
	nodeIp := t.CCEInstanceIDToVPCIP[instanceId]
	osName := instance.Spec.InstanceOS.OSName
	osVersion := instance.Spec.InstanceOS.OSVersion
	operatingSystem := fmt.Sprintf("%s-%s", osName, osVersion)

	k8s := t.Cluster.Spec.K8SVersion
	runtime := string(instance.Spec.RuntimeType) + "-" + instance.Spec.RuntimeVersion
	rpmName := strings.ToLower(fmt.Sprintf("%s-%s-%s-xpu-container-toolkit", osName, k8s, runtime))

	// 3、获取pod
	pod, err := upgrade.GetAgentPod(podName, agentImage, "", "",
		nodeIp, nodeName, ccetypes.K8SVersion(""), "",
		ccetypes.RuntimeType(""), ccetypes.RuntimeType(""),
		"", "", rpmName, operatingSystem, t.config.TargetVersion, xpuContainerToolkitPath)
	if err != nil {
		return nil, nil, fmt.Errorf("GetAgentPod failed: %v", err)
	}
	return pod, nil, nil
}

// CheckComponentVersionAfterUpgrade 实现Interface, 用于在升级后检查XPU Container Toolkit版本
//
//gocover:ignore
func (t *UpgradeXPUContainer) CheckComponentVersionAfterUpgrade(ctx context.Context, instanceId string) error {
	// 检查instanceCRD上的xpuContainerToolkitVersion字段
	// 实际的版本验证已经在安装脚本的最后阶段完成
	instanceCrd, err := t.Clients.MetaClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace,
		instanceId, &metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("get instance %s failed, error: %s", instanceId, err.Error())
	}
	if instanceCrd.Spec.XPUContainerToolkitVersion != t.config.TargetVersion {
		return fmt.Errorf("checkComponentVersionAfterUpgrade failed, expected: %s, actual: %s",
			t.config.TargetVersion, instanceCrd.Spec.XPUContainerToolkitVersion)
	}

	return nil
}

func (t *UpgradeXPUContainer) targetVersion() string {
	return t.config.TargetVersion
}

//gocover:ignore
func (t *UpgradeXPUContainer) UpdateInstanceCRDComponentVersion(ctx context.Context, instanceId string) error {
	if instanceId == "" {
		return fmt.Errorf("instanceId is empty")
	}

	instanceCrd, err := t.Clients.MetaClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace,
		instanceId, &metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("get instance %s failed, error: %s", instanceId, err.Error())
	}

	updateInstanceCrd := instanceCrd.DeepCopy()
	targetXPUContainerVersion := t.config.TargetVersion
	updateInstanceCrd.Spec.XPUContainerToolkitVersion = targetXPUContainerVersion

	// 更新节点CRD配置
	err = t.Clients.MetaClient.UpdateInstance(ctx, consts.MetaClusterDefaultNamespace, instanceId, updateInstanceCrd)
	if err != nil {
		return fmt.Errorf("update instance %s failed, error: %s", instanceId, err.Error())
	}
	return nil
}

func (t *UpgradeXPUContainer) GetAgentConfigMap(ctx context.Context, instanceId string) (*corev1.ConfigMap, error) {
	return nil, nil
}
