package xpu_container

import (
	"context"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup/upgrade/xpu_container"
)

type UpgradeXPUContainer struct {
	ExecClient exec.Interface
}

type UpgradeXPUContainerOptions struct {
	TargetXPUContainerVersion string
	XPUContainerAddress       string
	RpmName                   string
	// OperatingSystem 格式为 "{OSName-OSVersion}"，使用字符 - 分割
	OperatingSystem string
}

func (u *UpgradeXPUContainer) Run(ctx context.Context, options UpgradeXPUContainerOptions) error {
	if options.TargetXPUContainerVersion == "" {
		return fmt.Errorf("need TargetXPUContainerVersion")
	}
	if options.XPUContainerAddress == "" {
		return fmt.Errorf("need XPUContainerAddress")
	}
	if options.OperatingSystem == "" {
		return fmt.Errorf("need OperatingSystem")
	}

	index := strings.Index(options.OperatingSystem, "-")
	if index <= 0 || index == len(options.OperatingSystem)-1 {
		return fmt.Errorf("invalid OperatingSystem")
	}

	// options.OperatingSystem[:index]是OSName，options.OperatingSystem[index+1:]是OSVersion
	osName := options.OperatingSystem[:index]
	osVersion := options.OperatingSystem[index+1:]

	logger.Infof(ctx, "Starting XPU Container Toolkit upgrade: version=%s, os=%s-%s",
		options.TargetXPUContainerVersion, osName, osVersion)

	// 生成升级命令
	commands := xpu_container.GenerateUpgradeCommands(osName, osVersion,
		options.XPUContainerAddress, options.TargetXPUContainerVersion)

	for i, cmd := range commands {
		logger.Infof(ctx, "Executing XPU upgrade command %d/%d: %s", i+1, len(commands), cmd.Command)
		_, stderr, err := u.ExecClient.Exec(ctx, cmd)
		if err != nil {
			logger.Errorf(ctx, "XPU upgrade exec failed, err:%v, stderr:%v, cmd:%v", err, stderr, cmd.Command)
			return fmt.Errorf("XPU upgrade command failed: %v, stderr: %s", err, stderr)
		}
		logger.Infof(ctx, "XPU upgrade command %d completed successfully", i+1)
	}

	logger.Infof(ctx, "XPU Container Toolkit upgrade completed successfully")
	return nil
}
