{{- if .Values.CCEImageSecretEnabled }}
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-system
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: kube-public
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-devops
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: health-check
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-monitor
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: promtail
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
{{- end }}
