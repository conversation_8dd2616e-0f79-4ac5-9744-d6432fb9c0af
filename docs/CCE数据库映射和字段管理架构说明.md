# CCE数据库映射和字段管理架构说明

## 概述

本文档详细说明CCE项目中数据库映射、字段管理和Service层交互的架构设计，帮助开发者理解各层之间的关系和职责分工。

## 核心架构层次

### 1. 数据库表（物理存储层）

#### 节点表：`t_cce_instance`
- **表名**：`t_cce_instance`
- **特点**：包含直接的计费方式字段
- **关键字段**：
  ```sql
  `instance_charging_type` varchar(64) NOT NULL DEFAULT '' COMMENT 'instance charging type'
  `instance_name` varchar(256) NOT NULL DEFAULT '' COMMENT 'instance name'
  `cluster_id` varchar(32) NOT NULL DEFAULT '' COMMENT 'cluster ID'
  ```

#### 节点组表：`t_cce_instance_group`
- **表名**：`t_cce_instance_group`
- **特点**：没有直接的计费方式字段，信息存储在JSON中
- **关键字段**：
  ```sql
  `instance_template` varchar(8192) NOT NULL DEFAULT '{}' COMMENT 'instance spec template in json'
  ```
- **JSON结构示例**：
  ```json
  {
    "instanceChargingType": "Prepaid",
    "instanceType": "bcc.g1.c1m1",
    "imageId": "image-xxx"
  }
  ```

### 2. 字段定义层（`pkg/types/instance.go`）

#### 作用和特点
- **字段定义中心**：定义所有Instance相关的字段
- **映射关系定义**：通过gorm标签定义Go字段到数据库字段的映射
- **业务完整性**：包含完整的业务逻辑字段
- **字段分类**：既有数据库字段，也有纯业务逻辑字段

#### 字段分类

##### 数据库字段（`gorm:"column:xxx"`）
```go
// 存储到数据库的字段
InstanceChargingType bcc.PaymentTiming `json:"instanceChargingType,omitempty" gorm:"column:instance_charging_type"`
InstanceName         string            `json:"instanceName,omitempty" gorm:"column:instance_name"`
ClusterID           string            `json:"clusterID,omitempty" gorm:"column:cluster_id"`
MachineType         MachineType       `json:"machineType,omitempty" gorm:"column:machine_type"`
```

##### 非数据库字段（`gorm:"-"`）
```go
// 不存储到数据库的字段
InstanceTemplateID    string              `json:"instanceTemplateID,omitempty" gorm:"-"`
IAMRole              *IAMRole            `json:"iamRole,omitempty" gorm:"-"`
UserData             string              `json:"userData,omitempty" gorm:"-"`
CheckGPUDriver       bool                `json:"checkGPUDriver,omitempty" gorm:"-"`
ReconcileSteps       map[StepName]Step   `json:"reconcileSteps,omitempty" gorm:"-"`
ErrorMessage         string              `json:"errorMessage,omitempty" gorm:"-"`
```

#### 设计原因
1. **业务完整性**：满足所有业务逻辑需求
2. **存储优化**：不是所有字段都需要持久化
3. **性能考虑**：临时计算字段、状态字段等不需要存储
4. **数据分离**：配置类数据和状态类数据分离

### 3. ORM模型层（`pkg/models/t_cce_instance.go`）

#### 作用和特点
- **结构组合器**：将多个结构体组合成完整的数据库模型
- **ORM入口**：GORM通过这个结构体与数据库交互
- **一比一映射**：通过EMBEDDED实现与数据库表的完整映射

#### 结构定义
```go
// Instance mapping to t_cce_instance
type Instance struct {
    BaseModel                                    // 基础字段（id, created_at, updated_at等）
    
    Spec   *ccetypes.InstanceSpec   `json:"spec" gorm:"EMBEDDED"`     // 嵌入InstanceSpec的所有字段
    Status *ccetypes.InstanceStatus `json:"status" gorm:"EMBEDDED"`   // 嵌入InstanceStatus的所有字段
}

func (*Instance) TableName() string {
    return "t_cce_instance"
}
```

#### GORM EMBEDDED的作用
当GORM看到`EMBEDDED`标签时，会将嵌入结构体的所有字段展开：

```go
// 概念上GORM看到的结构
type Instance struct {
    // BaseModel字段
    ID        uint      `gorm:"primary_key"`
    CreatedAt time.Time
    UpdatedAt time.Time
    
    // InstanceSpec字段（展开）
    InstanceChargingType bcc.PaymentTiming `gorm:"column:instance_charging_type"`
    InstanceName         string            `gorm:"column:instance_name"`
    ClusterID           string            `gorm:"column:cluster_id"`
    
    // InstanceStatus字段（展开）
    InstancePhase       InstancePhase     `gorm:"column:instance_phase"`
    // ... 其他字段
}
```

## Service层交互模式

### 1. 与Model层的交互

#### 间接交互：通过Model层访问字段
```go
// Service层代码
instancesDB := instanceListDB.Items  // []*models.Instance

// 访问pkg/types/instance.go中定义的字段
for _, instance := range instancesDB {
    chargingType := instance.Spec.InstanceChargingType  // 间接访问
    instanceName := instance.Spec.InstanceName
}
```

### 2. 与pkg/types的直接交互

#### 类型定义和常量使用
```go
// 直接导入和使用
import ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"

// 方法签名中使用类型
func (s *service) ListInstancesByPage(ctx context.Context, 
    clusterRole ccetypes.ClusterRole, // 直接使用类型
    // ... 其他参数
) (*ccesdk.InstancePage, error)
```

#### 业务逻辑判断
```go
// 使用常量进行业务判断
if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
    continue
}

if instancePhase == ccetypes.InstancePhaseRunning {
    // 处理逻辑
}
```

#### 对象创建和构造
```go
// 创建对象时使用类型和常量
instancesSDK = append(instancesSDK, &ccesdk.Instance{
    Spec: &ccesdk.InstanceSpec{
        ClusterRole: ccetypes.ClusterRoleNode,  // 使用常量
    },
    Status: &ccesdk.InstanceStatus{
        InstancePhase: ccetypes.InstancePhaseRunning,  // 使用状态常量
    },
})
```

## 查询方式的差异

### 节点表查询（直接字段）
```go
// Model层：直接查询数据库字段
queryMap["instance_charging_type"] = *option.ChargingType
```

### 节点组表查询（JSON字段）
```go
// Model层：使用JSON_EXTRACT查询
q = q.Where("JSON_EXTRACT(instance_template, '$.instanceChargingType') = ?", *option.ChargingType)
```

## 数据流转完整过程

```
API请求 (JSON)          Go结构体              数据库表
instanceChargingType → InstanceChargingType → instance_charging_type
     ↑                        ↑                       ↑
  json标签定义            Go字段名              gorm标签定义
```

### 具体流程
1. **API请求**：`{"instanceChargingType": "Prepaid"}`
2. **JSON反序列化**：根据`json:"instanceChargingType"`标签，映射到Go结构体的`InstanceChargingType`字段
3. **数据库操作**：根据`gorm:"column:instance_charging_type"`标签，映射到数据库的`instance_charging_type`字段

## 架构优势

### 1. 职责分离
- **SQL文件**：定义数据库表的物理结构
- **pkg/types**：定义字段和映射关系，管理业务类型
- **pkg/models**：组合完整模型，提供ORM接口

### 2. 灵活性
- 支持数据库字段和非数据库字段的统一管理
- 通过gorm标签灵活控制字段的存储行为
- 支持复杂的嵌入结构

### 3. 可维护性
- 类型安全：使用强类型而不是字符串
- 常量管理：统一管理业务常量
- 一致性：确保整个系统使用相同的类型定义

## 总结

- **`pkg/types/instance.go`**：字段定义的完整集合，包含数据库字段和业务字段
- **`pkg/models/t_cce_instance.go`**：结构组合的入口点，实现与数据库的一比一映射
- **Service层**：既直接使用pkg/types的类型和常量，也通过Model层间接访问字段
- **数据库表**：最终的物理存储，通过gorm标签与Go结构体建立映射关系

这种分层设计实现了职责分离，使代码更易维护和测试，同时保证了类型安全和业务逻辑的完整性。
