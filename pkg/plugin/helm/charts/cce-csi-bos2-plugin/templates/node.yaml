{{- range $i, $v := .Values.cluster.nodes }}
---
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: csi-bos2plugin-node-server-{{ $i }}
  namespace: kube-system
spec:
  selector:
    matchLabels:
      app: csi-bos2plugin-node-server-{{ $i }}
  template:
    metadata:
      labels:
        app: csi-bos2plugin-node-server-{{ $i }}
    spec:
      serviceAccount: csi-bos2-external-runner
      hostNetwork: true
      restartPolicy: Always
      priorityClassName: system-node-critical
      {{- if $v.kubeletRootPathAffinity }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: cce.baidubce.com/kubelet-dir
                    operator: In
                    values:
                      - {{ printf "%x" $v.kubeletRootPath }}
      {{- end }}
      containers:
        - name: node-driver-registrar
          image: {{ template "registrar-image.registry" $ }}/{{ template "registrar-image.repository" $ }}:{{ template "registrar-image.tag" $ }}
          args:
            - "--csi-address=$(ADDRESS)"
            - "--kubelet-registration-path=$(ADDRESS)"
            - "--v={{ $.Values.logLevel }}"
          env:
            - name: ADDRESS
              value: {{ $v.kubeletRootPath }}/plugins/{{ $.Values.driverName }}/csi.sock
          imagePullPolicy: "Always"
          volumeMounts:
            - name: reg-dir
              mountPath: /registration
            - name: kubelet-dir
              mountPath: {{ $v.kubeletRootPath }}
        - name: csi-bos2plugin
          securityContext:
            privileged: true
            capabilities:
              add: ["SYS_ADMIN"]
            allowPrivilegeEscalation: true
          image: {{ template "plugin-image.registry" $ }}/{{ template "plugin-image.repository" $ }}:{{ template "plugin-image.tag" $ }}
          args :
            - "--override-driver-name={{ $.Values.driverName }}"
            - "--driver-type=bos2"
            - "--csi-endpoint=$(CSI_ENDPOINT)"
            - "--driver-mode=node"
            - "--auth-mode={{ $.Values.authMode | default "key" }}"
            - "--max-volumes-per-node={{ $.Values.maxVolumesPerNode }}"
            {{- if $.Values.topologyMode }}
            - "--topology-mode={{ $.Values.topologyMode }}"
            {{- end }}
            {{- if $.Values.region }}
            - "--region={{ $.Values.region }}"
            {{- end }}
            {{- if $.Values.clusterID }}
            - "--cluster-id={{ $.Values.clusterID }}"
            {{- end }}
            {{- if $.Values.endpoint.bos }}
            - "--bos-endpoint={{ $.Values.endpoint.bos }}"
            {{- end }}
            - "--bosfs2-image={{ template "bosfs2-image.registry" $ }}/{{ template "bosfs2-image.repository" $ }}:{{ template "bosfs2-image.tag" $ }}"
            - "--logtostderr"
            - "--v={{ $.Values.logLevel }}"
          env:
            - name: CSI_ENDPOINT
              value: unix:/{{ $v.kubeletRootPath }}/plugins/{{ $.Values.driverName }}/csi.sock
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            {{- with $.Values.endpoint.cceGateway }}
            - name: CCE_GATEWAY_ENDPOINT
              value: {{ $.Values.endpoint.cceGateway }}
            {{- end }}
          imagePullPolicy: "Always"
          volumeMounts:
            - name: kubelet-dir
              mountPath: {{ $v.kubeletRootPath }}
              mountPropagation: "Bidirectional"
            - name: host-sys
              mountPath: /sys
            - name: host-var-run
              mountPath: /var/run
            - name: bosfs2-cred-dir
              mountPath: /etc/bosfs2/credentials
      {{- with $.Values.nodeServerTolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: kubelet-dir
          hostPath:
            path: {{ $v.kubeletRootPath }}
            type: DirectoryOrCreate
        - name: reg-dir
          hostPath:
            path: {{ $v.kubeletRootPath }}/plugins_registry
            type: DirectoryOrCreate
        - name: host-sys
          hostPath:
            path: /sys
        - name: host-var-run
          hostPath:
            path: {{ $.Values.dockerSocketPath }}
            type: ""
        - name: bosfs2-cred-dir
          hostPath:
            path: /etc/bosfs2/credentials
            type: DirectoryOrCreate
{{- end }}
