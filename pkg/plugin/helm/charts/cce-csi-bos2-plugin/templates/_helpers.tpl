{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "cce-csi-bos2-plugin.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "cce-csi-bos2-plugin.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "cce-csi-bos2-plugin.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "cce-csi-bos2-plugin.labels" -}}
helm.sh/chart: {{ include "cce-csi-bos2-plugin.chart" . }}
{{ include "cce-csi-bos2-plugin.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "cce-csi-bos2-plugin.selectorLabels" -}}
app.kubernetes.io/name: {{ include "cce-csi-bos2-plugin.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "cce-csi-bos2-plugin.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "cce-csi-bos2-plugin.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
CSI Driver name
*/}}
{{- define "cce-csi-bos2-plugin.driver-name" -}}
csi-bos2plugin
{{- end -}}

{{/*
CSI Node Registrar Image
*/}}
{{- define "registrar-image.registry" -}}
{{- if .Values.images.registrar.registry -}}
{{- .Values.images.registrar.registry -}}
{{- else -}}
{{- .Values.images.globalRegistry | default "registry.baidubce.com" -}}
{{- end -}}
{{- end -}}

{{- define "registrar-image.repository" -}}
{{- $name := .Values.images.registrar.repository | default "cce-plugin-pro/csi-node-driver-registrar" -}}
{{- if .Values.cluster.arm64 -}}
{{- printf "%s-arm64" $name -}}
{{- else -}}
{{- $name -}}
{{- end -}}
{{- end -}}

{{- define "registrar-image.tag" -}}
{{- .Values.images.registrar.tag | default "v2.1.0" -}}
{{- end -}}


{{/*
CSI Plugin Image
*/}}
{{- define "plugin-image.registry" -}}
{{- if .Values.images.plugin.registry -}}
{{- .Values.images.plugin.registry -}}
{{- else -}}
{{- .Values.images.globalRegistry | default "registry.baidubce.com" -}}
{{- end -}}
{{- end -}}

{{- define "plugin-image.repository" -}}
{{- $name := .Values.images.plugin.repository | default "cce-plugin-pro/cce-csi-plugin" -}}
{{- if .Values.cluster.arm64 -}}
{{- printf "%s-arm64" $name -}}
{{- else -}}
{{- $name -}}
{{- end -}}
{{- end -}}

{{- define "plugin-image.tag" -}}
{{- .Values.images.plugin.tag | default "v0.0.1" -}}
{{- end -}}


{{/*
bosfs2 Image
*/}}
{{- define "bosfs2-image.registry" -}}
{{- if .Values.images.bosfs2.registry -}}
{{- .Values.images.bosfs2.registry -}}
{{- else -}}
{{- .Values.images.globalRegistry | default "registry.baidubce.com" -}}
{{- end -}}
{{- end -}}

{{- define "bosfs2-image.repository" -}}
{{- $name := .Values.images.bosfs2.repository | default "cce-plugin-pro/csi-bosfs2" -}}
{{- if .Values.cluster.arm64 -}}
{{- printf "%s-arm64" $name -}}
{{- else -}}
{{- $name -}}
{{- end -}}
{{- end -}}

{{- define "bosfs2-image.tag" -}}
{{- .Values.images.bosfs2.tag | default "v0.0.1" -}}
{{- end -}}