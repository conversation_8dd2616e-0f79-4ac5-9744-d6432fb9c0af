CCE-CSI-BOS2-Plugin
=======

CCE CSI BOS2 Plugin 为百度云CCE集群中可用的BOS磁盘插件。

**注意**：插件仅支持K8S版本>=1.16版本的CCE kubernetes集群。


## Introduction
------------

容器的特性决定了容器本身是非持久化的，容器被删除，其上的数据也一并删除。bos可以解决容器的数据共享和持久化存储问题，适用于大数据分析、媒体、游戏等很多场景。
而PV（PersistentVolume）和PVC（PersistentVolumeClaim）是K8S提供的用于抽象存储细节的API资源。利用百度云BOS对象存储在集群内创建PV和PVC资源，用户可以直接将bos bucket作为存储卷挂载到容器中，而无需关注底层的实现细节，从而更加便捷地为容器集群提供持久化存储方案。

## Prerequisites
-------------

- 百度云CCE Kubernetes集群（K8S版本>=1.16）

## Configuration

| Parameter | Description | Default |
| --- | --- | --- |
| region | 集群所在地域名称缩写 | bj |
| cluster.arm64 | 是否为arm64架构集群 | false |
| cluster.nodes.kubeletRootPath | kubelet数据目录 | /home/<USER>/kubelet |
| cluster.nodes.kubeletRootPathAffinity | plugin是否开启节点亲和性 | true |

## Changelog

- 0.0.1 初始版本，支持在CCE集群部署bosfs2