# Default values for cce-csi-bos-plugin.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Support CCE cluster whose k8s version >= 1.16.
region: bj # 需要修改为集群实际的 region: bj, gz, su, hkg, bd, fwh, nj
clusterID: 

cluster:
  arm64: false
  nodes:
  - kubeletRootPath: "/home/<USER>/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/data/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/var/lib/kubelet"
    kubeletRootPathAffinity: true

authMode: key
driverName: csi-bos2plugin
topologyMode: auto
maxVolumesPerNode: 5
logLevel: 2
dockerSocketPath: /var/run

nodeServerTolerations:
- operator: Exists # node server tolerates all taints by default.

endpoint:
  bos:
  cceGateway:

images:
  globalRegistry: registry.baidubce.com
  registrar: 
    repository: cce-plugin-pro/csi-node-driver-registrar
    tag: ""
  plugin:
    repository: cce-plugin-pro/cce-csi-plugin
    tag: ""
  bosfs2:
    repository: cce-plugin-pro/csi-bosfs2
    tag: ""
