package addon

import (
	"bytes"
	"context"
	"errors"
	"text/template"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
)

const (
	AddOnCCECSIRapidFSPlugin Type = "cce-csi-rapidfs-plugin"

	helmChartAddOnCCECSIRapidFSPlugin HelmChartName = "cce-csi-rapidfs-plugin"
)

func init() {
	SupportAddons[AddOnCCECSIRapidFSPlugin] = nil
	HelmSupportedAddons[AddOnCCECSIRapidFSPlugin] = nil
}

type CSIRapidFSPlugin struct {
	base   *clients.Clients
	config *CSIRapidFSPluginConfig
}

type CSIRapidFSPluginConfig struct {
	clusterID string
}

func (c *CSIRapidFSPluginConfig) Config() {}

func NewCSIRapidFSPlugin(ctx context.Context, base *clients.Clients, c Config) (Interface, error) {
	if base == nil {
		return nil, errors.New("base is nil")
	}

	config := &CSIRapidFSPluginConfig{
		clusterID: base.Cluster.Spec.ClusterID,
	}

	return &CSIRapidFSPlugin{
		base:   base,
		config: config,
	}, nil
}

func (c *CSIRapidFSPlugin) Name(ctx context.Context) Type {
	return AddOnCCECSIRapidFSPlugin
}

// Namespace - 插件部署命名空间
func (c *CSIRapidFSPlugin) Namespace(ctx context.Context) string {
	return ""
}

func (c *CSIRapidFSPlugin) HelmChartName(ctx context.Context) HelmChartName {
	return helmChartAddOnCCECSIRapidFSPlugin
}

func (c *CSIRapidFSPlugin) HelmChartVersion(ctx context.Context) string {
	return "" // return empty means install the latest version by default
}

func (c *CSIRapidFSPlugin) ModifyHelmChartValues(ctx context.Context, values string) (string, error) {
	// modify nothing and just return the origin values
	return "", nil
}

func (c *CSIRapidFSPlugin) RenderValuesYAML(ctx context.Context, valuesTemp string) (string, error) {
	tmpl, err := template.New("CSIRapidFSPlugin").Parse(valuesTemp)
	if err != nil {
		logger.Errorf(ctx, "template.Parse failed: %v", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, &struct {
	}{}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %v", err)
		return "", err
	}

	logger.Infof(ctx, "CSIRapidFSPlugin YAML: %s", buf.String())

	return buf.String(), nil
}
