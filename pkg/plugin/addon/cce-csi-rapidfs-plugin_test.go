// nolint
package addon

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

type MockConfig struct{}

// 实现 Config 接口
func (c *MockConfig) Config() {}

func TestNewCSIRapidFSPlugin(t *testing.T) {
	tests := []struct {
		name    string
		base    *clients.Clients
		wantErr bool
	}{
		{
			name: "success case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
			wantErr: false,
		},
		{
			name:    "nil base case",
			base:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			cfg := &MockConfig{}
			got, err := NewCSIRapidFSPlugin(ctx, tt.base, cfg)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				plugin, ok := got.(*CSIRapidFSPlugin)
				assert.True(t, ok)
				assert.Equal(t, tt.base, plugin.base)
				assert.Equal(t, tt.base.Cluster.Spec.ClusterID, plugin.config.clusterID)
			}
		})
	}
}

func TestCSIRapidFSPlugin_Name(t *testing.T) {
	tests := []struct {
		name string
		base *clients.Clients
	}{
		{
			name: "normal case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
		},
		{
			name: "nil base case",
			base: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base: tt.base,
				config: &CSIRapidFSPluginConfig{
					clusterID: "test-cluster-id",
				},
			}
			got := plugin.Name(ctx)
			assert.Equal(t, AddOnCCECSIRapidFSPlugin, got)
		})
	}
}

func TestCSIRapidFSPlugin_Namespace(t *testing.T) {
	tests := []struct {
		name     string
		base     *clients.Clients
		config   *CSIRapidFSPluginConfig
		expected string
	}{
		{
			name: "normal case with cluster ID",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
			config: &CSIRapidFSPluginConfig{
				clusterID: "test-cluster-id",
			},
			expected: "",
		},
		{
			name:     "nil base case",
			base:     nil,
			config:   nil,
			expected: "",
		},
		{
			name: "empty cluster ID case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "",
					},
				},
			},
			config: &CSIRapidFSPluginConfig{
				clusterID: "",
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base:   tt.base,
				config: tt.config,
			}
			got := plugin.Namespace(ctx)
			assert.Equal(t, tt.expected, got)
		})
	}
}

func TestCSIRapidFSPlugin_HelmChartName(t *testing.T) {
	tests := []struct {
		name string
		base *clients.Clients
	}{
		{
			name: "normal case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
		},
		{
			name: "nil base case",
			base: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base: tt.base,
				config: &CSIRapidFSPluginConfig{
					clusterID: "test-cluster-id",
				},
			}
			got := plugin.HelmChartName(ctx)
			assert.Equal(t, helmChartAddOnCCECSIRapidFSPlugin, got)
		})
	}
}

func TestCSIRapidFSPlugin_HelmChartVersion(t *testing.T) {
	tests := []struct {
		name     string
		base     *clients.Clients
		config   *CSIRapidFSPluginConfig
		expected string
	}{
		{
			name: "normal case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
			config: &CSIRapidFSPluginConfig{
				clusterID: "test-cluster-id",
			},
			expected: "",
		},
		{
			name:     "nil base case",
			base:     nil,
			config:   nil,
			expected: "",
		},
		{
			name: "empty config case",
			base: &clients.Clients{
				Cluster: &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ClusterID: "test-cluster-id",
					},
				},
			},
			config:   nil,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base:   tt.base,
				config: tt.config,
			}
			got := plugin.HelmChartVersion(ctx)
			assert.Equal(t, tt.expected, got)
		})
	}
}

func TestCSIRapidFSPlugin_ModifyHelmChartValues(t *testing.T) {
	tests := []struct {
		name     string
		base     *clients.Clients
		config   *CSIRapidFSPluginConfig
		input    string
		expected string
		wantErr  bool
	}{
		{
			name:     "normal case",
			base:     &clients.Clients{},
			config:   &CSIRapidFSPluginConfig{},
			input:    "test-values",
			expected: "",
			wantErr:  false,
		},
		{
			name:     "nil config case",
			base:     &clients.Clients{},
			config:   nil,
			input:    "test-values",
			expected: "",
			wantErr:  false,
		},
		{
			name:     "nil base case",
			base:     nil,
			config:   &CSIRapidFSPluginConfig{},
			input:    "test-values",
			expected: "",
			wantErr:  false,
		},
		{
			name:     "empty input case",
			base:     &clients.Clients{},
			config:   &CSIRapidFSPluginConfig{},
			input:    "",
			expected: "",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base:   tt.base,
				config: tt.config,
			}
			got, err := plugin.ModifyHelmChartValues(ctx, tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, got)
			}
		})
	}
}

func TestCSIRapidFSPlugin_RenderValuesYAML(t *testing.T) {
	tests := []struct {
		name       string
		base       *clients.Clients
		config     *CSIRapidFSPluginConfig
		valuesTemp string
		expected   string
		wantErr    bool
	}{
		{
			name:       "normal template",
			base:       &clients.Clients{},
			config:     &CSIRapidFSPluginConfig{},
			valuesTemp: "key: key1",
			expected:   "key: key1",
			wantErr:    false,
		},
		{
			name:       "empty template",
			base:       &clients.Clients{},
			config:     &CSIRapidFSPluginConfig{},
			valuesTemp: "",
			expected:   "",
			wantErr:    false,
		},
		{
			name:       "invalid template",
			base:       &clients.Clients{},
			config:     &CSIRapidFSPluginConfig{},
			valuesTemp: "key: {{ .invalid ",
			expected:   "",
			wantErr:    true,
		},
		{
			name:       "nil config",
			base:       &clients.Clients{},
			config:     nil,
			valuesTemp: "key: value",
			expected:   "key: value",
			wantErr:    false,
		},
		{
			name:       "nil base",
			base:       nil,
			config:     &CSIRapidFSPluginConfig{},
			valuesTemp: "key: value",
			expected:   "key: value",
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			plugin := &CSIRapidFSPlugin{
				base:   tt.base,
				config: tt.config,
			}
			got, err := plugin.RenderValuesYAML(ctx, tt.valuesTemp)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, got)
			}
		})
	}
}
