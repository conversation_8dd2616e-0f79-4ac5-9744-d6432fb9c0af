package bbc

import (
    "bytes"
    "context"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gorilla/mux"
    "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
)

// setup a local http server to validate request path, method and body
func setupBBCSDKTestServer(t *testing.T, routes map[string]func(w http.ResponseWriter, r *http.Request)) (*Client, *httptest.Server) {
    t.Helper()

    // dummy credentials
    credentials := &bce.Credentials{AccessKeyID: "ak", SecretAccessKey: "sk"}
    cfg := &bce.Config{Credentials: credentials, Checksum: true}

    cli := NewClient(cfg)
    cli.SetDebug(true)

    r := mux.NewRouter()
    for p, h := range routes {
        r.HandleFunc(p, h)
    }

    srv := httptest.NewServer(r)
    cli.Endpoint = srv.URL
    return cli, srv
}

func TestBindEnterpriseSecurityGroup_Success(t *testing.T) {
    // expected request matcher
    handler := func(w http.ResponseWriter, r *http.Request) {
        if r.Method != http.MethodPost {
            t.Fatalf("unexpected method: %s", r.Method)
        }
        if r.URL.Path != "/v1/instance/enterpriseSecurityGroups" {
            t.Fatalf("unexpected path: %s", r.URL.Path)
        }
        if _, ok := r.URL.Query()["bind"]; !ok {
            t.Fatalf("missing bind param in query: %v", r.URL.RawQuery)
        }

        // read body
        buf := new(bytes.Buffer)
        _, _ = buf.ReadFrom(r.Body)
        var payload struct {
            InstanceIds              []string `json:"instanceIds"`
            EnterpriseSecurityGroups []string `json:"enterpriseSecurityGroups"`
        }
        if err := json.Unmarshal(buf.Bytes(), &payload); err != nil {
            t.Fatalf("unmarshal body failed: %v", err)
        }
        if len(payload.InstanceIds) != 1 || payload.InstanceIds[0] != "i-1" {
            t.Fatalf("unexpected instanceIds: %v", payload.InstanceIds)
        }
        if len(payload.EnterpriseSecurityGroups) != 2 || payload.EnterpriseSecurityGroups[0] != "esg-a" || payload.EnterpriseSecurityGroups[1] != "esg-b" {
            t.Fatalf("unexpected esg ids: %v", payload.EnterpriseSecurityGroups)
        }

        // empty body with 200 indicates success
        w.WriteHeader(http.StatusOK)
        _, _ = w.Write([]byte(""))
    }

    client, server := setupBBCSDKTestServer(t, map[string]func(http.ResponseWriter, *http.Request){
        "/v1/instance/enterpriseSecurityGroups": handler,
    })
    defer server.Close()

    err := client.BindEnterpriseSecurityGroup(context.TODO(), []string{"i-1"}, []string{"esg-a", "esg-b"}, NewSignOption())
    if err != nil {
        t.Fatalf("BindEnterpriseSecurityGroup returned error: %v", err)
    }
}

func TestBindEnterpriseSecurityGroup_InvalidArgs(t *testing.T) {
    client, server := setupBBCSDKTestServer(t, map[string]func(http.ResponseWriter, *http.Request){})
    defer server.Close()

    // empty instanceIDs
    if err := client.BindEnterpriseSecurityGroup(context.TODO(), []string{}, []string{"esg-a"}, NewSignOption()); err == nil {
        t.Fatalf("expected error when instanceIDs empty")
    }
    // nil esg ids
    if err := client.BindEnterpriseSecurityGroup(context.TODO(), []string{"i-1"}, nil, NewSignOption()); err == nil {
        t.Fatalf("expected error when esg ids nil")
    }
}

