package securitygroup

import (
	"context"
	"errors"
	"sort"
	"testing"
	"time"

	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/baidubce/bce-sdk-go/services/esg"
	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc/mock"
	bbcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc/mock"
	bccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	hpasmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/hpas/mock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

// stringSliceEqualIgnoreOrder 是一个自定义的 gomock 匹配器，用于比较两个字符串切片是否包含相同的元素（忽略顺序）
type stringSliceEqualIgnoreOrder struct {
	expected []string
}

func (m stringSliceEqualIgnoreOrder) Matches(x interface{}) bool {
	actual, ok := x.([]string)
	if !ok {
		return false
	}
	if len(actual) != len(m.expected) {
		return false
	}

	// 复制并排序两个切片进行比较
	actualCopy := make([]string, len(actual))
	expectedCopy := make([]string, len(m.expected))
	copy(actualCopy, actual)
	copy(expectedCopy, m.expected)
	sort.Strings(actualCopy)
	sort.Strings(expectedCopy)

	for i := range actualCopy {
		if actualCopy[i] != expectedCopy[i] {
			return false
		}
	}
	return true
}

func (m stringSliceEqualIgnoreOrder) String() string {
	return "matches " + gomock.Eq(m.expected).String() + " (ignoring order)"
}

func newBBCInstance() *ccev1.Instance {
	return &ccev1.Instance{
		Spec: ccetypes.InstanceSpec{
			MachineType: ccetypes.MachineTypeBBC,
			AccountID:   "account-id",
		},
		Status: ccetypes.InstanceStatus{
			Machine: ccetypes.Machine{InstanceID: "i-xxxxx"},
		},
	}
}

func TestBindSecurityGroups_BBC_EnterprisePending_CallsEnterpriseBind(t *testing.T) {
	ctx := context.TODO()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	bbcClient := bbcmock.NewMockInterface(ctl)
	stsClient := stsmock.NewMockInterface(ctl)

	// Expect sign option
	stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(&bce.SignOption{})

	// Expect enterprise bind with only enterprise pending ids
	bbcClient.EXPECT().BindEnterpriseSecurityGroup(
		ctx,
		[]string{"i-xxxxx"},
		[]string{"esg-a"},
		gomock.Any(),
	).Return(nil)

	provider := &SecurityGroupProvider{stsclient: stsClient, bbcclient: bbcClient}

	instance := newBBCInstance()
	// to be bound has one enterprise and one normal; enterprise path should only submit enterprise ids
	idsToBeBound := []string{"esg-a", "g-1"}
	idsBound := []string{}

	if err := provider.BindSecurityGroups(ctx, instance, idsToBeBound, idsBound); err != nil {
		t.Fatalf("BindSecurityGroups returned error: %v", err)
	}
}

func TestBindSecurityGroups_BBC_NormalPending_CallsNormalBind(t *testing.T) {
	ctx := context.TODO()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	bbcClient := bbcmock.NewMockInterface(ctl)
	stsClient := stsmock.NewMockInterface(ctl)

	stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(&bce.SignOption{})
	// Expect normal bind with pending normal + already bound normal (merged unique)
	bbcClient.EXPECT().BindSecurityGroup(
		ctx,
		[]string{"i-xxxxx"},
		stringSliceEqualIgnoreOrder{[]string{"g-1", "g-2"}},
		gomock.Any(),
	).Return(nil)

	provider := &SecurityGroupProvider{stsclient: stsClient, bbcclient: bbcClient}

	instance := newBBCInstance()
	idsToBeBound := []string{"g-1"}
	idsBound := []string{"g-2"}

	if err := provider.BindSecurityGroups(ctx, instance, idsToBeBound, idsBound); err != nil {
		t.Fatalf("BindSecurityGroups returned error: %v", err)
	}
}

func TestBindSecurityGroups_BBC_Enterprise_MergeAndDedup(t *testing.T) {
	ctx := context.TODO()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	bbcClient := bbcmock.NewMockInterface(ctl)
	stsClient := stsmock.NewMockInterface(ctl)

	stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(&bce.SignOption{})
	// pending enterprise after filtering (remove already bound) should be [esg-a], then merge bound [esg-b] => [esg-a, esg-b]
	bbcClient.EXPECT().BindEnterpriseSecurityGroup(
		ctx,
		[]string{"i-xxxxx"},
		stringSliceEqualIgnoreOrder{[]string{"esg-a", "esg-b"}},
		gomock.Any(),
	).Return(nil)

	provider := &SecurityGroupProvider{stsclient: stsClient, bbcclient: bbcClient}

	instance := newBBCInstance()
	idsToBeBound := []string{"esg-a", "esg-b", "esg-a"}
	idsBound := []string{"esg-b"}

	if err := provider.BindSecurityGroups(ctx, instance, idsToBeBound, idsBound); err != nil {
		t.Fatalf("BindSecurityGroups returned error: %v", err)
	}
}

func TestBindSecurityGroups_BBC_PendingAllBound_ReturnsNil(t *testing.T) {
	ctx := context.TODO()
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	// No calls expected on clients
	bbcClient := bbcmock.NewMockInterface(ctl)
	stsClient := stsmock.NewMockInterface(ctl)

	provider := &SecurityGroupProvider{stsclient: stsClient, bbcclient: bbcClient}

	instance := newBBCInstance()
	idsToBeBound := []string{"esg-a", "g-1"}
	idsBound := []string{"esg-a", "g-1"}

	if err := provider.BindSecurityGroups(ctx, instance, idsToBeBound, idsBound); err != nil {
		t.Fatalf("BindSecurityGroups returned error: %v", err)
	}
}

func TestGetNormalSecurityGroups(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		mockSTSClient  *stsmock.MockInterface
		mockBCCClient  *bccmock.MockInterface
		mockBBCClient  *mock.MockInterface
		mockHPASClient *hpasmock.MockInterface
		provider       *SecurityGroupProvider
	}

	type args struct {
		ctx        context.Context
		vpcID      string
		instanceID string
		accountID  string
	}

	signOption := &bce.SignOption{}

	tests := []struct {
		name        string
		fields      func() fields
		args        args
		want        *bccapi.ListSecurityGroupResult
		expectedErr bool
	}{
		{
			name: "成功-第一次调用",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockSTSClient := stsmock.NewMockInterface(ctrl)
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockBBCClient := mock.NewMockInterface(ctrl)
				mockHPASClient := hpasmock.NewMockInterface(ctrl)
				provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

				// 清理缓存
				sgCache.Flush()

				request := &bccapi.ListSecurityGroupArgs{
					InstanceId: "i-test123",
					VpcId:      "vpc-test123",
				}

				expectedResult := &bccapi.ListSecurityGroupResult{
					SecurityGroups: []bccapi.SecurityGroupModel{
						{
							Id:    "sg-test123",
							Name:  "test-sg",
							Desc:  "test security group",
							VpcId: "vpc-test123",
						},
					},
					Marker:      "",
					IsTruncated: false,
					NextMarker:  "",
					MaxKeys:     1000,
				}

				mockSTSClient.EXPECT().
					NewSignOption(gomock.Any(), "account-test123").
					Return(signOption).
					Times(1)

				mockBCCClient.EXPECT().
					ListSecurityGroups(gomock.Any(), request, signOption).
					Return(expectedResult, nil).
					Times(1)

				return fields{
					ctrl:           ctrl,
					mockSTSClient:  mockSTSClient,
					mockBCCClient:  mockBCCClient,
					mockBBCClient:  mockBBCClient,
					mockHPASClient: mockHPASClient,
					provider:       provider,
				}
			},
			args: args{
				ctx:        context.Background(),
				vpcID:      "vpc-test123",
				instanceID: "i-test123",
				accountID:  "account-test123",
			},
			want: &bccapi.ListSecurityGroupResult{
				SecurityGroups: []bccapi.SecurityGroupModel{
					{
						Id:    "sg-test123",
						Name:  "test-sg",
						Desc:  "test security group",
						VpcId: "vpc-test123",
					},
				},
				Marker:      "",
				IsTruncated: false,
				NextMarker:  "",
				MaxKeys:     1000,
			},
			expectedErr: false,
		},
		{
			name: "成功-重试后成功",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockSTSClient := stsmock.NewMockInterface(ctrl)
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockBBCClient := mock.NewMockInterface(ctrl)
				mockHPASClient := hpasmock.NewMockInterface(ctrl)
				provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

				// 清理缓存
				sgCache.Flush()

				request := &bccapi.ListSecurityGroupArgs{
					InstanceId: "i-test123",
					VpcId:      "vpc-test123",
				}

				expectedResult := &bccapi.ListSecurityGroupResult{
					SecurityGroups: []bccapi.SecurityGroupModel{
						{
							Id:    "sg-test456",
							Name:  "test-sg-2",
							Desc:  "test security group 2",
							VpcId: "vpc-test123",
						},
					},
					Marker:      "",
					IsTruncated: false,
					NextMarker:  "",
					MaxKeys:     1000,
				}

				// 第一次调用失败
				mockSTSClient.EXPECT().
					NewSignOption(gomock.Any(), "account-test123").
					Return(signOption).
					Times(2)

				mockBCCClient.EXPECT().
					ListSecurityGroups(gomock.Any(), request, signOption).
					Return(nil, errors.New("temporary error")).
					Times(1)

				// 第二次调用成功
				mockBCCClient.EXPECT().
					ListSecurityGroups(gomock.Any(), request, signOption).
					Return(expectedResult, nil).
					Times(1)

				return fields{
					ctrl:           ctrl,
					mockSTSClient:  mockSTSClient,
					mockBCCClient:  mockBCCClient,
					mockBBCClient:  mockBBCClient,
					mockHPASClient: mockHPASClient,
					provider:       provider,
				}
			},
			args: args{
				ctx:        context.Background(),
				vpcID:      "vpc-test123",
				instanceID: "i-test123",
				accountID:  "account-test123",
			},
			want: &bccapi.ListSecurityGroupResult{
				SecurityGroups: []bccapi.SecurityGroupModel{
					{
						Id:    "sg-test456",
						Name:  "test-sg-2",
						Desc:  "test security group 2",
						VpcId: "vpc-test123",
					},
				},
				Marker:      "",
				IsTruncated: false,
				NextMarker:  "",
				MaxKeys:     1000,
			},
			expectedErr: false,
		},
		{
			name: "失败-所有重试都失败",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockSTSClient := stsmock.NewMockInterface(ctrl)
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockBBCClient := mock.NewMockInterface(ctrl)
				mockHPASClient := hpasmock.NewMockInterface(ctrl)
				provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

				// 清理缓存
				sgCache.Flush()

				request := &bccapi.ListSecurityGroupArgs{
					InstanceId: "i-test123",
					VpcId:      "vpc-test123",
				}

				expectedError := errors.New("persistent error")

				mockSTSClient.EXPECT().
					NewSignOption(gomock.Any(), "account-test123").
					Return(signOption).
					Times(maxRetries)

				mockBCCClient.EXPECT().
					ListSecurityGroups(gomock.Any(), request, signOption).
					Return(nil, expectedError).
					Times(maxRetries)

				return fields{
					ctrl:           ctrl,
					mockSTSClient:  mockSTSClient,
					mockBCCClient:  mockBCCClient,
					mockBBCClient:  mockBBCClient,
					mockHPASClient: mockHPASClient,
					provider:       provider,
				}
			},
			args: args{
				ctx:        context.Background(),
				vpcID:      "vpc-test123",
				instanceID: "i-test123",
				accountID:  "account-test123",
			},
			want:        nil,
			expectedErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			f := tc.fields()
			defer f.ctrl.Finish()

			result, err := f.provider.GetNormalSecurityGroups(tc.args.ctx, tc.args.vpcID, tc.args.instanceID, tc.args.accountID)

			if tc.expectedErr {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				if result != nil {
					t.Errorf("expected nil result but got %v", result)
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got %v", err)
				}
				if result == nil {
					t.Errorf("expected result but got nil")
				}
				if tc.want != nil && len(result.SecurityGroups) > 0 && len(tc.want.SecurityGroups) > 0 {
					if result.SecurityGroups[0].Id != tc.want.SecurityGroups[0].Id {
						t.Errorf("expected SecurityGroup ID %s but got %s", tc.want.SecurityGroups[0].Id, result.SecurityGroups[0].Id)
					}
				}
			}
		})
	}
}

func TestGetEnterpriseSecurityGroups(t *testing.T) {
	type fields struct {
		ctrl           *gomock.Controller
		mockSTSClient  *stsmock.MockInterface
		mockBCCClient  *bccmock.MockInterface
		mockBBCClient  *mock.MockInterface
		mockHPASClient *hpasmock.MockInterface
		provider       *SecurityGroupProvider
	}

	type args struct {
		ctx        context.Context
		vpcID      string
		instanceID string
		accountID  string
	}

	signOption := &bce.SignOption{}

	tests := []struct {
		name        string
		fields      func() fields
		args        args
		want        *esg.ListEsgResult
		expectedErr bool
	}{
		{
			name: "成功-第一次调用",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockSTSClient := stsmock.NewMockInterface(ctrl)
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockBBCClient := mock.NewMockInterface(ctrl)
				mockHPASClient := hpasmock.NewMockInterface(ctrl)
				provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

				// 清理缓存
				sgCache.Flush()

				request := &esg.ListEsgArgs{
					InstanceId: "i-test123",
				}

				expectedResult := &esg.ListEsgResult{
					EnterpriseSecurityGroups: []esg.EnterpriseSecurityGroup{
						{
							Id:   "esg-test123",
							Name: "test-esg",
							Desc: "test enterprise security group",
						},
					},
					Marker:      "",
					IsTruncated: false,
					NextMarker:  "",
					MaxKeys:     1000,
				}

				mockSTSClient.EXPECT().
					NewSignOption(gomock.Any(), "account-test123").
					Return(signOption).
					Times(1)

				mockBCCClient.EXPECT().
					ListEnterpriseSecurityGroup(gomock.Any(), request, signOption).
					Return(expectedResult, nil).
					Times(1)

				return fields{
					ctrl:           ctrl,
					mockSTSClient:  mockSTSClient,
					mockBCCClient:  mockBCCClient,
					mockBBCClient:  mockBBCClient,
					mockHPASClient: mockHPASClient,
					provider:       provider,
				}
			},
			args: args{
				ctx:        context.Background(),
				vpcID:      "vpc-test123",
				instanceID: "i-test123",
				accountID:  "account-test123",
			},
			want: &esg.ListEsgResult{
				EnterpriseSecurityGroups: []esg.EnterpriseSecurityGroup{
					{
						Id:   "esg-test123",
						Name: "test-esg",
						Desc: "test enterprise security group",
					},
				},
				Marker:      "",
				IsTruncated: false,
				NextMarker:  "",
				MaxKeys:     1000,
			},
			expectedErr: false,
		},
		{
			name: "失败-所有重试都失败",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mockSTSClient := stsmock.NewMockInterface(ctrl)
				mockBCCClient := bccmock.NewMockInterface(ctrl)
				mockBBCClient := mock.NewMockInterface(ctrl)
				mockHPASClient := hpasmock.NewMockInterface(ctrl)
				provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

				// 清理缓存
				sgCache.Flush()

				request := &esg.ListEsgArgs{
					InstanceId: "i-test123",
				}

				expectedError := errors.New("persistent error")

				mockSTSClient.EXPECT().
					NewSignOption(gomock.Any(), "account-test123").
					Return(signOption).
					Times(maxRetries)

				mockBCCClient.EXPECT().
					ListEnterpriseSecurityGroup(gomock.Any(), request, signOption).
					Return(nil, expectedError).
					Times(maxRetries)

				return fields{
					ctrl:           ctrl,
					mockSTSClient:  mockSTSClient,
					mockBCCClient:  mockBCCClient,
					mockBBCClient:  mockBBCClient,
					mockHPASClient: mockHPASClient,
					provider:       provider,
				}
			},
			args: args{
				ctx:        context.Background(),
				vpcID:      "vpc-test123",
				instanceID: "i-test123",
				accountID:  "account-test123",
			},
			want:        nil,
			expectedErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			f := tc.fields()
			defer f.ctrl.Finish()

			result, err := f.provider.GetEnterpriseSecurityGroups(tc.args.ctx, tc.args.vpcID, tc.args.instanceID, tc.args.accountID)

			if tc.expectedErr {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				if result != nil {
					t.Errorf("expected nil result but got %v", result)
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got %v", err)
				}
				if result == nil {
					t.Errorf("expected result but got nil")
				}
				if tc.want != nil && len(result.EnterpriseSecurityGroups) > 0 && len(tc.want.EnterpriseSecurityGroups) > 0 {
					if result.EnterpriseSecurityGroups[0].Id != tc.want.EnterpriseSecurityGroups[0].Id {
						t.Errorf("expected EnterpriseSecurityGroup ID %s but got %s", tc.want.EnterpriseSecurityGroups[0].Id, result.EnterpriseSecurityGroups[0].Id)
					}
				}
			}
		})
	}
}

func TestRetryBehavior(t *testing.T) {
	// 测试重试间隔时间
	start := time.Now()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSTSClient := stsmock.NewMockInterface(ctrl)
	mockBCCClient := bccmock.NewMockInterface(ctrl)
	mockBBCClient := mock.NewMockInterface(ctrl)
	mockHPASClient := hpasmock.NewMockInterface(ctrl)

	provider := NewProvider(context.Background(), mockSTSClient, mockBCCClient, mockBBCClient, mockHPASClient)

	ctx := context.Background()
	vpcID := "vpc-test123"
	instanceID := "i-test123"
	accountID := "account-test123"
	signOption := &bce.SignOption{}

	// 清理缓存
	sgCache.Flush()

	request := &bccapi.ListSecurityGroupArgs{
		InstanceId: instanceID,
		VpcId:      vpcID,
	}

	expectedError := errors.New("persistent error")

	mockSTSClient.EXPECT().
		NewSignOption(gomock.Any(), accountID).
		Return(signOption).
		Times(maxRetries)

	mockBCCClient.EXPECT().
		ListSecurityGroups(gomock.Any(), request, signOption).
		Return(nil, expectedError).
		Times(maxRetries)

	_, err := provider.GetNormalSecurityGroups(ctx, vpcID, instanceID, accountID)

	elapsed := time.Since(start)

	if err == nil {
		t.Errorf("expected error but got none")
	}
	// 验证重试间隔：应该至少等待 (maxRetries-1) * retryInterval
	expectedMinDuration := time.Duration(maxRetries-1) * retryInterval
	if elapsed < expectedMinDuration {
		t.Errorf("Expected at least %v, but got %v", expectedMinDuration, elapsed)
	}
}
