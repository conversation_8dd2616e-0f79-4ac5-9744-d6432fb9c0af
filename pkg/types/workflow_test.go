package ccetypes

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestComponentName_XPUContainerToolkit 测试XPU Container Toolkit组件名称常量
func TestComponentName_XPUContainerToolkit(t *testing.T) {
	// 验证XPU组件名称常量值正确
	assert.Equal(t, ComponentName("xpuContainerToolkit"), ComponentXPUContainerToolkit)

	// 验证组件名称不为空
	assert.NotEmpty(t, ComponentXPUContainerToolkit)

	// 验证组件名称符合预期格式
	assert.Equal(t, "xpuContainerToolkit", string(ComponentXPUContainerToolkit))
}

// TestUpgradeNodesWorkflowConfig_SetInstanceNeedDrain 测试设置实例需要排水
func TestUpgradeNodesWorkflowConfig_SetInstanceNeedDrain(t *testing.T) {
	tests := []struct {
		name       string
		config     *UpgradeNodesWorkflowConfig
		instanceId string
		expected   bool
	}{
		{
			name: "设置实例需要排水-初始化空map",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: nil,
			},
			instanceId: "instance-1",
			expected:   true,
		},
		{
			name: "设置实例需要排水-已有map",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: make(map[string]bool),
			},
			instanceId: "instance-2",
			expected:   true,
		},
		{
			name: "覆盖已存在的实例设置",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: map[string]bool{
					"instance-3": false,
				},
			},
			instanceId: "instance-3",
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.config.SetInstanceNeedDrain(tt.instanceId)

			// 验证map已初始化
			assert.NotNil(t, tt.config.InstanceIdToNeedDrain)

			// 验证实例被设置为需要排水
			assert.Equal(t, tt.expected, tt.config.InstanceIdToNeedDrain[tt.instanceId])
		})
	}
}

// TestUpgradeNodesWorkflowConfig_IsInstanceDrain 测试判断实例是否需要排水
func TestUpgradeNodesWorkflowConfig_IsInstanceDrain(t *testing.T) {
	tests := []struct {
		name       string
		config     *UpgradeNodesWorkflowConfig
		instanceId string
		expected   bool
	}{
		{
			name: "map为nil-返回false",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: nil,
			},
			instanceId: "instance-1",
			expected:   false,
		},
		{
			name: "实例不存在-返回false",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: make(map[string]bool),
			},
			instanceId: "instance-2",
			expected:   false,
		},
		{
			name: "实例存在且为true",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: map[string]bool{
					"instance-3": true,
				},
			},
			instanceId: "instance-3",
			expected:   true,
		},
		{
			name: "实例存在且为false",
			config: &UpgradeNodesWorkflowConfig{
				InstanceIdToNeedDrain: map[string]bool{
					"instance-4": false,
				},
			},
			instanceId: "instance-4",
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.IsInstanceDrain(tt.instanceId)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestComponent_XPUContainerToolkit 测试XPU组件结构体
func TestComponent_XPUContainerToolkit(t *testing.T) {
	tests := []struct {
		name      string
		component Component
	}{
		{
			name: "XPU组件-基本字段",
			component: Component{
				Name:           ComponentXPUContainerToolkit,
				CurrentVersion: "1.0.2",
				TargetVersion:  "1.0.5",
			},
		},
		{
			name: "XPU组件-空版本",
			component: Component{
				Name:           ComponentXPUContainerToolkit,
				CurrentVersion: "",
				TargetVersion:  "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 验证组件名称正确
			assert.Equal(t, ComponentXPUContainerToolkit, tt.component.Name)

			// 验证结构体字段可以正常赋值和读取
			assert.NotNil(t, tt.component.Name)
			assert.IsType(t, "", tt.component.CurrentVersion)
			assert.IsType(t, "", tt.component.TargetVersion)
		})
	}
}

// TestWorkflowType_Constants 测试工作流类型常量
func TestWorkflowType_Constants(t *testing.T) {
	// 验证现有的工作流类型常量不为空
	assert.NotEmpty(t, WorkflowTypeMasterContainerized)
	assert.NotEmpty(t, WorkflowTypeUpgradeClusterK8SVersion)
	assert.NotEmpty(t, WorkflowTypeUpgradeMasterK8SVersion)
	assert.NotEmpty(t, WorkflowTypeUpgradeNodesK8SVersion)
	assert.NotEmpty(t, WorkflowTypeUpgradeNodes)
}

// TestWorkflowPhase_Constants 测试工作流阶段常量
func TestWorkflowPhase_Constants(t *testing.T) {
	// 验证工作流阶段常量
	phases := []WorkflowPhase{
		WorkflowPhasePending,
		WorkflowPhaseUpgrading,
		WorkflowPhaseSucceeded,
		WorkflowPhaseFailed,
		WorkflowPhaseVerifying,
		WorkflowPhasePaused,
		WorkflowPhaseDeleting,
		WorkflowPhaseDeleted,
		WorkflowPhaseUnknown,
	}

	for _, phase := range phases {
		assert.NotEmpty(t, phase)
		assert.IsType(t, WorkflowPhase(""), phase)
	}
}

// TestPausePolicy_Constants 测试暂停策略常量
func TestPausePolicy_Constants(t *testing.T) {
	// 验证暂停策略常量
	policies := []PausePolicy{
		NotPause,
		FirstBatch,
		EveryBatch,
	}

	for _, policy := range policies {
		assert.NotEmpty(t, policy)
		assert.IsType(t, PausePolicy(""), policy)
	}
}

// TestTaskGroupName_Constants 测试任务组名称常量
func TestTaskGroupName_Constants(t *testing.T) {
	// 验证任务组名称常量
	taskGroups := []TaskGroupName{
		TaskGroupNamePreCheck,
		TaskGroupNameBackup,
		TaskGroupNameOperate,
		TaskGroupNamePostCheck,
	}

	for _, taskGroup := range taskGroups {
		assert.NotEmpty(t, taskGroup)
		assert.IsType(t, TaskGroupName(""), taskGroup)
	}
}

// TestUpgradeNodesWorkflowConfig_WithXPUComponent 测试包含XPU组件的节点升级配置
func TestUpgradeNodesWorkflowConfig_WithXPUComponent(t *testing.T) {
	config := &UpgradeNodesWorkflowConfig{
		CCEInstanceIDList:    []string{"instance-1", "instance-2"},
		InstanceGroupID:      "ig-test",
		NodeUpgradeBatchSize: 1,
		Components: []Component{
			{
				Name:           ComponentKubelet,
				CurrentVersion: "1.28.8",
				TargetVersion:  "1.30.1",
			},
			{
				Name:           ComponentXPUContainerToolkit,
				CurrentVersion: "1.0.2",
				TargetVersion:  "1.0.5",
			},
		},
	}

	// 验证配置包含XPU组件
	hasXPUComponent := false
	for _, component := range config.Components {
		if component.Name == ComponentXPUContainerToolkit {
			hasXPUComponent = true
			assert.Equal(t, "1.0.2", component.CurrentVersion)
			assert.Equal(t, "1.0.5", component.TargetVersion)
			break
		}
	}
	assert.True(t, hasXPUComponent, "配置应该包含XPU组件")

	// 验证基本字段
	assert.Equal(t, 2, len(config.CCEInstanceIDList))
	assert.Equal(t, "ig-test", config.InstanceGroupID)
	assert.Equal(t, 1, config.NodeUpgradeBatchSize)
	assert.Equal(t, 2, len(config.Components))
}
