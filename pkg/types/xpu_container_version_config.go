package ccetypes

import (
	"fmt"
	"strings"
)

// xpu-container-toolkit版本映射表

type XPUContainerVersionConfig interface {
	IsSupported() bool // 是否支持版本
}

const (
	// 目前支持的XPU版本
	XPUToolkit_1_0_5 = "1.0.5" // 兼容所有类型昆仑芯
	XPUToolkit_1_0_4 = "1.0.4" // 2025-04-24~2025-06-14cce安装版本
	XPUToolkit_1_0_2 = "1.0.2" // 2025-04-24之前创建的昆仑芯安装版本

	// 存量版本
	XPUToolkit_Unknown = ""

	XPUToolkitVersionKeyPrefix = "install"
)

// xpu-container-toolkit组件版本只与操作系统有关，与k8s版本无关
// xpu-container-toolkit版本与操作系统对应关系表
// 结构为 map['{os}'][]XPUContainerToolkitVersion
// os字段为 centos7、centos8、baidulinux3、rocky linux、ubuntu16、ubuntu18类似结构。
// 确保返回[]XPUContainerToolkitVersion的版本是从小到大
var (
	supportXPUContainerVersion = map[string][]string{
		// 根据操作系统选择支持的版本，所有操作系统都支持安装1.0.5
		"ubuntu16": {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		"ubuntu18": {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		"ubuntu20": {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		"ubuntu22": {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		"centos7":  {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		"centos8":  {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		//"baidulinux3":  {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		//"rocky linux":  {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
		//"rocky linux8": {XPUToolkit_1_0_2, XPUToolkit_1_0_4, XPUToolkit_1_0_5},
	}

	installXPUContainerVersion = map[string]string{
		// 安装阶段使用map - 默认使用最新版本1.0.5
		"install-ubuntu16": XPUToolkit_1_0_5,
		"install-ubuntu18": XPUToolkit_1_0_5,
		"install-ubuntu20": XPUToolkit_1_0_5,
		"install-ubuntu22": XPUToolkit_1_0_5,
		"install-centos7":  XPUToolkit_1_0_5,
		"install-centos8":  XPUToolkit_1_0_5,
		//"install-baidulinux3":  XPUToolkit_1_0_5,
		//"install-rocky linux":  XPUToolkit_1_0_5,
		//"install-rocky linux8": XPUToolkit_1_0_5,
	}
)

// supportXPUContainerVersionSet 用于判断是否是支持升级的xpu toolkit版本
var supportXPUContainerVersionSet = map[string]bool{
	XPUToolkit_1_0_5: true,
	XPUToolkit_1_0_4: true,
	XPUToolkit_1_0_2: true,
}

// GetXPUToolkitVersionMapKeyOS 根据传入osName和osVersion两个字段，生成查询supportXPUContainerVersion map需要的os参数
// 复用nvidia的逻辑
func GetXPUToolkitVersionMapKeyOS(osName, osVersion string) string {
	return GetToolkitVersionMapKeyOS(osName, osVersion)
}

// IsSupportXPUContainerToolkitVersion 根据操作系统判断是否支持指定版本的xpu-container-toolkit
// 参数key为操作系统名称，如 "centos7", "ubuntu20" 等
// 查找逻辑：直接根据操作系统选择支持的版本列表
// 单测要点：
// 1. 正常场景：支持的操作系统和版本组合
//   - 输入：("ubuntu20", "1.0.5") -> 输出：true
//   - 输入：("centos7", "1.0.3") -> 输出：true
//
// 2. 不支持场景：
//   - 不支持的操作系统：("windows", "1.0.5") -> 输出：false
//   - 不支持的版本：("ubuntu20", "2.0.0") -> 输出：false
//
// 3. 边界条件：
//   - 空字符串输入：("", "1.0.5") -> 输出：false
//   - 大小写敏感性：("UBUNTU20", "1.0.5") -> 输出：true（转小写处理）
//
// 4. 异常场景：
//   - nil或无效版本号的处理
//
// 5. 并发安全：多个goroutine同时调用的线程安全性
func IsSupportXPUContainerToolkitVersion(key, targetVersion string) bool {
	// 将key全部转换为小写字母
	lowerKey := strings.ToLower(key)

	// 直接根据操作系统查找支持的版本列表
	listXPUContainerToolkitVersion, ok := supportXPUContainerVersion[lowerKey]
	if ok {
		// 操作系统匹配成功，检查版本是否支持
		for _, version := range listXPUContainerToolkitVersion {
			if version == targetVersion {
				return true
			}
		}
	}
	return false
}

// IsSupportedXPU 判断版本是不是cce已经适配过的XPU版本
func IsSupportedXPU(upgradeVersion string) bool {
	_, ok := supportXPUContainerVersionSet[upgradeVersion]
	if ok {
		return true
	}
	return false
}

// GetSupportUpgradeXPUToolkitVersion 根据操作系统获取支持升级的xpu-container-toolkit版本列表
// 参数key为操作系统名称，如 "centos7", "ubuntu20" 等
// 返回升级版本列表，版本列表严格保持递增
// 单测要点：
// 1. 正常场景：支持的操作系统返回版本列表
//   - 输入："ubuntu20" -> 输出：["1.0.0", "1.0.3", "1.0.5"], nil
//   - 输入："centos7" -> 输出：["1.0.0", "1.0.3", "1.0.5"], nil
//
// 2. 异常场景：
//   - 不支持的操作系统：输入："windows" -> 输出：nil, "unsupported os: windows"
//
// 3. 边界条件：
//   - 空字符串输入："" -> 输出：nil, "unsupported os: "
//   - 大小写处理："UBUNTU20" -> 输出：["1.0.0", "1.0.3", "1.0.5"], nil
//
// 4. 版本顺序验证：
//   - 返回的版本列表必须按升序排列
//   - 版本格式必须符合语义化版本规范
//
// 5. 并发安全：多个goroutine同时调用的线程安全性
func GetSupportUpgradeXPUToolkitVersion(key string) ([]string, error) {
	// 将key全部转换为小写字母
	lowerKey := strings.ToLower(key)

	// 直接根据操作系统查找支持的版本列表
	listXPUContainerToolkitVersion, ok := supportXPUContainerVersion[lowerKey]
	if ok {
		// 操作系统匹配成功
		return listXPUContainerToolkitVersion, nil
	}
	return nil, fmt.Errorf("unsupported os: %s", key)
}

// GetDefaultXPUContainerToolkitVersion 根据操作系统获取默认的XPU toolkit版本
// 单测要点：
// 1. 正常场景：支持的操作系统返回最新版本
//   - 输入：任意k8s版本，任意runtime，"ubuntu", "20.04" -> 输出："1.0.5"
//   - 输入：任意k8s版本，任意runtime，"centos", "7" -> 输出："1.0.5"
//
// 2. 不支持的操作系统：
//   - 输入：任意k8s版本，任意runtime，"windows", "10" -> 输出：""
//
// 3. 边界条件：
//   - 空字符串操作系统：输入："", "" -> 输出：""
//   - 大小写处理：输入："UBUNTU", "20.04" -> 输出："1.0.5"
//
// 4. 参数依赖性验证：
//   - k8SVersion参数不影响结果（XPU版本只与OS相关）
//   - runtimeType和runtimeVersion参数不影响结果
//
// 5. 版本列表为空时的处理：
//   - 如果supportXPUContainerVersion中某OS对应空列表，返回XPUToolkit_Unknown
//
// 6. 并发安全：多个goroutine同时调用的线程安全性
func GetDefaultXPUContainerToolkitVersion(k8SVersion K8SVersion, runtimeType RuntimeType, runtimeVersion,
	oSName, oSVersion string) string {
	os := GetXPUToolkitVersionMapKeyOS(oSName, oSVersion)

	// 直接根据操作系统获取支持的版本列表，返回最新版本
	versions, ok := supportXPUContainerVersion[strings.ToLower(os)]
	if ok && len(versions) > 0 {
		return versions[len(versions)-1]
	}
	return XPUToolkit_Unknown
}

// GetInstanceDefaultInstallXPUContainerToolkitVersion 获取实例安装时的默认XPU toolkit版本
// 只依赖操作系统，返回最新版本1.0.5
func GetInstanceDefaultInstallXPUContainerToolkitVersion(k8SVersion K8SVersion, runtimeType RuntimeType, runtimeVersion,
	oSName, oSVersion string) string {
	os := GetXPUToolkitVersionMapKeyOS(oSName, oSVersion)

	// 直接根据操作系统匹配安装版本
	key := strings.ToLower(fmt.Sprintf("%s-%s", XPUToolkitVersionKeyPrefix, os))
	xpuContainerToolkitVersion, ok := installXPUContainerVersion[key]
	if ok {
		return xpuContainerToolkitVersion
	}

	// 如果没有匹配到，返回默认最新版本
	return XPUToolkit_1_0_5
}

// GetClusterDefaultInstallXPUContainerToolkitVersion 创建集群时默认生成最新逻辑
// xpu-container-toolkit与k8s版本无关，直接返回最新版本
func GetClusterDefaultInstallXPUContainerToolkitVersion(k8SVersion K8SVersion) string {
	// 所有k8s版本都支持最新的xpu-container-toolkit版本
	return XPUToolkit_1_0_5
}
