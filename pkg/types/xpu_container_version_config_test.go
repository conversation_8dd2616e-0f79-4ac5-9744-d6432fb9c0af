package ccetypes

import (
	"testing"
)

func TestIsSupportXPUContainerToolkitVersion(t *testing.T) {
	tests := []struct {
		name          string
		osKey         string
		targetVersion string
		expected      bool
	}{
		{
			name:          "支持的Ubuntu20版本1.0.5",
			osKey:         "ubuntu20",
			targetVersion: "1.0.5",
			expected:      true,
		},
		{
			name:          "支持的CentOS7版本1.0.4",
			osKey:         "centos7",
			targetVersion: "1.0.4",
			expected:      true,
		},
		{
			name:          "不支持的版本",
			osKey:         "ubuntu20",
			targetVersion: "2.0.0",
			expected:      false,
		},
		{
			name:          "不支持的操作系统",
			osKey:         "windows",
			targetVersion: "1.0.5",
			expected:      false,
		},
		{
			name:          "大小写不敏感",
			osKey:         "UBUNTU20",
			targetVersion: "1.0.5",
			expected:      true,
		},
		{
			name:          "BaiduLinux支持",
			osKey:         "baidulinux3",
			targetVersion: "1.0.5",
			expected:      false,
		},
		{
			name:          "Rocky Linux支持",
			osKey:         "rocky linux",
			targetVersion: "1.0.5",
			expected:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsSupportXPUContainerToolkitVersion(tt.osKey, tt.targetVersion)
			if result != tt.expected {
				t.Errorf("IsSupportXPUContainerToolkitVersion(%s, %s) = %v, expected %v",
					tt.osKey, tt.targetVersion, result, tt.expected)
			}
		})
	}
}

func TestGetSupportUpgradeXPUToolkitVersion(t *testing.T) {
	tests := []struct {
		name           string
		osKey          string
		expectedLength int
		expectedError  bool
		expectedFirst  string
		expectedLast   string
	}{
		{
			name:           "Ubuntu20支持的版本列表",
			osKey:          "ubuntu20",
			expectedLength: 3,
			expectedError:  false,
			expectedFirst:  "1.0.2",
			expectedLast:   "1.0.5",
		},
		{
			name:           "CentOS7支持的版本列表",
			osKey:          "centos7",
			expectedLength: 3,
			expectedError:  false,
			expectedFirst:  "1.0.2",
			expectedLast:   "1.0.5",
		},
		{
			name:          "不支持的操作系统",
			osKey:         "windows",
			expectedError: true,
		},
		{
			name:           "大小写不敏感",
			osKey:          "CENTOS8",
			expectedLength: 3,
			expectedError:  false,
			expectedFirst:  "1.0.2",
			expectedLast:   "1.0.5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			versions, err := GetSupportUpgradeXPUToolkitVersion(tt.osKey)

			if tt.expectedError {
				if err == nil {
					t.Errorf("GetSupportUpgradeXPUToolkitVersion(%s) expected error, got nil", tt.osKey)
				}
				return
			}

			if err != nil {
				t.Errorf("GetSupportUpgradeXPUToolkitVersion(%s) unexpected error: %v", tt.osKey, err)
				return
			}

			if len(versions) != tt.expectedLength {
				t.Errorf("GetSupportUpgradeXPUToolkitVersion(%s) returned %d versions, expected %d",
					tt.osKey, len(versions), tt.expectedLength)
				return
			}

			if len(versions) > 0 {
				if versions[0] != tt.expectedFirst {
					t.Errorf("GetSupportUpgradeXPUToolkitVersion(%s) first version = %s, expected %s",
						tt.osKey, versions[0], tt.expectedFirst)
				}
				if versions[len(versions)-1] != tt.expectedLast {
					t.Errorf("GetSupportUpgradeXPUToolkitVersion(%s) last version = %s, expected %s",
						tt.osKey, versions[len(versions)-1], tt.expectedLast)
				}
			}
		})
	}
}

func TestIsSupportedXPU(t *testing.T) {
	tests := []struct {
		name           string
		upgradeVersion string
		expected       bool
	}{
		{
			name:           "支持的版本1.0.5",
			upgradeVersion: "1.0.5",
			expected:       true,
		},
		{
			name:           "支持的版本1.0.4",
			upgradeVersion: "1.0.4",
			expected:       true,
		},
		{
			name:           "支持的版本1.0.2",
			upgradeVersion: "1.0.2",
			expected:       true,
		},
		{
			name:           "不支持的版本",
			upgradeVersion: "2.0.0",
			expected:       false,
		},
		{
			name:           "空版本",
			upgradeVersion: "",
			expected:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsSupportedXPU(tt.upgradeVersion)
			if result != tt.expected {
				t.Errorf("IsSupportedXPU(%s) = %v, expected %v",
					tt.upgradeVersion, result, tt.expected)
			}
		})
	}
}

func TestGetDefaultXPUContainerToolkitVersion(t *testing.T) {
	tests := []struct {
		name            string
		k8sVersion      K8SVersion
		runtimeType     RuntimeType
		runtimeVersion  string
		osName          string
		osVersion       string
		expectedVersion string
	}{
		{
			name:            "Ubuntu20默认版本",
			k8sVersion:      "1.28.8",
			runtimeType:     "containerd",
			runtimeVersion:  "1.6.28",
			osName:          "ubuntu",
			osVersion:       "20.04",
			expectedVersion: "1.0.5",
		},
		{
			name:            "CentOS7默认版本",
			k8sVersion:      "1.30.1",
			runtimeType:     "containerd",
			runtimeVersion:  "1.7.13",
			osName:          "centos",
			osVersion:       "7",
			expectedVersion: "1.0.5",
		},
		{
			name:            "不支持的操作系统",
			k8sVersion:      "1.28.8",
			runtimeType:     "containerd",
			runtimeVersion:  "1.6.28",
			osName:          "windows",
			osVersion:       "10",
			expectedVersion: XPUToolkit_Unknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetDefaultXPUContainerToolkitVersion(
				tt.k8sVersion, tt.runtimeType, tt.runtimeVersion, tt.osName, tt.osVersion)
			if result != tt.expectedVersion {
				t.Errorf("GetDefaultXPUContainerToolkitVersion() = %s, expected %s",
					result, tt.expectedVersion)
			}
		})
	}
}

func TestGetInstanceDefaultInstallXPUContainerToolkitVersion(t *testing.T) {
	tests := []struct {
		name            string
		k8sVersion      K8SVersion
		runtimeType     RuntimeType
		runtimeVersion  string
		osName          string
		osVersion       string
		expectedVersion string
	}{
		{
			name:            "Ubuntu20安装版本",
			k8sVersion:      "1.28.8",
			runtimeType:     "containerd",
			runtimeVersion:  "1.6.28",
			osName:          "ubuntu",
			osVersion:       "20.04",
			expectedVersion: "1.0.5",
		},
		{
			name:            "CentOS7安装版本",
			k8sVersion:      "1.30.1",
			runtimeType:     "containerd",
			runtimeVersion:  "1.7.13",
			osName:          "centos",
			osVersion:       "7",
			expectedVersion: "1.0.5",
		},
		{
			name:            "不支持的操作系统返回默认版本",
			k8sVersion:      "1.28.8",
			runtimeType:     "containerd",
			runtimeVersion:  "1.6.28",
			osName:          "windows",
			osVersion:       "10",
			expectedVersion: "1.0.5", // 返回默认最新版本
		},
		{
			name:            "Rocky Linux安装版本",
			k8sVersion:      "1.28.8",
			runtimeType:     "containerd",
			runtimeVersion:  "1.6.28",
			osName:          "rocky",
			osVersion:       "8",
			expectedVersion: "1.0.5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetInstanceDefaultInstallXPUContainerToolkitVersion(
				tt.k8sVersion, tt.runtimeType, tt.runtimeVersion, tt.osName, tt.osVersion)
			if result != tt.expectedVersion {
				t.Errorf("GetInstanceDefaultInstallXPUContainerToolkitVersion() = %s, expected %s",
					result, tt.expectedVersion)
			}
		})
	}
}

func TestGetClusterDefaultInstallXPUContainerToolkitVersion(t *testing.T) {
	tests := []struct {
		name            string
		k8sVersion      K8SVersion
		expectedVersion string
	}{
		{
			name:            "K8s 1.28.8版本",
			k8sVersion:      "1.28.8",
			expectedVersion: "1.0.5",
		},
		{
			name:            "K8s 1.30.1版本",
			k8sVersion:      "1.30.1",
			expectedVersion: "1.0.5",
		},
		{
			name:            "K8s 1.20.0版本",
			k8sVersion:      "1.20.0",
			expectedVersion: "1.0.5", // 所有版本都返回最新版本
		},
		{
			name:            "K8s 1.29.0版本",
			k8sVersion:      "1.29.0",
			expectedVersion: "1.0.5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetClusterDefaultInstallXPUContainerToolkitVersion(tt.k8sVersion)
			if result != tt.expectedVersion {
				t.Errorf("GetClusterDefaultInstallXPUContainerToolkitVersion(%s) = %s, expected %s",
					tt.k8sVersion, result, tt.expectedVersion)
			}
		})
	}
}

func TestGetXPUToolkitVersionMapKeyOS(t *testing.T) {
	tests := []struct {
		name      string
		osName    string
		osVersion string
		expected  string
	}{
		{
			name:      "Ubuntu 20.04",
			osName:    "ubuntu",
			osVersion: "20.04",
			expected:  "ubuntu20", // 假设GetToolkitVersionMapKeyOS的行为
		},
		{
			name:      "CentOS 7",
			osName:    "centos",
			osVersion: "7",
			expected:  "centos7",
		},
		{
			name:      "BaiduLinux 3",
			osName:    "baidulinux",
			osVersion: "3",
			expected:  "baidulinux3",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetXPUToolkitVersionMapKeyOS(tt.osName, tt.osVersion)
			// 注意：这里假设GetToolkitVersionMapKeyOS函数已经存在并正确实现
			// 实际测试时需要根据该函数的真实行为调整期望值
			if result == "" {
				t.Errorf("GetXPUToolkitVersionMapKeyOS(%s, %s) returned empty string",
					tt.osName, tt.osVersion)
			}
		})
	}
}
