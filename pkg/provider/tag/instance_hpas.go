package tag

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/hpas"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/tag"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
)

type hpasInstanceTagMember struct {
	stsClient  sts.Interface
	hpasClient hpas.Interface

	machineID string
	accountID string
}

func newHPASInstanceTagMember(ctx context.Context, config *Config, instance *ccev1.Instance) (*hpasInstanceTagMember, error) {
	if config == nil {
		return nil, fmt.Errorf("newBCCTagProvider failed: config is nil")
	}

	if config.STSEndpoint == "" || config.IAMEndpoint == "" || config.HPASOpenAPIEndpoint == "" ||
		config.Region == "" || config.Timeout == 0 {
		str, _ := json.Marshal(config)
		return nil, fmt.Errorf("newBCCTagProvider failed: config %v not matched", string(str))
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, config.MaxRetryTime, config.RetryIntervalSeconds)

	// 初始化 stsclient
	stsClient := sts.NewClient(ctx, &bce.Config{
		Endpoint:    config.STSEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, &bce.Config{
		Endpoint:    config.IAMEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)

	// 初始化 bcc client
	hpasClient := hpas.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.HPASOpenAPIEndpoint,
		RetryPolicy: retryPolicy,
	})

	machineID := getMachineID(instance)
	if machineID == "" {
		return nil, ErrEmptyMachineID
	}
	if instance.Spec.AccountID == "" {
		return nil, ErrEmptyAccountID
	}

	return &hpasInstanceTagMember{
		stsClient:  stsClient,
		hpasClient: hpasClient,
		machineID:  machineID,
		accountID:  instance.Spec.AccountID,
	}, nil
}

func (t *hpasInstanceTagMember) BindTags(ctx context.Context, owners ...OwnerTagGetter) error {
	if len(owners) == 0 {
		return nil
	}

	var hpasTags []tag.Tag
	for _, owner := range owners {
		ownerTag := owner.GetTag(ctx)
		hpasTags = append(hpasTags, tag.Tag{
			TagKey:   ownerTag.TagKey,
			TagValue: ownerTag.TagValue,
		})
	}

	attachTagsReq := &hpas.TagsReq{
		ResourceId:   []string{t.machineID},
		ResourceType: "hpas",
		Tags:         hpasTags,
	}

	return t.hpasClient.AttachTag(ctx, attachTagsReq, t.stsClient.NewSignOption(ctx, t.accountID))
}

func (t *hpasInstanceTagMember) UnbindTags(ctx context.Context, owners ...OwnerTagGetter) error {
	if len(owners) == 0 {
		return nil
	}

	var hpasTags []tag.Tag
	for _, owner := range owners {
		ownerTag := owner.GetTag(ctx)
		hpasTags = append(hpasTags, tag.Tag{
			TagKey:   ownerTag.TagKey,
			TagValue: ownerTag.TagValue,
		})
	}
	detachTagsReq := &hpas.TagsReq{
		ResourceId:   []string{t.machineID},
		ResourceType: "hpas",
		Tags:         hpasTags,
	}

	return t.hpasClient.DetachTags(ctx, detachTagsReq, t.stsClient.NewSignOption(ctx, t.accountID))
}
