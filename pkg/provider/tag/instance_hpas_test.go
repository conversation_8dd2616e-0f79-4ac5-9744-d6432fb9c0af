package tag

import (
	"context"

	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/hpas"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	hpasmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/hpas/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/tag"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

func TestHPASTagProvider_BindTags(t *testing.T) {
	type mocks struct {
		ctrl       *gomock.Controller
		stsClient  sts.Interface
		hpasClient hpas.Interface
	}

	testCases := []struct {
		name        string
		mocks       *mocks
		owners      []OwnerTagGetter
		expectedErr bool
	}{
		{
			name: "bind cluster tag successfully",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				stsClient := stsmock.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(gomock.Any(), "account-id").Return(nil)
				hpasClient := hpasmock.NewMockInterface(ctrl)
				hpasClient.EXPECT().AttachTag(gomock.Any(), &hpas.TagsReq{
					ResourceId:   []string{"i-xxxx"},
					ResourceType: "hpas",
					Tags: []tag.Tag{
						{
							TagKey:   ccetypes.ClusterIDTagKey,
							TagValue: "cluster-id",
						},
					},
				}, gomock.Any()).Return(nil)

				return &mocks{
					ctrl:       ctrl,
					stsClient:  stsClient,
					hpasClient: hpasClient,
				}
			}(),
			owners: []OwnerTagGetter{
				&clusterTagGetter{
					clusterID: "cluster-id",
				},
			},
			expectedErr: false,
		},
		{
			name: "bind tags with hpas client error",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				stsClient := stsmock.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(gomock.Any(), "account-id").Return(nil)
				hpasClient := hpasmock.NewMockInterface(ctrl)
				hpasClient.EXPECT().AttachTag(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("hpas error"))

				return &mocks{
					ctrl:       ctrl,
					stsClient:  stsClient,
					hpasClient: hpasClient,
				}
			}(),
			owners: []OwnerTagGetter{
				&clusterTagGetter{
					clusterID: "cluster-id",
				},
			},
			expectedErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			p := &hpasInstanceTagMember{
				stsClient:  tc.mocks.stsClient,
				hpasClient: tc.mocks.hpasClient,
				machineID:  "i-xxxx",
				accountID:  "account-id",
			}
			err := p.BindTags(context.Background(), tc.owners...)
			if (err == nil && tc.expectedErr) || (err != nil && !tc.expectedErr) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestHPASTagProvider_UnbindTags_ErrorCases(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	stsClient := stsmock.NewMockInterface(ctrl)
	stsClient.EXPECT().NewSignOption(gomock.Any(), "account-id").Return(nil)
	hpasClient := hpasmock.NewMockInterface(ctrl)
	hpasClient.EXPECT().DetachTags(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("detach error"))

	p := &hpasInstanceTagMember{
		stsClient:  stsClient,
		hpasClient: hpasClient,
		machineID:  "i-xxxx",
		accountID:  "account-id",
	}

	owners := []OwnerTagGetter{
		&clusterTagGetter{
			clusterID: "cluster-id",
		},
	}

	err := p.UnbindTags(context.Background(), owners...)
	if err == nil {
		t.Error("expected error but got nil")
	}
}

func TestNewHPASInstanceTagMember(t *testing.T) {
	testCases := []struct {
		name        string
		config      *Config
		instance    *ccev1.Instance
		expectedErr bool
		checkResult func(*hpasInstanceTagMember) bool
	}{
		{
			name:   "nil config",
			config: nil,
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
				},
			},
			expectedErr: true,
		},
		{
			name: "invalid config - missing endpoints",
			config: &Config{
				STSEndpoint: "",
				Region:      "bj",
				Timeout:     30,
			},
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
				},
			},
			expectedErr: true,
		},
		{
			name: "empty machine ID",
			config: &Config{
				STSEndpoint:          "https://sts.bj.baidubce.com",
				IAMEndpoint:          "https://iam.bj.baidubce.com",
				HPASOpenAPIEndpoint:  "https://hpas.bj.baidubce.com",
				Region:               "bj",
				Timeout:              30,
				MaxRetryTime:         3,
				RetryIntervalSeconds: 1,
				ServiceRoleName:      "test-role",
				ServiceName:          "test-service",
				ServicePassword:      "test-password",
			},
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
				},
			},
			expectedErr: true,
		},
		{
			name: "empty account ID",
			config: &Config{
				STSEndpoint:          "https://sts.bj.baidubce.com",
				IAMEndpoint:          "https://iam.bj.baidubce.com",
				HPASOpenAPIEndpoint:  "https://hpas.bj.baidubce.com",
				Region:               "bj",
				Timeout:              30,
				MaxRetryTime:         3,
				RetryIntervalSeconds: 1,
				ServiceRoleName:      "test-role",
				ServiceName:          "test-service",
				ServicePassword:      "test-password",
			},
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "",
					MachineType: ccetypes.MachineTypeHPAS,
					Existed:     true,
					ExistedOption: ccetypes.ExistedOption{
						ExistedInstanceID: "i-existed",
					},
				},
			},
			expectedErr: true,
		},
		{
			name: "successful creation with existed instance",
			config: &Config{
				STSEndpoint:          "https://sts.bj.baidubce.com",
				IAMEndpoint:          "https://iam.bj.baidubce.com",
				HPASOpenAPIEndpoint:  "https://hpas.bj.baidubce.com",
				Region:               "bj",
				Timeout:              30,
				MaxRetryTime:         3,
				RetryIntervalSeconds: 1,
				ServiceRoleName:      "test-role",
				ServiceName:          "test-service",
				ServicePassword:      "test-password",
			},
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
					Existed:     true,
					ExistedOption: ccetypes.ExistedOption{
						ExistedInstanceID: "i-existed",
					},
				},
			},
			expectedErr: false,
			checkResult: func(member *hpasInstanceTagMember) bool {
				return member.machineID == "i-existed" && member.accountID == "account-id"
			},
		},
		{
			name: "successful creation with status instance ID",
			config: &Config{
				STSEndpoint:          "https://sts.bj.baidubce.com",
				IAMEndpoint:          "https://iam.bj.baidubce.com",
				HPASOpenAPIEndpoint:  "https://hpas.bj.baidubce.com",
				Region:               "bj",
				Timeout:              30,
				MaxRetryTime:         3,
				RetryIntervalSeconds: 1,
				ServiceRoleName:      "test-role",
				ServiceName:          "test-service",
				ServicePassword:      "test-password",
			},
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
				},
				Status: ccetypes.InstanceStatus{
					Machine: ccetypes.Machine{
						InstanceID: "i-status",
					},
				},
			},
			expectedErr: false,
			checkResult: func(member *hpasInstanceTagMember) bool {
				return member.machineID == "i-status" && member.accountID == "account-id"
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			member, err := newHPASInstanceTagMember(context.TODO(), tc.config, tc.instance)

			if tc.expectedErr {
				if err == nil {
					t.Error("expected error but got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tc.checkResult != nil && !tc.checkResult(member) {
				t.Error("result check failed")
			}
		})
	}
}

func TestHPASTagProvider_UnbindTags(t *testing.T) {
	type mocks struct {
		ctrl       *gomock.Controller
		stsClient  sts.Interface
		bccClient  bcc.Interface
		tagClient  tag.Interface
		hpasClient hpas.Interface
	}

	testCases := []struct {
		name        string
		mocks       *mocks
		instance    *ccev1.Instance
		owners      []OwnerTagGetter
		expectedErr bool
	}{
		{
			name: "unbind bcc tag from cluster",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(gomock.Any(), "account-id").Return(nil)
				hpasClient := hpasmock.NewMockInterface(ctrl)
				hpasClient.EXPECT().DetachTags(gomock.Any(), &hpas.TagsReq{
					ResourceId:   []string{"i-xxxx"},
					ResourceType: "hpas",
					Tags: []tag.Tag{
						{
							TagKey:   ccetypes.ClusterIDTagKey,
							TagValue: "cluster-id",
						},
					},
				}, gomock.Any()).Return(nil)

				return &mocks{
					ctrl:       ctrl,
					stsClient:  stsClient,
					hpasClient: hpasClient,
				}
			}(),
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID:   "account-id",
					MachineType: ccetypes.MachineTypeHPAS,
					Existed:     true,
					ExistedOption: ccetypes.ExistedOption{
						ExistedInstanceID: "i-xxxx",
						Rebuild:           nil,
					},
				},
				Status: ccetypes.InstanceStatus{},
			},
			owners: []OwnerTagGetter{
				&clusterTagGetter{
					clusterID: "cluster-id",
				},
			},
			expectedErr: false,
		},
		{
			name: "unbind bcc tag from instanceGroup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(gomock.Any(), "account-id").Return(nil)
				hpasClient := hpasmock.NewMockInterface(ctrl)
				hpasClient.EXPECT().DetachTags(gomock.Any(), &hpas.TagsReq{
					ResourceId:   []string{"i-xxxx"},
					ResourceType: "hpas",
					Tags: []tag.Tag{
						{
							TagKey:   ccetypes.InstanceGroupIDTagKey,
							TagValue: "instancegroup-id",
						},
					},
				}, gomock.Any()).Return(nil)

				return &mocks{
					ctrl:       ctrl,
					stsClient:  stsClient,
					hpasClient: hpasClient,
				}
			}(),
			instance: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					AccountID: "account-id",
				},
				Status: ccetypes.InstanceStatus{
					Machine: ccetypes.Machine{
						InstanceID: "i-xxxx",
					},
				},
			},
			owners: []OwnerTagGetter{
				&instanceGroupTagGetter{
					instanceGroupID: "instancegroup-id",
				},
			},
			expectedErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			p := &hpasInstanceTagMember{
				stsClient:  tc.mocks.stsClient,
				hpasClient: tc.mocks.hpasClient,
				machineID:  getMachineID(tc.instance),
				accountID:  tc.instance.Spec.AccountID,
			}
			err := p.UnbindTags(context.Background(), tc.owners...)
			if (err == nil && tc.expectedErr) || (err != nil && !tc.expectedErr) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
				return
			}
		})
	}
}
