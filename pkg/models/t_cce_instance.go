// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/06 17:09:00, by <EMAIL>, create
*/
/*
封装 Instance 对象的数据库操作
*/

package models

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	gromtag "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/gotag/gorm"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/gotag/validate"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

// Instance mapping to t_cce_instance
type Instance struct {
	BaseModel

	Spec   *ccetypes.InstanceSpec   `json:"spec" gorm:"EMBEDDED"`
	Status *ccetypes.InstanceStatus `json:"status" gorm:"EMBEDDED"`
}

// InstanceList - InstanceList 列表
type InstanceList struct {
	Items      []*Instance
	TotalCount int
}

// InstanceFieldMap - instance 数据库字段映射表
var InstanceFieldMap = map[string]string{
	"clusterRole":       "cluster_role",
	"instanceName":      "instance_name",
	"instanceID":        "instance_id",
	"hostname":          "hostname",
	"vpcIP":             "vpc_ip",
	"createdAt":         "created_at",
	"instanceGroupID":   "instance_group_id",
	"instanceGroupName": "instance_group_name",
	"instanceId":        "instance_id",
	"fixIp":             "vpc_ip",
	"becRegion":         "bec_region",
	"serviceID":         "order_id", // bec 虚机对比 bcc 多了一个 serviceID，但是没有订单ID，所以复用该字段作为 serviceID。
}

// 获取实例表的属性，传入key命中或者和value匹配则返回
func getInstanceField(fieldName string) string {
	val, ok := InstanceFieldMap[fieldName]
	if ok {
		return val
	}

	for _, v := range InstanceFieldMap {
		if v == fieldName {
			return fieldName
		}
	}
	return ""
}

const (
	// InstanceKeywordTypeDefault instance 默认模糊匹配字段
	InstanceKeywordTypeDefault string = "instance_name"

	// InstanceOrderByDefault instance 默认排序字段
	InstanceOrderByDefault string = "instance_name"

	namespaceDefault = "default"
)

// InstanceListOption - 查询节点列表的选项
type InstanceListOption struct {
	AccountID       string
	ClusterID       string
	CCEInstanceIDs  []string
	ClusterRole     ccetypes.ClusterRole
	PageNo          int
	PageSize        int
	TotalCountOnly  bool
	KeywordType     string
	Keyword         string
	InstanceGroupID string
	BecRegion       string
	OrderBy         string
	Order           string
	ChargingType    *string // 计费方式筛选
}

// TableName database tableName
func (*Instance) TableName() string {
	return "t_cce_instance"
}

// CreateInstances 新建节点
func (c *Client) CreateInstances(ctx context.Context, clusterID string, instances []*Instance) ([]string, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	if len(instances) == 0 {
		logger.Infof(ctx, "instances is empty, skip createInstances")
		return []string{}, nil
	}

	var err error
	var cceInstanceIDList []string

	// 开始事务
	tx := c.db.Begin()

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "Error accur during CreateNode, rollback transaction")
			tx.Rollback()
		}
	}()

	cceInstanceIDList, err = c.insertInstances(ctx, clusterID, instances, tx)
	if err != nil {
		logger.Infof(ctx, "insertInstances failed: %s", err)
		return cceInstanceIDList, err
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logger.Errorf(ctx, "DB commit failed: %v", err)
		return cceInstanceIDList, err
	}

	return cceInstanceIDList, nil
}

// GetInstanceByCCEID - 通过 cceInstanceID 获取 Instance
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: instance ID defined by CCE or instance id by bcc
//   - accountID: account ID
//
// RETURNS:
//
//	*Instance: instance details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByCCEID(ctx context.Context, cceInstanceID string, accountID string) (*Instance, error) {
	logger.WithValues(ctx, "method", "GetInstanceByCCEID")

	if cceInstanceID == "" || accountID == "" {
		return nil, errors.New("cceInstanceID or accountID is empty")
	}

	var instances []*Instance

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id = ? AND account_id = ? AND instance_phase <> ?", cceInstanceID, accountID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "Where cceInstanceID=%s accountID=%s instance_phase=deleted failed: %v", cceInstanceID, accountID, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "cceInstanceID=%s not exists", cceInstanceID)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi cceInstanceID=%s exist", cceInstanceID)
	}

	return instances[0], nil
}

// GetInstanceByCCEIDWithDeleted - 通过 cceInstanceID 获取 Instance，包括已删除的Instance
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: instance ID defined by CCE or instance id by bcc
//   - accountID: account ID
//
// RETURNS:
//
//	*Instance: instance details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByCCEIDWithDeleted(ctx context.Context, cceInstanceID string, accountID string) (*Instance, error) {
	logger.WithValues(ctx, "method", "GetInstanceByCCEID")

	if cceInstanceID == "" || accountID == "" {
		return nil, errors.New("cceInstanceID or accountID is empty")
	}

	var instances []*Instance

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id = ? AND account_id = ?", cceInstanceID, accountID).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "Where cceInstanceID=%s accountID=%s failed: %v", cceInstanceID, accountID, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "cceInstanceID=%s not exists", cceInstanceID)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi cceInstanceID=%s exist", cceInstanceID)
	}

	return instances[0], nil
}

// GetInstanceByCCEIDWithoutStatusDeleted - 通过 cceInstanceID 获取 Instance,包含状态为Deleted的实例
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: instance ID defined by CCE or instance id by bcc
//   - accountID: account ID
//
// RETURNS:
//
//	*Instance: instance details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByCCEIDWithoutStatusDeleted(ctx context.Context, cceInstanceID string, accountID string) (*Instance, error) {
	logger.WithValues(ctx, "method", "GetInstanceByCCEIDWithoutStatusDeleted")

	if cceInstanceID == "" || accountID == "" {
		return nil, errors.New("cceInstanceID or accountID is empty")
	}

	var instances []*Instance

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id = ? AND account_id = ?", cceInstanceID, accountID).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "Where cceInstanceID=%s accountID=%s failed: %v", cceInstanceID, accountID, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "cceInstanceID=%s not exists", cceInstanceID)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi cceInstanceID=%s exist", cceInstanceID)
	}

	return instances[0], nil
}

func (c *Client) GetInstanceByClusterIDAndVPCIP(ctx context.Context, clusterID string, vpcIP string) (*Instance, error) {
	if clusterID == "" || vpcIP == "" {
		return nil, errors.New("clusterID or vpcIP is empty")
	}
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cluster_id = ? AND vpc_ip = ? AND instance_phase <> ?", clusterID, vpcIP, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT cluster_id=%s vpc_ip=%s failed: %v", clusterID, vpcIP, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "instance with clusterID=%s and vpcIP=%s not exists", clusterID, vpcIP)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi instance with clusterID=%s and vpcIP=%s exist", clusterID, vpcIP)
	}

	return instances[0], nil
}

func (c *Client) GetInstanceByClusterIDAndHostname(ctx context.Context, clusterID string, hostname string) (*Instance, error) {
	if clusterID == "" || hostname == "" {
		return nil, errors.New("clusterID or hostname is empty")
	}
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cluster_id = ? AND hostname = ? AND instance_phase <> ?", clusterID, hostname, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT cluster_id=%s hostname=%s failed: %v", clusterID, hostname, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "instance with clusterID=%s and hostname=%s not exists", clusterID, hostname)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi instance with clusterID=%s and hostname=%s exist", clusterID, hostname)
	}

	return instances[0], nil
}

func (c *Client) GetInstanceByClusterIDAndInstanceName(ctx context.Context, clusterID string, instanceName string) (*Instance, error) {
	if clusterID == "" || instanceName == "" {
		return nil, errors.New("clusterID or instanceName is empty")
	}
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cluster_id = ? AND instance_name = ? AND instance_phase <> ?", clusterID, instanceName, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT cluster_id=%s instance_name=%s failed: %v", clusterID, instanceName, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "instance with clusterID=%s and instance_name=%s not exists", clusterID, instanceName)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi instance with clusterID=%s and instance_name=%s exist", clusterID, instanceName)
	}

	return instances[0], nil
}

func (c *Client) GetInstanceByClusterIDAndInstanceGroupName(ctx context.Context, clusterID string, instanceGroupName string) (*Instance, error) {
	if clusterID == "" || instanceGroupName == "" {
		return nil, errors.New("clusterID or instanceName is empty")
	}
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cluster_id = ? AND instance_group_name = ? AND instance_phase = ?", clusterID, instanceGroupName, string(ccetypes.InstancePhaseRunning)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT cluster_id=%s instance_group_name=%s failed: %v", clusterID, instanceGroupName, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "instance with clusterID=%s and instance_group_name=%s not exists", clusterID, instanceGroupName)
	}

	result := rand.Intn(len(instances))

	return instances[result], nil
}

// GetInstanceByCCEIDWithoutAccount - 通过 cceInstanceID 获取 Instance
// TODO: 除后台程序外, 前端调用不要使用该方法
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: instance ID defined by CCE or instance id by bcc
//
// RETURNS:
//
//	*Instance: instance details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByCCEIDWithoutAccount(ctx context.Context, cceInstanceID string) (*Instance, error) {
	if cceInstanceID == "" {
		return nil, errors.New("cceInstanceID  is empty")
	}

	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id = ? AND instance_phase <> ?", cceInstanceID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT cceInstanceID=%s failed: %v", cceInstanceID, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "cceInstanceID=%s not exists", cceInstanceID)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi cceInstanceID=%s exist", cceInstanceID)
	}

	return instances[0], nil
}

// GetInstanceByBCCID - 通过 bcc bbc id 获取 Instance
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceID: instance ID defined by CCE or instance id by bcc
//   - accountID: account ID
//
// RETURNS:
//
//	*Instance: instance details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceByBCCID(ctx context.Context, instanceID string, accountID string) (*Instance, error) {
	if instanceID == "" || accountID == "" {
		return nil, errors.New("instanceID or accountID is empty")
	}

	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("instance_id = ? AND account_id = ? AND instance_phase <> ?", instanceID, accountID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "SELECT instanceID=%s accountID=%s failed: %v", instanceID, accountID, err)
		return nil, err
	}

	if len(instances) == 0 {
		return nil, ErrNotExist.New(ctx, "instanceID=%s not exists", instanceID)
	}

	if len(instances) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi instanceID=%s exist", instanceID)
	}

	return instances[0], nil
}

// GetInstancesByCCEIDs - 通过 UUIDs 获取 Instances
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceIDs: cceInstanceID list
//   - accountID: account ID
//
// RETURNS:
//
//	[]*Instance: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesByCCEIDs(ctx context.Context, clusterID string, cceInstanceIDs []string, accountID string) ([]*Instance, error) {
	instances := []*Instance{}

	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	if accountID == "" {
		return nil, errors.New("accountID is empty")
	}

	if len(cceInstanceIDs) == 0 {
		logger.Infof(ctx, "cceInstanceIDs is empty")
		return instances, nil
	}

	// cceInstanceIDs 清理
	ids := utils.ValidateInstanceIDs(ctx, cceInstanceIDs)

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id in (?) AND cluster_id = ? AND account_id = ? AND instance_phase <> ?",
			ids, clusterID, accountID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select cceInstanceID=%v clusterID=%s accountID=%s failed: %v", ids, clusterID, accountID, err)
		return nil, err
	}

	return instances, nil
}

// GetInstancesByBCCIDs - 通过 instance IDs 获取 Instances
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceIDs: instanceID list
//   - accountID: account ID
//
// RETURNS:
//
//	[]*Instance: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesByBCCIDs(ctx context.Context, clusterID string, instanceIDs []string, accountID string) ([]*Instance, error) {
	var err error
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	if accountID == "" {
		return nil, errors.New("accountID is empty")
	}

	if len(instanceIDs) == 0 {
		logger.Infof(ctx, "instanceIDs is empty")
		return []*Instance{}, nil
	}

	// instanceIDs 清理
	ids := utils.ValidateInstanceIDs(ctx, instanceIDs)

	// 这个事务，只是为了保证读主库，避免上层逻辑受到主从同步延迟的影响
	tx := c.db.Begin()

	// InstanceID 和 ExistedInstanceID 都需要考虑
	// 注意 ExistedInstanceID 大部分均为空, 务必保证 ids 不存在 ""
	instances := []*Instance{}
	if err := tx.Unscoped().Table(c.cceInstanceTableName).
		Where("(instance_id in (?) OR existed_instance_id in (?)) AND cluster_id = ? AND account_id = ? AND instance_phase <> ?",
			ids, ids, clusterID, accountID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instanceID=%v accountID=%s failed: %v", instanceIDs, accountID, err)
		return nil, err
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logger.Errorf(ctx, "tx commit failed: %v", err)
		return nil, err
	}

	return instances, nil
}

// GetInstancesByPage - 获取集群未删除的 Node, 支持模糊查询、排序、分页
//
// PARAMS:
//   - ctx: The context to trace request
//   - masterIncluded: 是否包含 master
//   - accountID: account ID
//   - clusterID: cluster ID
//   - keywordType: 模糊查询的数据库字段
//   - keyword: 模糊匹配的关键字
//   - orderBy: 按照什么数据库字段排序
//   - order: 升序还是降序
//   - pageSize: 每页元素数目
//   - pageNo: 查询第几页
//
// RETURNS:
//
//	[]*Instance: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesByPage(ctx context.Context, accountID, clusterID, keywordType, keyword, orderBy, order string,
	pageSize, pageNo int, clusterRole ccetypes.ClusterRole, becRegion string) ([]*Instance, error) {
	instances := []*Instance{}

	// TODO: phases 放不进来, 是因为前端查询涉及了 NodeReady 和 NodeNotReady, 交互和实现都有问题, 蛋疼
	query, err := getInstancesByPageQuery(ctx, keywordType, clusterRole, becRegion)
	if err != nil {
		logger.Errorf(ctx, "getInstancesByPageQuery failed: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "GetInstancesByPage SQL: %s", query)

	if becRegion == "" {
		if err := c.db.Unscoped().Table(c.cceInstanceTableName).
			Where(query, accountID, clusterID, "%"+keyword+"%", string(ccetypes.InstancePhaseDeleted)).
			Order("cluster_role asc, " + orderBy + " " + order).
			Offset((pageNo - 1) * pageSize).
			Limit(pageSize).
			Find(&instances).Error; err != nil {
			logger.Errorf(ctx, "query instances failed: %v", err)
			return nil, err
		}
	} else {
		if err := c.db.Unscoped().Table(c.cceInstanceTableName).
			Where(query, accountID, clusterID, "%"+keyword+"%", string(ccetypes.InstancePhaseDeleted), becRegion). // TODO: 防止 SQL 注入, 实现待改进
			Order("cluster_role asc, " + orderBy + " " + order).
			Offset((pageNo - 1) * pageSize).
			Limit(pageSize).
			Find(&instances).Error; err != nil {
			logger.Errorf(ctx, "query instances failed: %v", err)
			return nil, err
		}
	}

	return instances, nil
}

func (c *Client) GetInstancesByAccountID(ctx context.Context, accountID string, clusterRole ccetypes.ClusterRole) ([]*Instance, error) {
	logger.WithValues(ctx, "method", "GetInstanceByCCEID")
	startTime := time.Now()
	defer func() {
		logger.Infof(ctx, "GetInstancesByAccountID %s, cost: %v", accountID, time.Since(startTime))
	}()

	var instances []*Instance

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Select([]string{"cluster_id", "vpc_ip"}).
		Where("account_id = ? AND cluster_role = ? AND instance_phase <> ?", accountID, clusterRole, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "Where accountID=%s instance_phase=deleted failed: %v", accountID, err)
		return nil, err
	}
	return instances, nil
}

// GetInstancesByBatchQuery - 批量搜索集群未删除的 Node, 支持精确查询、排序、分页
//
// PARAMS:
//   - ctx: The context to trace request
//   - masterIncluded: 是否包含 master
//   - accountID: account ID
//   - clusterID: cluster ID
//   - keywordType: 查询的数据库字段
//   - keyword: 批次搜索匹配的多个关键字
//   - orderBy: 按照设置的数据库字段排序
//   - order: 升序还是降序
//   - pageSize: 每页元素数目
//   - pageNo: 查询第几页
//
// RETURNS:
//
//	[]*Instance: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesByBatchQuery(ctx context.Context, accountID, clusterID, keywordType, keyword, orderBy, order string,
	pageSize, pageNo int, clusterRole ccetypes.ClusterRole, becRegion string) ([]*Instance, error) {
	instances := []*Instance{}

	query, err := getInstancesByBatchQuery(ctx, keywordType, clusterRole, becRegion)
	if err != nil {
		logger.Errorf(ctx, "GetInstancesByBatchQuery failed: %v", err)
		return nil, err
	}

	logger.Infof(ctx, "GetInstancesByBatchQuery SQL: %s", query)

	keywords := strings.Split(keyword, ",")
	for i, k := range keywords {
		logger.Infof(ctx, "keyword= %s", k)
		keywords[i] = strings.TrimSpace(k)
	}

	if becRegion == "" {
		if err := c.db.Unscoped().Table(c.cceInstanceTableName).
			Where(query, accountID, clusterID, keywords, string(ccetypes.InstancePhaseDeleted)).
			Order("cluster_role asc, " + orderBy + " " + order).
			Offset((pageNo - 1) * pageSize).
			Limit(pageSize).
			Find(&instances).Error; err != nil {
			logger.Errorf(ctx, "query instances failed: %v", err)
			return nil, err
		}
	} else {
		if err := c.db.Unscoped().Table(c.cceInstanceTableName).
			Where(query, accountID, clusterID, keywords, string(ccetypes.InstancePhaseDeleted), becRegion). // TODO: 防止 SQL 注入, 实现待改进
			Order("cluster_role asc, " + orderBy + " " + order).
			Offset((pageNo - 1) * pageSize).
			Limit(pageSize).
			Find(&instances).Error; err != nil {
			logger.Errorf(ctx, "query instances failed: %v", err)
			return nil, err
		}
	}

	return instances, nil
}

// GetInstanceEx - 获取节点列表
func (c *Client) GetInstanceEx(ctx context.Context, option InstanceListOption) (*InstanceList, error) {
	// accountID和集群 ID强制传入，避免跨集群或跨账号查询
	if option.AccountID == "" {
		return nil, errors.New("account_id is not supported")
	}
	if option.ClusterID == "" {
		return nil, errors.New("cluster_id is not supported")
	}

	q := c.db.Unscoped().Table(c.cceInstanceTableName).Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted))

	queryMap := map[string]interface{}{
		"account_id": option.AccountID,
		"cluster_id": option.ClusterID,
	}
	if option.ClusterRole != "" {
		queryMap["cluster_role"] = option.ClusterRole
	}
	// 如果指定了节点 ID，则优先使用节点 ID 查询；否则检查InstanceGroupID和keywordType
	if len(option.CCEInstanceIDs) > 0 {
		q = q.Where("cce_instance_id IN (?)", option.CCEInstanceIDs)
	} else {
		if option.InstanceGroupID != "" {
			queryMap["instance_group_id"] = option.InstanceGroupID
		}
	}

	if option.BecRegion != "" {
		queryMap["bec_region"] = option.BecRegion
	}

	// 计费方式筛选：支持Prepaid(包年包月)、Postpaid(按量付费)、bid(抢占实例)
	if option.ChargingType != nil && *option.ChargingType != "" {
		queryMap["instance_charging_type"] = *option.ChargingType
	}

	// 注意 queryMap的配置都需要在这前面，新加查询条件需要加单测
	q = q.Where(queryMap)

	// 如果没有指定keyword，则忽略该条件; 如果 keyword只有','，则说明关键词为空，查询条件不用 in 而是 = ''
	if option.Keyword != "" {
		// 兼容接口tag和db tag
		keywordType := getInstanceField(option.KeywordType)
		if keywordType == "" {
			return nil, fmt.Errorf("invalid keywordType: %s", option.KeywordType)
		}

		if strings.Contains(option.Keyword, ",") {
			keywords := strings.Split(option.Keyword, ",")
			for i, k := range keywords {
				keywords[i] = strings.TrimSpace(k)
			}

			if len(keywords) > 0 {
				q = q.Where(keywordType+" IN (?)", keywords)
			} else {
				q = q.Where(keywordType + " = ''")
			}
		} else {
			q = q.Where(keywordType+" LIKE ?", "%"+option.Keyword+"%")
		}
	}

	var list = InstanceList{
		Items: make([]*Instance, 0),
	}
	if err := q.Count(&list.TotalCount).Error; err != nil {
		logger.Errorf(ctx, "select count(instanceGroup) failed, option: %s, err: %v", utils.ToJSON(option), err)
		return nil, err
	}
	if option.TotalCountOnly {
		return &list, nil
	}

	// 不指定排序方式默认按创建时间降序
	if option.OrderBy == "" {
		option.OrderBy = "created_at"
		option.Order = "DESC"
	}
	// 兼容接口tag和db tag
	orderBy := getInstanceField(option.OrderBy)
	if orderBy == "" {
		return nil, fmt.Errorf("invalid orderBy: %s", option.OrderBy)
	}
	q = q.Order(fmt.Sprintf("%s %s", orderBy, option.Order))

	if option.PageNo != 0 && option.PageSize != 0 {
		q = q.Offset((option.PageNo - 1) * option.PageSize).Limit(option.PageSize)
	}
	if err := q.Find(&list.Items).Error; err != nil {
		logger.Errorf(ctx, "select instances failed, option: %s, err: %v", utils.ToJSON(option), err)
		return nil, err
	}
	return &list, nil
}

func getInstancesByPageQuery(ctx context.Context, keywordType string, clusterRole ccetypes.ClusterRole, becRegion string) (string, error) {
	query := "account_id = ? AND cluster_id = ? AND " + keywordType + " LIKE ? AND instance_phase <> ?"

	// ClusterRole
	if clusterRole == ccetypes.ClusterRoleNode {
		query = query + " AND cluster_role = 'node'"
	} else if clusterRole == ccetypes.ClusterRoleMaster {
		query = query + " AND cluster_role = 'master'"
	}

	// becRegion
	if becRegion != "" {
		query = query + " AND bec_region = ?"
	}

	return query, nil
}

func getInstancesByBatchQuery(ctx context.Context, keywordType string, clusterRole ccetypes.ClusterRole, becRegion string) (string, error) {
	query := "account_id = ? AND cluster_id = ? AND " + keywordType + " IN (?) AND instance_phase <> ?"

	// ClusterRole
	if clusterRole == ccetypes.ClusterRoleNode {
		query = query + " AND cluster_role = 'node'"
	} else if clusterRole == ccetypes.ClusterRoleMaster {
		query = query + " AND cluster_role = 'master'"
	}

	// becRegion
	if becRegion != "" {
		query = query + " AND bec_region = ?"
	}

	return query, nil
}

// GetInstancesByInstanceGroupID - 获取节点组内未删除的 Node, 支持分页
//
// PARAMS:
//   - ctx: The context to trace request
//   - accountID: account ID
//   - clusterID: cluster ID
//   - instanceGroupID: instanceGroup ID
//   - pageSize: 每页元素数目
//   - pageNo: 查询第几页
//
// RETURNS:
//
//	*InstanceList: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesByInstanceGroupID(ctx context.Context, accountID, clusterID, instanceGroupID, keywordType, keyword, orderBy, order string, pageSize, pageNo int) (*InstanceList, error) {
	order = strings.ToUpper(order)

	instanceList := InstanceList{
		Items:      make([]*Instance, 0),
		TotalCount: 0,
	}

	q := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("account_id = ?", accountID).
		Where("cluster_id = ?", clusterID).
		Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted)).
		Where("instance_group_id = ?", instanceGroupID)

	if keywordType != "" && keyword != "" {
		columnName, found := InstanceFieldMap[keywordType]
		if !found {
			return nil, fmt.Errorf("unsupported searching field: %s", keywordType)
		}
		q = q.Where(fmt.Sprintf("%s LIKE ?", columnName), keyword)
	}

	q = q.Order("created_at DESC")
	if orderBy != "" {
		columnName, found := InstanceFieldMap[orderBy]
		if !found {
			return nil, fmt.Errorf("unsupported order field: %s", orderBy)
		}

		if order == "" {
			order = "ASC"
		}
		if order != "ASC" && order != "DESC" {
			return nil, fmt.Errorf("unsupported order: %s", order)
		}

		q = q.Order(fmt.Sprintf("%s %s", columnName, order), true)
	}

	if pageSize > 0 && pageNo > 0 {
		if err := q.Count(&instanceList.TotalCount).Error; err != nil {
			logger.Errorf(ctx, "count instances failed: %v", err)
			return nil, err
		}
		q = q.Offset((pageNo - 1) * pageSize).Limit(pageSize)
	}

	if err := q.Find(&instanceList.Items).Error; err != nil {
		logger.Errorf(ctx, "query instances failed: %v", err)
		return nil, err
	}

	if instanceList.TotalCount == 0 {
		instanceList.TotalCount = len(instanceList.Items)
	}
	return &instanceList, nil
}

// GetInstancesWithBatchQueryByInstanceGroupID - 获取节点组内未删除的 Node, 支持分页，支持批量查询
//
// PARAMS:
//   - ctx: The context to trace request
//   - accountID: account ID
//   - clusterID: cluster ID
//   - instanceGroupID: instanceGroup ID
//   - pageSize: 每页元素数目
//   - pageNo: 查询第几页
//   - keywordType: 查询的数据库字段
//   - keyword: 批次搜索匹配的多个关键字
//
// RETURNS:
//
//	*InstanceList: instance list
//	error: nil if succeed, error if fail
func (c *Client) GetInstancesWithBatchQueryByInstanceGroupID(ctx context.Context, accountID, clusterID, instanceGroupID, keywordType, keyword, orderBy, order string, pageSize, pageNo int) (*InstanceList, error) {
	order = strings.ToUpper(order)

	instanceList := InstanceList{
		Items:      make([]*Instance, 0),
		TotalCount: 0,
	}

	q := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("account_id = ?", accountID).
		Where("cluster_id = ?", clusterID).
		Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted)).
		Where("instance_group_id = ?", instanceGroupID)

	if keywordType != "" && keyword != "" {
		columnName := getInstanceField(keywordType)
		if columnName == "" {
			return nil, fmt.Errorf("unsupported searching field: %s", keywordType)
		}
		if strings.Contains(keyword, ",") {
			keywords := strings.Split(keyword, ",")
			for i, k := range keywords {
				logger.Infof(ctx, "keyword= %s", k)
				keywords[i] = strings.TrimSpace(k)
			}
			q = q.Where(fmt.Sprintf("%s IN (?)", columnName), keywords)
		} else {
			q = q.Where(fmt.Sprintf("%s LIKE ?", columnName), keyword)
		}
	}

	q = q.Order("created_at DESC")
	if orderBy != "" {
		columnName := getInstanceField(orderBy)
		if columnName == "" {
			return nil, fmt.Errorf("unsupported order field: %s", orderBy)
		}

		if order == "" {
			order = "ASC"
		}
		if order != "ASC" && order != "DESC" {
			return nil, fmt.Errorf("unsupported order: %s", order)
		}

		q = q.Order(fmt.Sprintf("%s %s", columnName, order), true)
	}

	if pageSize > 0 && pageNo > 0 {
		if err := q.Count(&instanceList.TotalCount).Error; err != nil {
			logger.Errorf(ctx, "count instances failed: %v", err)
			return nil, err
		}
		q = q.Offset((pageNo - 1) * pageSize).Limit(pageSize)
	}

	if err := q.Find(&instanceList.Items).Error; err != nil {
		logger.Errorf(ctx, "query instances failed: %v", err)
		return nil, err
	}

	if instanceList.TotalCount == 0 {
		instanceList.TotalCount = len(instanceList.Items)
	}
	return &instanceList, nil
}

// GetAllInstances - 获取所有instance
func (c *Client) GetAllInstances(ctx context.Context) ([]*Instance, error) {
	instances := []*Instance{}

	if err := c.db.Unscoped().
		Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}
	if len(instances) == 0 {
		logger.Errorf(ctx, "this db does not have any non-deleted instances")
		return nil, nil
	}

	return instances, nil
}

// GetInstances - 查询 Instance 列表
func (c *Client) GetInstances(ctx context.Context, accountID, clusterID string) ([]*Instance, error) {
	if accountID == "" {
		return nil, errors.New("accountID is empty")
	}

	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("account_id = ? AND cluster_id = ? AND instance_phase <> ?", accountID, clusterID, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}

	return instances, nil
}

func (c *Client) GetInstancesByHpas(ctx context.Context, accountID string) ([]*Instance, error) {
	if accountID == "" {
		return nil, errors.New("accountID is empty")
	}

	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("account_id = ? AND machine_type = ? AND instance_phase <> ?", accountID, "HPAS", string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}

	return instances, nil
}

// GetInstancesByRole - 查询 Instance 列表
func (c *Client) GetInstancesByRole(ctx context.Context, accountID, clusterID string, role ccetypes.ClusterRole) ([]*Instance, error) {
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("account_id = ? AND cluster_id = ? AND cluster_role = ? AND instance_phase <> ?", accountID, clusterID, role, string(ccetypes.InstancePhaseDeleted)).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "Select account_id=%s cluster_id=%s role=%s failed: %v",
			accountID, clusterID, role, err)

		return nil, err
	}

	return instances, nil
}

// GetSyncInstances - 用户 Sync 同步 Instance 列表
func (c *Client) GetSyncInstances(ctx context.Context, updateDuration time.Duration, cceInstanceIDs []string) ([]*Instance, error) {
	instances := []*Instance{}

	if len(cceInstanceIDs) == 0 {
		// 不是必选项，默认值
		cceInstanceIDs = []string{""}
	}

	// cceInstanceIDs 清理
	ids := utils.ValidateInstanceIDs(ctx, cceInstanceIDs)

	if updateDuration < 0 {
		updateDuration = -updateDuration
	}
	updateTime := time.Now().Add(-updateDuration)

	if err := c.db.Unscoped().
		Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted)).
		Where("( updated_at > ? OR cce_instance_id in (?) )", updateTime.UTC(), ids).
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}

	return instances, nil
}

// GetSyncCCEInstanceIDs  - 用户 Sync 同步 CCEInstanceID 列表
func (c *Client) GetSyncCCEInstanceIDs(ctx context.Context, updateDuration time.Duration, cceInstanceIDs []string) ([]string, error) {
	instances := []*Instance{}

	if len(cceInstanceIDs) == 0 {
		// 不是必选项，默认值
		cceInstanceIDs = []string{""}
	}

	if updateDuration < 0 {
		updateDuration = -updateDuration
	}
	updateTime := time.Now().Add(-updateDuration)

	if err := c.db.Unscoped().
		Where("instance_phase <> ?", string(ccetypes.InstancePhaseDeleted)).
		Where("( updated_at > ? OR cce_instance_id in (?) )", updateTime.UTC(), cceInstanceIDs).
		Select("cce_instance_id").
		Find(&instances).Error; err != nil {
		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}

	result := []string{}
	for _, instance := range instances {
		result = append(result, instance.Spec.CCEInstanceID)
	}

	return result, nil
}

// GetInstanceNum - 获取集群未删除的 instance 数
//
// PARAMS:
//   - ctx: The context to trace request
//   - accountID: account ID
//   - clusterID: cluster ID
//   - keywordType: 模糊查询的数据库字段
//   - keyword: 模糊匹配的关键字
//
// RETURNS:
//
//	int: node number in cluster
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceNum(ctx context.Context, accountID, clusterID, keywordType, keyword string, masterIncluded bool) (int, error) {
	var count int

	query := "account_id = ? AND cluster_id = ? AND " + keywordType + " LIKE ? AND instance_phase <> ? AND cluster_role = 'node'"
	if masterIncluded {
		query = "account_id = ? AND cluster_id = ? AND " + keywordType + " LIKE ? AND instance_phase <> ?"
	}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Model(&Instance{}).
		Where(query, accountID, clusterID, "%"+keyword+"%", string(ccetypes.InstancePhaseDeleted)).
		Count(&count).Error; err != nil {
		logger.Errorf(ctx, "count nodes failed: %v", err)
		return count, err
	}

	return count, nil
}

// UpdateInstancesDeleteOptionAndPhase - 更新 Instance 删除状态
//
// PARAMS:
//   - ctx: The context to trace request
//   - accountID: account ID
//   - cceInstanceID: instance ID defined by CCE
//   - deleteOption: instance delete options, MoveOut DeleteResource DeleteCDSSnapshot
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstancesDeleteOptionAndPhase(ctx context.Context, clusterID string, cceInstanceIDs []string, accountID string, deleteOption *ccetypes.DeleteOption) error {
	if len(cceInstanceIDs) <= 0 {
		return errors.New("cceInstanceIDs is empty")
	}

	if clusterID == "" {
		return errors.New("clusterID is empty")
	}

	if accountID == "" {
		return errors.New("accountID is empty")
	}

	// cceInstanceIDs 清理
	ids := utils.ValidateInstanceIDs(ctx, cceInstanceIDs)

	// 更新字段
	updateMap := map[string]any{
		"instance_phase": string(ccetypes.InstancePhaseDeleting),
	}

	if deleteOption != nil {
		updateMap["move_out"] = deleteOption.MoveOut
		updateMap["delete_resource"] = deleteOption.DeleteResource
		updateMap["delete_cds_snapshot"] = deleteOption.DeleteCDSSnapshot
	}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Where("cce_instance_id IN (?) AND cluster_id = ? AND account_id = ? AND instance_phase <> ?", ids, clusterID, accountID, ccetypes.InstancePhaseDeleted).
		Update(updateMap).
		Error; err != nil {
		logger.Errorf(ctx, "delete instance %v failed: %v", cceInstanceIDs, err)
		return err
	}

	return nil
}

// UpdatePartInstanceSpec - 更新 Instance 数据库中 spec，实际上有很多spec是不能更新的，这里只更新可更新的spec字段
// 目前仅仅update instance_group_id、instance_group_name、labels、taints
//
// PARAMS:
//   - ctx: The context to trace request
//   - accountID: account ID
//   - instanceID: instance ID defined by CCE
//   - spec: instance spec
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdatePartInstanceSpec(ctx context.Context, accountID, instanceID string, spec *ccetypes.InstanceSpec) error {
	if accountID == "" {
		return errors.New("empty accountID")
	}

	if instanceID == "" {
		return errors.New("empty instanceID")
	}

	if spec == nil {
		return errors.New("nil instanceSpec")
	}

	// 获取 Instance
	instance, err := c.GetInstanceByCCEID(ctx, instanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance cceInstanceID=%s failed: %v", instanceID, err)
		return err
	}

	if instance == nil {
		msg := fmt.Sprintf("instance %s not exist", instanceID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// struct 批量更新会忽略空串的更新, 而 instanceGroup 有需要给相关信息置空的操作, 所以用 map 方式更新
	// 目前仅仅update这些
	updateFields := map[string]any{
		"instance_group_id":      spec.InstanceGroupID,
		"instance_group_name":    spec.InstanceGroupName,
		"labels":                 spec.Labels,
		"taints":                 spec.Taints,
		"annotations":            spec.Annotations,
		"cce_instance_priority":  spec.CCEInstancePriority,
		"instance_type":          spec.InstanceType,
		"instance_name":          spec.InstanceName,
		"eip_bandwidth":          spec.EIPBandwidth,
		"instance_charging_type": spec.InstanceChargingType,
		"cpu":                    spec.CPU,
		"mem":                    spec.MEM,
		"vpc_subnet_type":        spec.VPCSubnetType,
		"vpc_subnet_id":          spec.VPCSubnetID,
		"vpc_subnet_uuid":        spec.VPCSubnetUUID,
		"vpc_subnet_cidr":        spec.VPCSubnetCIDR,
		"vpc_subnet_cidr_ipv6":   spec.VPCSubnetCIDRIPv6,
		"available_zone":         spec.AvailableZone,
		"need_eip":               spec.NeedEIP,
		"gpu_type":               spec.GPUType,
		"gpu_count":              spec.GPUCount,
	}

	// 更新 InstanceSpec
	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Model(instance).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instance %s spec failed: %v", instanceID, err)
		return err
	}
	return nil
}

// UpdateInstanceStatus - 更新 Instance 数据库中 status
// TODO: 该方法不能将某个字段更新成该字段类型对应的零值
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceID: instance ID defined by CCE
//   - accountID: account ID
//   - instanceStatus: instance status
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceStatus(ctx context.Context, cceInstanceID string, accountID string, instanceStatus *ccetypes.InstanceStatus) error {
	if cceInstanceID == "" {
		return errors.New("cceInstanceID is emtpy")
	}

	if accountID == "" {
		return errors.New("account is empty")
	}

	if instanceStatus == nil {
		logger.Warnf(ctx, "InstanceStatus is nil, skip UpdateInstanceStatus")
		return nil
	}

	// 获取 Instance
	instance, err := c.GetInstanceByCCEIDWithoutStatusDeleted(ctx, cceInstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance cceInstanceID=%s failed: %v", cceInstanceID, err)
		return err
	}

	if instance == nil {
		msg := fmt.Sprintf("instance %s is nil", cceInstanceID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// 更新 InstanceStatus
	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Model(instance).Updates(instanceStatus).Error; err != nil {
		logger.Errorf(ctx, "update instance %s status failed: %v", err)
		return err
	}

	return nil
}

// UpdateInstanceStatusPart - 更新 Instance 数据库中部分 status, 支持将某个字段更新成该字段类型对应的零值
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceID: instance ID defined by CCE
//   - accountID: account ID
//   - instanceStatus: instance status
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdatePartInstanceStatus(ctx context.Context, accountID string, cceInstanceID string, status *ccetypes.InstanceStatus) error {
	if accountID == "" {
		return errors.New("account is empty")
	}

	if cceInstanceID == "" {
		return errors.New("cceInstanceID is emtpy")
	}

	if status == nil {
		logger.Warnf(ctx, "InstanceStatus is nil, skip UpdateInstanceStatus")
		return nil
	}

	// 获取 Instance
	instance, err := c.GetInstanceByCCEID(ctx, cceInstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance cceInstanceID=%s failed: %v", cceInstanceID, err)
		return err
	}

	if instance == nil {
		msg := fmt.Sprintf("instance %s not exist", cceInstanceID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// struct 批量更新会忽略字段零值的更新, 而 eip 等信息存在置空的操作, 所以用 map 方式更新
	updateFields := map[string]any{
		"machine_status": status.Machine.MachineStatus,
		"vpc_ip":         status.Machine.VPCIP,
		"floating_ip":    status.Machine.FloatingIP,
		"eip":            status.Machine.EIP,
	}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Model(instance).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instance %s status failed: %v", cceInstanceID, err)
		return err
	}

	return nil
}

// UpdateInstancePhase - 更新 InstancePhase 状态
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceID: cce-instance-id
//   - accountID: accountID
//   - phase: instance 状态
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstancePhase(ctx context.Context, cceInstanceID string, accountID string, phase ccetypes.InstancePhase) error {
	if cceInstanceID == "" {
		return errors.New("cceInstanceID is empty")
	}

	if accountID == "" {
		return errors.New("accountID is empty")
	}

	if phase == "" {
		return errors.New("phase is empty")
	}

	// 获取 Instance
	instance, err := c.GetInstanceByCCEID(ctx, cceInstanceID, accountID)
	if err != nil && !IsNotExist(err) {
		logger.Errorf(ctx, "SELECT cceInstanceID=%s failed: %v", cceInstanceID, err)
		return err
	}

	if IsNotExist(err) || instance == nil {
		msg := fmt.Sprintf("Update instance %s phase failed: instance not exist", cceInstanceID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// 更新 Instance Phase
	if err := c.db.Unscoped().Table(c.cceInstanceTableName).Model(instance).Update("instance_phase", phase).Error; err != nil {
		logger.Errorf(ctx, "Update instance %s phase failed: %v", cceInstanceID, err)
		return err
	}

	return nil
}

type updateInstanceSpecFunc func(ctx context.Context, ns, name string, spec *ccetypes.InstanceSpec, instanceMeta *metav1.ObjectMeta,
	conditions map[validate.ValidateCondition]any, skipValidate bool) error

// UpdateInstanceSpecDBAndCRD - 更新 instanceSpec DB 记录和 CRD
func (c *Client) UpdateInstanceSpecDBAndCRD(ctx context.Context, accountID, cceInstanceID string, spec *ccetypes.InstanceSpec, instanceMeta *metav1.ObjectMeta,
	conditions map[validate.ValidateCondition]any, skipValidate bool, updateFunc updateInstanceSpecFunc) error {
	if accountID == "" || cceInstanceID == "" {
		return errors.New("accountID or cceInstanceID is empty")
	}

	if spec == nil {
		return errors.New("instanceSpec is nil")
	}

	var err error

	// 开始事务
	tx := c.db.Begin()
	defer func() {
		// 回滚事务
		if err != nil {
			logger.Errorf(ctx, "update instance spec in db and crd failed, rollback transaction")
			tx.Rollback()
		}
	}()

	// 获取 Instance
	instance, err := c.GetInstanceByCCEID(ctx, cceInstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance cceInstanceID=%s failed: %v", cceInstanceID, err)
		return err
	}

	if instance == nil {
		msg := fmt.Sprintf("instance %s not exist", cceInstanceID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	fieldsMap, err := gromtag.ConvertStructToMap(*spec)
	if err != nil {
		logger.Errorf(ctx, "convert instanceSpec to map failed: %v", err)
		return err
	}

	logger.Infof(ctx, "ConvertStructToMap succeeded: spec=%s, map=%s", utils.ToJSON(spec), utils.ToJSON(fieldsMap))

	// 更新 InstanceSpec
	if err = tx.Unscoped().Table(c.cceInstanceTableName).Model(instance).Updates(fieldsMap).Error; err != nil {
		logger.Errorf(ctx, "update instance %s spec failed: %v", cceInstanceID, err)
		return err
	}

	// 更新 InstanceSpec in CRD: 可能会失败 (有参数校验)，失败回滚
	err = updateFunc(ctx, DefaultNamespace, cceInstanceID, spec, instanceMeta, conditions, skipValidate)
	if err != nil {
		logger.Errorf(ctx, "update instance spec in crd failed: %v", err)
		return err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Errorf(ctx, "tx commit failed: %v", err)
		return err
	}

	return nil
}

type getInstanceCRDFunc func(ctx context.Context, ns, name string, opts *metav1.GetOptions) (*ccev1.Instance, error)

type updateInstanceCRDFunc func(ctx context.Context, ns, name string, instance *ccev1.Instance) error

// UpdatePartInstanceDBAndRCD - 更新 Instance DB 记录和 CRD
// 1) 先更新 DB, 如 CRD 更新失败, DB 更新会回滚, 确保 DB 和 CRD 记录一致
// 2) 仅仅是更新 instance 中部分值
// 3) 支持将某个字段更新成该字段类型对应的零值
// 4) gorm struct 批量更新会忽略字段零值的更新, 而 eip 等信息存在置空的操作, 所以用 map 方式更新
func (c *Client) UpdatePartInstanceDBAndCRD(ctx context.Context, accountID string, cceInstanceID string, instance *Instance, getInstanceCRD getInstanceCRDFunc, updateInstanceCRD updateInstanceCRDFunc) error {
	if accountID == "" || cceInstanceID == "" {
		return errors.New("account or cceInstanceID is empty")
	}

	spec, status := instance.Spec, instance.Status
	if instance == nil || spec == nil || status == nil {
		logger.Warnf(ctx, "instance spec or status is nil, skip UpdateInstanceStatus")
		return nil
	}

	var err error
	var instanceDB *Instance
	var instanceCRD *ccev1.Instance

	// 开启事务：否则可能会出现 DB CRD 不一致报警
	tx := c.db.Begin()
	defer func() {
		// 回滚事务
		if err != nil {
			logger.Errorf(ctx, "UpdatePartInstanceDBAndCRD failed, rollback transaction")
			tx.Rollback()
		}
	}()

	// 获取 Instance DB
	instanceDB, err = c.GetInstanceByCCEID(ctx, cceInstanceID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetInstance cceInstanceID=%s failed: %v", cceInstanceID, err)
		return err
	}

	if instanceDB == nil {
		logger.Errorf(ctx, "instance %s not exist", cceInstanceID)
		err = fmt.Errorf("instance %s not exist", cceInstanceID)
		return err
	}

	// 更新 DB 记录
	updateFields := map[string]any{
		"instance_name":          spec.InstanceName,
		"eip_bandwidth":          spec.EIPBandwidth,
		"instance_charging_type": spec.InstanceChargingType,
		// "root_disk_type":         spec.RootDiskType,
		"root_disk_size":       spec.RootDiskSize,
		"cpu":                  spec.CPU,
		"mem":                  spec.MEM,
		"vpc_subnet_type":      spec.VPCSubnetType,
		"vpc_subnet_id":        spec.VPCSubnetID,
		"vpc_subnet_uuid":      spec.VPCSubnetUUID,
		"vpc_subnet_cidr":      spec.VPCSubnetCIDR,
		"vpc_subnet_cidr_ipv6": spec.VPCSubnetCIDRIPv6,
		"available_zone":       spec.AvailableZone,
		"need_eip":             spec.NeedEIP,

		"machine_status": status.Machine.MachineStatus,
		"vpc_ip":         status.Machine.VPCIP,
		"floating_ip":    status.Machine.FloatingIP,
		"eip":            status.Machine.EIP,
	}

	if err = tx.Unscoped().Table(c.cceInstanceTableName).Model(instanceDB).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instance %s status failed: %v", cceInstanceID, err)
		return err
	}

	// 获取 Instance CRD
	instanceCRD, err = getInstanceCRD(ctx, namespaceDefault, cceInstanceID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "metaK8SClient.GetInstance %s failed: %v", cceInstanceID, err)
		return err
	}

	if instanceCRD == nil {
		logger.Errorf(ctx, "instance %s not exists in meta-cluster", cceInstanceID)
		err = fmt.Errorf("instance %s not exists in meta-cluster", cceInstanceID)
		return err
	}

	// 更新 CRD 记录: 必须与 updateFields 统一, 否则会出现 DB CRD 不一致报警
	// 防止因为 instance 状态不正常在 sync 时 iam 被覆盖
	if spec.IAMRole != nil {
		instanceCRD.Spec.IAMRole = spec.IAMRole
	}
	instanceCRD.Spec.InstanceName = spec.InstanceName
	instanceCRD.Spec.EIPBandwidth = spec.EIPBandwidth
	instanceCRD.Spec.InstanceChargingType = spec.InstanceChargingType
	// instanceCRD.Spec.RootDiskType = spec.RootDiskType
	instanceCRD.Spec.RootDiskSize = spec.RootDiskSize
	instanceCRD.Spec.CPU = spec.CPU
	instanceCRD.Spec.MEM = spec.MEM
	instanceCRD.Spec.VPCSubnetType = spec.VPCSubnetType
	instanceCRD.Spec.VPCSubnetID = spec.VPCSubnetID
	instanceCRD.Spec.VPCSubnetUUID = spec.VPCSubnetUUID
	instanceCRD.Spec.VPCSubnetCIDR = spec.VPCSubnetCIDR
	instanceCRD.Spec.VPCSubnetCIDRIPv6 = spec.VPCSubnetCIDRIPv6
	instanceCRD.Spec.AvailableZone = spec.AvailableZone
	instanceCRD.Spec.NeedEIP = spec.NeedEIP

	instanceCRD.Status.Machine.MachineStatus = status.Machine.MachineStatus
	instanceCRD.Status.Machine.VPCIP = status.Machine.VPCIP
	instanceCRD.Status.Machine.FloatingIP = status.Machine.FloatingIP
	instanceCRD.Status.Machine.EIP = status.Machine.EIP

	if err = updateInstanceCRD(ctx, namespaceDefault, cceInstanceID, instanceCRD); err != nil {
		logger.Errorf(ctx, "metaK8SClient.UpdateInstance %s failed: %v", cceInstanceID, err)
		return err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Errorf(ctx, "tx commit failed: %v", err)
		return err
	}

	return nil
}

// insertInstances - 新增 Instance 实例
//
// PARAMS:
//   - ctx: The context to trace request
//   - instances: []*Instance
//   - db: db *gorm.DB
//
// RETURNS:
//
//	[]string: 返回 Instance 的 cceInstanceID
//	error: nil if succeed, error if fail
func (c *Client) insertInstances(ctx context.Context, clusterID string, instanceList []*Instance, db *gorm.DB) ([]string, error) {
	cceInstanceIDList := []string{}

	if db == nil {
		return cceInstanceIDList, errors.New("gorm.DB is nil")
	}

	if len(instanceList) == 0 {
		logger.Infof(ctx, "instanceList is empty")
		return cceInstanceIDList, nil
	}

	// 已有实例, 检查是否在列表中
	existedInstanceIDs := map[string]int{}
	for _, i := range instanceList {
		instance := i

		if instance == nil || instance.Spec == nil {
			continue
		}

		clusterID := instance.Spec.ClusterID
		if clusterID == "" {
			return cceInstanceIDList, errors.New("instance.Spec.clusterID is empty")
		}

		accountID := instance.Spec.AccountID
		if accountID == "" {
			return cceInstanceIDList, errors.New("instance.Spec.AccountID is empty")
		}

		// 已有实例, 检查是否在库中
		if instance.Spec.ExistedOption.ExistedInstanceID != "" {
			logger.Infof(ctx, "ExistedInstanceID: %s", instance.Spec.ExistedOption.ExistedInstanceID)

			if instance.Spec.Existed == false {
				return cceInstanceIDList,
					fmt.Errorf("ExistedOption.InstanceID %s exist, but instance.Spec.Existed == false", instance.Spec.ExistedOption.ExistedInstanceID)
			}

			instanceID := instance.Spec.ExistedOption.ExistedInstanceID

			// 输入参数中 ExistedInstanceID 不能重复
			if _, ok := existedInstanceIDs[instanceID]; ok {
				logger.Errorf(ctx, "ExistedInstanceID %s already exists in input InstnaceGroupList", instanceID)
				return cceInstanceIDList, ErrExistedInstanceIDDuplicated.New(ctx,
					fmt.Sprintf("ExistedInstanceID %s already exists in input InstnaceGroupList", instanceID))
			}
			existedInstanceIDs[instanceID] = 0

			ids, err := c.GetInstancesByBCCIDs(ctx, clusterID, []string{instanceID}, accountID)
			// check instance not exists in current CCE Cluster
			if err != nil {
				logger.Errorf(ctx, "GetInstancesByIDs failed: %s", err)
				return cceInstanceIDList, err
			}

			if len(ids) >= 1 {
				logger.Errorf(ctx, "Instance %s already exist in CCE Cluster", instanceID)
				return cceInstanceIDList, ErrAlreadyExists.New(ctx, "Instance %s already exists in CCE", instanceID)
			}
		}
		var err error
		// check instances not exist in CCE
		err = c.CheckExistedInstanceIDAlreadyExistsInCCE(ctx, instanceList, accountID)
		if err != nil {
			logger.Errorf(ctx, "CheckExistedInstanceIDAlreadyExistsInCCE failed: %v", err)
			return cceInstanceIDList, err
		}

		// 构建 CCEInstanceID, 允许指定 CCEInstanceID 创建
		cceInstanceID := instance.Spec.CCEInstanceID
		if cceInstanceID == "" {
			cceInstanceID, err = c.generateCCEInstanceID(ctx, db, clusterID)
			if err != nil {
				logger.Errorf(ctx, "GenerateInstanceUUID failed: %v", err)
				return cceInstanceIDList, err
			}

			if cceInstanceID == "" {
				return cceInstanceIDList, errors.New("GenerateCCEInstanceID return cceInstanceID empty")
			}

			// 设置 CCEInstanceID
			instance.Spec.CCEInstanceID = cceInstanceID
		}

		// 设置 InstanceName
		if instance.Spec.InstanceName == "" {
			instance.Spec.InstanceName = cceInstanceID
		}

		// 完成数据库入库
		if err := db.Table(c.cceInstanceTableName).Create(instance).Error; err != nil {
			logger.Errorf(ctx, "instance %v in DB failed: %v", utils.ToJSON(instance), err)
			return cceInstanceIDList, err
		}

		cceInstanceIDList = append(cceInstanceIDList, cceInstanceID)
	}

	return cceInstanceIDList, nil
}

// DeepCopy - return deepcopy of *InstanceModel
// 是否抛弃字段交由上层处理
//
// RETURNS:
//
//	*InstanceModel: The DeepCopy of InstanceModel
//	error: nil if succeed, error if fail
func (i *Instance) DeepCopy() (*Instance, error) {
	if i.Spec == nil && i.Status == nil {
		return nil, errors.New("instance.Spec or instance.Status in nil")
	}

	instance := &Instance{}

	if i.Spec != nil {
		instance.Spec = i.Spec.DeepCopy()
	}

	if i.Status != nil {
		instance.Status = i.Status.DeepCopy()
	}

	return instance, nil
}

// UpdateInstanceDeleteTime - 更新 Instance 数据库中 deleted_at 字段
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceID: CCE InstanceID
//   - accountID: AccountID
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceDeleteTime(ctx context.Context, cceInstanceID string, accountID string) error {
	if cceInstanceID == "" || accountID == "" {
		return errors.New("cceInstanceID or accountID is empty")
	}

	// 更新 Instance DeletedTime
	if err := c.db.Table(c.cceInstanceTableName).Where("cce_instance_id = ? AND account_id = ?", cceInstanceID, accountID).
		UpdateColumn("deleted_at", time.Now()).Error; err != nil {
		logger.Errorf(ctx, "Update instance %s deleted_at failed: %v", cceInstanceID, err)
		return err
	}

	return nil
}

// GetClusterSubnets - 查询 cluster 节点所在的 zone 和 subnetCIDR
// 注意: 仅返回了 Instance 部分信息
func (c *Client) GetClusterSubnets(ctx context.Context, accountID string, clusterID string) ([]*Instance, error) {
	instances := []*Instance{}

	if err := c.db.Unscoped().Table(c.cceInstanceTableName).
		Select("distinct vpc_subnet_id, available_zone").
		Where("cluster_id=? and account_id=? and instance_phase!=? and cluster_role=?", clusterID, accountID, ccetypes.InstancePhaseDeleted, ccetypes.ClusterRoleNode).
		Scan(&instances).Error; err != nil {
		return nil, err
	}

	return instances, nil
}

func (c *Client) UpdatePartInstanceGPUInfo(ctx context.Context, accountID, cceInstanceID string, gpuType string, gpuCount int) error {
	if cceInstanceID == "" || accountID == "" {
		return errors.New("cceInstanceID or accountID is empty")
	}

	// 更新字段
	updateMap := map[string]any{
		"gpu_type":  gpuType,
		"gpu_count": gpuCount,
	}

	// 更新 Instance DeletedTime
	if err := c.db.Unscoped().Table(c.cceInstanceTableName).Where("cce_instance_id = ? AND account_id = ?", cceInstanceID, accountID).
		Update(updateMap).Error; err != nil {
		logger.Errorf(ctx, "Update instance %s deleted_at failed: %v", cceInstanceID, err)
		return err
	}

	return nil
}

func cmpInstance(a *Instance, b *Instance) bool {
	// 去除 id, created_at, updated_at, deleted_at
	a.BaseModel = BaseModel{}
	b.BaseModel = BaseModel{}

	if !cmp.Equal(a, b) {
		return false
	}

	return true
}

func cmpInstanceList(a []*Instance, b []*Instance) bool {
	if len(a) != len(b) {
		return false
	}

	am := make(map[string]*Instance, len(a))
	for _, instance := range a {
		c := instance
		am[instance.Spec.CCEInstanceID] = c
	}

	bm := make(map[string]*Instance, len(b))
	for _, instance := range b {
		c := instance
		bm[instance.Spec.CCEInstanceID] = c
	}

	for InstanceID, want := range am {
		target, ok := bm[InstanceID]
		if !ok {
			return false
		}

		if !cmpInstance(want, target) {
			return false
		}
	}

	return true
}
