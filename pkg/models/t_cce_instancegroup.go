/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  t_cce_instancegroup
 * @Version: 1.0.0
 * @Date: 2020/6/18 3:16 下午
 */
package models

import (
	"context"
	"fmt"

	"github.com/jinzhu/gorm"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// instanceGroupFieldMap - instance_group 数据库字段映射表
var instanceGroupFieldMap = map[string]string{
	"clusterRole":       "cluster_role",
	"createdAt":         "created_at",
	"instanceGroupID":   "cce_instance_group_id",
	"instanceGroupName": "instance_group_name",
}

// 获取节点组表的属性，传入key命中或者和value匹配则返回
func getInstanceGroupField(fieldName string) string {
	val, ok := instanceGroupFieldMap[fieldName]
	if ok {
		return val
	}

	for _, v := range instanceGroupFieldMap {
		if v == fieldName {
			return fieldName
		}
	}
	return ""
}

// InstanceGroup mapping to t_cce_instancegroup
type InstanceGroup struct {
	BaseModel

	Spec    *ccetypes.InstanceGroupSpec   `json:"spec" gorm:"EMBEDDED"`
	Status  *ccetypes.InstanceGroupStatus `json:"status" gorm:"EMBEDDED"`
	Deleted bool                          `json:"deleted" gorm:"column:deleted"`
}

type InstanceGroupList struct {
	Items      []*InstanceGroup
	TotalCount int
}

type InstanceGroupListOption struct {
	AccountID         string
	ClusterID         string
	Role              ccetypes.ClusterRole
	CAEnabled         *bool
	PageNo            int
	PageSize          int
	TotalCountOnly    bool
	InstanceGroupName string
	InstanceGroupID   string
	OrderBy           string
	Order             string
	ChargingType      *string // 计费方式筛选
}

type updateInstanceGroupCRDFunc func(ctx context.Context, ns, name string, ig *ccev1.InstanceGroup) (*ccev1.InstanceGroup, error)

func (ig *InstanceGroup) TableName() string {
	return "t_cce_instance_group"
}

// CreateInstanceGroups 新建节点组
func (c *Client) CreateInstanceGroups(ctx context.Context, instanceGroups []*InstanceGroup) ([]string, error) {
	var err error
	var cceInstanceGroupIDList []string

	// 开始事务
	tx := c.db.Begin()

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "Error occur during CreateInstanceGroup, rollback transaction")
			tx.Rollback()
		}
	}()

	cceInstanceGroupIDList, err = c.insertInstanceGroups(ctx, instanceGroups, tx)
	if err != nil {
		logger.Infof(ctx, "insertInstanceGroups failed: %s", err)
		return cceInstanceGroupIDList, err
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logger.Errorf(ctx, "DB commit failed: %v", err)
		return cceInstanceGroupIDList, err
	}

	return cceInstanceGroupIDList, nil
}

// GetInstanceGroupByCCEID - 通过 cceInstanceGroupID 获取 InstanceGroup
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceGroupID: instanceGroup ID defined by CCE
//   - accountID: account ID
//
// RETURNS:
//
//	*InstanceGroup: instanceGroup details
//	error: nil if succeed, error if fail
func (c *Client) GetInstanceGroupByCCEID(ctx context.Context, accountID, cceInstanceGroupID string) (*InstanceGroup, error) {
	if cceInstanceGroupID == "" || accountID == "" {
		return nil, fmt.Errorf("cceInstanceGroupID or accountID is empty")
	}

	instanceGroups := make([]*InstanceGroup, 0)

	// 开始事务
	tx := c.db.Begin()

	var err error
	defer func() {
		if err != nil {
			logger.Errorf(ctx, "Error occur during GetInstanceGroupByCCEID, rollback transaction")
			tx.Rollback()
		}
	}()

	if err = tx.Unscoped().Table(c.cceInstanceGroupTableName).
		Where("cce_instance_group_id = ? AND account_id = ? AND deleted = ?", cceInstanceGroupID, accountID, false).
		Find(&instanceGroups).Error; err != nil {
		logger.Errorf(ctx, "SELECT cceInstanceGroupID=%s accountID=%s failed: %v", cceInstanceGroupID, accountID, err)
		return nil, err
	}

	// 提交事务
	err = tx.Commit().Error
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroupByCCEID DB commit failed: %v", err)
		return nil, err
	}

	if len(instanceGroups) == 0 {
		return nil, ErrNotExist.New(ctx, "cceInstanceGroupID=%s not exists", cceInstanceGroupID)
	}

	if len(instanceGroups) > 1 {
		return nil, ErrMultipleExist.New(ctx, "multi cceInstanceGroupID=%s exist", cceInstanceGroupID)
	}

	return instanceGroups[0], nil
}

// GetAllInstanceGroups - 获取所有instanceGroup
func (c *Client) GetAllInstanceGroups(ctx context.Context) ([]*InstanceGroup, error) {
	option := InstanceGroupListOption{}

	list, err := c.GetInstanceGroupsEx(ctx, option)
	return list.Items, err
}

// GetInstanceGroups - 查询 InstanceGroup 列表
func (c *Client) GetInstanceGroups(ctx context.Context, accountID, clusterID string) ([]*InstanceGroup, error) {
	option := InstanceGroupListOption{
		AccountID: accountID,
		ClusterID: clusterID,
	}

	list, err := c.GetInstanceGroupsEx(ctx, option)
	return list.Items, err
}

// GetInstanceGroupsByRole - 查询 InstanceGroup 列表
func (c *Client) GetInstanceGroupsByRole(ctx context.Context, accountID, clusterID string, role ccetypes.ClusterRole) ([]*InstanceGroup, error) {
	option := InstanceGroupListOption{
		AccountID: accountID,
		ClusterID: clusterID,
		Role:      role,
	}

	list, err := c.GetInstanceGroupsEx(ctx, option)
	return list.Items, err
}

// GetInstanceGroupsEx - 支持通过查询选项查询InstanceGroup列表
func (c *Client) GetInstanceGroupsEx(ctx context.Context, option InstanceGroupListOption) (*InstanceGroupList, error) {
	list := InstanceGroupList{
		Items: make([]*InstanceGroup, 0),
	}
	q := c.db.Unscoped().Table(c.cceInstanceGroupTableName).Where("deleted = ?", false)
	if option.AccountID != "" {
		q = q.Where("account_id = ?", option.AccountID)
	}
	if option.ClusterID != "" {
		q = q.Where("cluster_id = ?", option.ClusterID)
	}
	if option.Role != "" {
		q = q.Where("cluster_role = ?", option.Role)
	}
	if option.CAEnabled != nil {
		q = q.Where("enable_autoscaler = ?", option.CAEnabled)
	}
	if option.InstanceGroupID != "" {
		q = q.Where("cce_instance_group_id LIKE ?", "%"+option.InstanceGroupID+"%")
	}
	if option.InstanceGroupName != "" {
		q = q.Where("instance_group_name LIKE ?", "%"+option.InstanceGroupName+"%")
	}

	// 计费方式筛选：支持Prepaid(包年包月)、Postpaid(按量付费)、bid(抢占实例)
	// 计费方式信息存储在instance_template的JSON字段中
	if option.ChargingType != nil && *option.ChargingType != "" {
		q = q.Where("JSON_EXTRACT(instance_template, '$.instanceChargingType') = ?", *option.ChargingType)
	}

	if err := q.Count(&list.TotalCount).Error; err != nil {
		logger.Errorf(ctx, "select count(instanceGroup) failed, option: %s, err: %v", utils.ToJSON(option), err)
		return nil, err
	}
	if option.TotalCountOnly {
		return &list, nil
	}

	// 指定排序方式
	if option.OrderBy != "" && option.Order != "" {
		columnName := getInstanceGroupField(option.OrderBy)
		if columnName == "" {
			return nil, fmt.Errorf("unsupported order field: %s", option.OrderBy)
		}
		q = q.Order(fmt.Sprintf("%s %s", columnName, option.Order))
	}
	if option.PageNo != 0 && option.PageSize != 0 {
		q = q.Offset((option.PageNo - 1) * option.PageSize).Limit(option.PageSize)
	}
	if err := q.Find(&list.Items).Error; err != nil {
		logger.Errorf(ctx, "select instanceGroups failed, option: %s, err: %v", utils.ToJSON(option), err)
		return nil, err
	}
	return &list, nil
}

// DeleteInstanceGroups - 删除 InstanceGroups
func (c *Client) DeleteInstanceGroups(ctx context.Context, accountID, clusterID string, cceInstanceGroupIDs []string) error {
	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if len(cceInstanceGroupIDs) == 0 {
		return nil
	}

	updateMap := map[string]interface{}{
		"deleted": true,
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Where("account_id = ? AND cluster_id = ? AND cce_instance_group_id IN (?)", accountID, clusterID, cceInstanceGroupIDs).
		Update(updateMap).
		Error; err != nil {

		logger.Errorf(ctx, "delete instanceGroup %v failed: %v", cceInstanceGroupIDs, err)
		return err
	}

	return nil
}

func (c *Client) UpdateInstanceGroupReplicas(ctx context.Context, accountID, cceInstanceGroupID string, replicas int) error {
	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}
	if cceInstanceGroupID == "" {
		return fmt.Errorf("cceInstanceGroupID is empty")
	}
	if replicas < 0 {
		return fmt.Errorf("replicas < 0")
	}

	instanceGroup, err := c.GetInstanceGroupByCCEID(ctx, accountID, cceInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroup cceInstanceGroupID=%s failed: %v", cceInstanceGroupID, err)
		return err
	}

	if instanceGroup == nil {
		msg := fmt.Sprintf("instanceGroup %s not exist", cceInstanceGroupID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	updateMap := map[string]interface{}{
		"replicas": replicas,
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Model(instanceGroup).Updates(updateMap).Error; err != nil {
		logger.Errorf(ctx, "update instanceGroup %s replicas failed: %v", err)
		return err
	}

	return nil
}

// UpdateInstanceGroupStatus - 更新 InstanceGroup 数据库中 status
//
// PARAMS:
//   - ctx: The context to trace request
//   - cceInstanceGroupID: instanceGroup ID defined by CCE
//   - accountID: account ID
//   - status: instanceGroup status
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (c *Client) UpdateInstanceGroupStatus(ctx context.Context, accountID string, cceInstanceGroupID string, status *ccetypes.InstanceGroupStatus) error {
	if cceInstanceGroupID == "" {
		return fmt.Errorf("cceInstanceGroupID is emtpy")
	}

	if accountID == "" {
		return fmt.Errorf("account is empty")
	}

	if status == nil {
		logger.Warnf(ctx, "InstanceGroupStatus is nil, skip UpdateInstanceGroupStatus")
		return nil
	}

	instanceGroup, err := c.GetInstanceGroupByCCEID(ctx, accountID, cceInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroup cceInstanceGroupID=%s failed: %v", cceInstanceGroupID, err)
		return err
	}

	if instanceGroup == nil {
		msg := fmt.Sprintf("instanceGroup %s not exist", cceInstanceGroupID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// 目前只update这些spec field
	updateFields := map[string]interface{}{
		"actual_replicas":      status.ActualReplicas,
		"ready_replicas":       status.ReadyReplicas,
		"scaling_replicas":     status.ScalingReplicas,
		"deleting_replicas":    status.DeletingReplicas,
		"other_replicas":       status.OtherReplicas,
		"undelivered_machines": status.UndeliveredMachines,
		"pause":                status.Pause,
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Model(instanceGroup).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instanceGroup %s status failed: %v", err)
		return err
	}

	return nil
}

// UpdateInstanceGroupSpec - 更新InstanceGroup spec
func (c *Client) UpdateInstanceGroupSpec(ctx context.Context, accountID, cceInstanceGroupID string, spec *ccetypes.InstanceGroupSpec) error {
	if cceInstanceGroupID == "" {
		return fmt.Errorf("cceInstanceGroupID is emtpy")
	}

	if accountID == "" {
		return fmt.Errorf("account is empty")
	}

	if spec == nil {
		logger.Warnf(ctx, "InstanceGroupSpec is nil, skip UpdateInstanceGroupSpec")
		return nil
	}

	instanceGroup, err := c.GetInstanceGroupByCCEID(ctx, accountID, cceInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroup cceInstanceGroupID=%s failed: %v", cceInstanceGroupID, err)
		return err
	}

	if instanceGroup == nil {
		msg := fmt.Sprintf("instanceGroup %s not exist", cceInstanceGroupID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	// 目前只update这些spec field
	updateFields := map[string]interface{}{
		"replicas":            spec.Replicas,
		"instance_template":   spec.InstanceTemplate,
		"instance_group_name": spec.InstanceGroupName,
	}
	if spec.ShrinkPolicy != "" {
		updateFields["shrink_policy"] = spec.ShrinkPolicy
	}
	if spec.UpdatePolicy != "" {
		updateFields["update_policy"] = spec.UpdatePolicy
	}
	if spec.CleanPolicy != "" {
		updateFields["clean_policy"] = spec.CleanPolicy
	}
	if spec.ClusterAutoscalerSpec != nil {
		updateFields["enable_autoscaler"] = spec.ClusterAutoscalerSpec.Enabled
		updateFields["autoscaler_min_replicas"] = spec.ClusterAutoscalerSpec.MinReplicas
		updateFields["autoscaler_max_replicas"] = spec.ClusterAutoscalerSpec.MaxReplicas
		updateFields["autoscaler_scaling_group_priority"] = spec.ClusterAutoscalerSpec.ScalingGroupPriority
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Model(instanceGroup).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instanceGroup %s spec failed: %v", cceInstanceGroupID, err)
		return err
	}

	return nil
}

func (c *Client) UpdateInstanceGroupDBAndCRD(ctx context.Context, accountID, cceInstanceGroupID string, instanceGroup *ccev1.InstanceGroup, updateInstanceGroupCRD updateInstanceGroupCRDFunc) error {
	if cceInstanceGroupID == "" {
		return fmt.Errorf("cceInstanceGroupID is empty")
	}

	if accountID == "" {
		return fmt.Errorf("account is empty")
	}

	if instanceGroup == nil {
		logger.Warnf(ctx, "InstanceGroup is nil, skip UpdateInstanceGroupDBAndCRD")
		return nil
	}

	var err error

	// 开始事务
	tx := c.db.Begin()
	defer func() {
		// 回滚事务
		if err != nil {
			logger.Errorf(ctx, "update instanceGroup spec failed, rollback transaction")
			tx.Rollback()
		}
	}()

	instanceGroupModel, err := c.GetInstanceGroupByCCEID(ctx, accountID, cceInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "UpdateInstanceGroupDBAndCRD cceInstanceGroupID=%s failed: %v", cceInstanceGroupID, err)
		return err
	}

	// update instanceGroup DB
	updateFields := map[string]interface{}{
		"handler":               instanceGroup.Spec.Handler,
		"cce_instance_group_id": instanceGroup.Spec.CCEInstanceGroupID,
		"instance_group_name":   instanceGroup.Spec.InstanceGroupName,
		"cluster_id":            instanceGroup.Spec.ClusterID,
		"cluster_role":          instanceGroup.Spec.ClusterRole,
		"user_id":               instanceGroup.Spec.UserID,
		"account_id":            instanceGroup.Spec.AccountID,
		"selector":              instanceGroup.Spec.Selector,
		"replicas":              instanceGroup.Spec.Replicas,
		"instance_template":     instanceGroup.Spec.InstanceTemplate,
		"shrink_policy":         instanceGroup.Spec.ShrinkPolicy,
		"update_policy":         instanceGroup.Spec.UpdatePolicy,
		"clean_policy":          instanceGroup.Spec.CleanPolicy,

		"ready_replicas":       instanceGroup.Status.ReadyReplicas,
		"undelivered_machines": instanceGroup.Status.UndeliveredMachines,
		"pause":                instanceGroup.Status.Pause,
	}

	if instanceGroup.Spec.ClusterAutoscalerSpec != nil {
		updateFields["enable_autoscaler"] = instanceGroup.Spec.ClusterAutoscalerSpec.Enabled
		updateFields["autoscaler_min_replicas"] = instanceGroup.Spec.ClusterAutoscalerSpec.MinReplicas
		updateFields["autoscaler_max_replicas"] = instanceGroup.Spec.ClusterAutoscalerSpec.MaxReplicas
		updateFields["autoscaler_scaling_group_priority"] = instanceGroup.Spec.ClusterAutoscalerSpec.ScalingGroupPriority
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Model(instanceGroupModel).Updates(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instanceGroup %s spec failed: %v", cceInstanceGroupID, err)
		return err
	}

	// update instanceGroup CRD
	if _, err = updateInstanceGroupCRD(ctx, consts.MetaClusterDefaultNamespace, cceInstanceGroupID, instanceGroup); err != nil {
		logger.Errorf(ctx, "update instanceGroup %s spec of crd failed: %v", cceInstanceGroupID, err)
		return err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Errorf(ctx, "tx commit failed: %v", err)
		return err
	}

	return nil
}

func (c *Client) UpdateInstanceGroupAutoscalerSpec(ctx context.Context, accountID, cceInstanceGroupID string, spec *ccetypes.ClusterAutoscalerSpec) error {
	if cceInstanceGroupID == "" {
		return fmt.Errorf("cceInstanceGroupID is empty")
	}

	if accountID == "" {
		return fmt.Errorf("account is empty")
	}

	if spec == nil {
		logger.Warnf(ctx, "ClusterAutoscalerSpec is nil, skip UpdateInstanceGroupAutoscalerSpec")
		return nil
	}

	instanceGroup, err := c.GetInstanceGroupByCCEID(ctx, accountID, cceInstanceGroupID)
	if err != nil {
		logger.Errorf(ctx, "GetInstanceGroup cceInstanceGroupID=%s failed: %v", cceInstanceGroupID, err)
		return err
	}

	if instanceGroup == nil {
		msg := fmt.Sprintf("instanceGroup %s not exist", cceInstanceGroupID)
		logger.Errorf(ctx, msg)
		return fmt.Errorf(msg)
	}

	updateFields := map[string]interface{}{
		"enable_autoscaler":                 spec.Enabled,
		"autoscaler_min_replicas":           spec.MinReplicas,
		"autoscaler_max_replicas":           spec.MaxReplicas,
		"autoscaler_scaling_group_priority": spec.ScalingGroupPriority,
	}

	if err := c.db.Unscoped().Table(c.cceInstanceGroupTableName).
		Model(instanceGroup).Update(updateFields).Error; err != nil {
		logger.Errorf(ctx, "update instanceGroup %s spec failed: %v", cceInstanceGroupID, err)
		return err
	}
	return nil
}

// insertInstanceGroups - 新增 InstanceGroup 实例
//
// PARAMS:
//   - ctx: The context to trace request
//   - instanceGroupList: []*InstanceGroup
//   - db: db *gorm.DB
//
// RETURNS:
//
//	[]string: 返回 InstanceGroup 的 cceInstanceGroupID
//	error: nil if succeed, error if fail
func (c *Client) insertInstanceGroups(ctx context.Context, instanceGroupList []*InstanceGroup, db *gorm.DB) ([]string, error) {
	cceInstanceGroupIDList := make([]string, 0)

	if db == nil {
		return cceInstanceGroupIDList, fmt.Errorf("gorm.DB is nil")
	}

	if len(instanceGroupList) == 0 {
		logger.Infof(ctx, "instanceGroupList is empty")
		return cceInstanceGroupIDList, nil
	}

	for _, ig := range instanceGroupList {
		instanceGroup := ig

		if instanceGroup == nil || instanceGroup.Spec == nil {
			continue
		}

		if instanceGroup.Spec.ClusterID == "" {
			return cceInstanceGroupIDList, fmt.Errorf("clusterID can not be empty")
		}
		if instanceGroup.Spec.AccountID == "" {
			return cceInstanceGroupIDList, fmt.Errorf("accountID can not be empty")
		}

		var cceInstanceGroupID string
		if ig.Spec.CCEInstanceGroupID != "" {
			cceInstanceGroupID = ig.Spec.CCEInstanceGroupID
		} else {
			generatedCCeInstanceGroupID, err := c.generateCCEInstanceGroupID(ctx, db, ccetypes.InstanceGroupIDPrefix)
			if err != nil {
				logger.Errorf(ctx, "GenerateInstanceUUID failed: %v", err)
				return cceInstanceGroupIDList, err
			}
			if generatedCCeInstanceGroupID == "" {
				return cceInstanceGroupIDList, fmt.Errorf("GenerateCCEInstanceID return cceInstanceGroupID empty")
			}
			cceInstanceGroupID = generatedCCeInstanceGroupID
		}

		instanceGroup.Spec.CCEInstanceGroupID = cceInstanceGroupID
		if instanceGroup.Spec.InstanceGroupName == "" {
			instanceGroup.Spec.InstanceGroupName = cceInstanceGroupID
		}
		if instanceGroup.Spec.InstanceTemplate.Labels == nil {
			instanceGroup.Spec.InstanceTemplate.Labels = map[string]string{}
		}
		instanceGroup.Spec.InstanceTemplate.Labels[ccetypes.ClusterIDLabelKey] = instanceGroup.Spec.ClusterID
		instanceGroup.Spec.InstanceTemplate.Labels[ccetypes.InstanceGroupIDLabelKey] = cceInstanceGroupID
		instanceGroup.Spec.InstanceTemplate.Labels[ccetypes.ClusterRoleLabelKey] = string(instanceGroup.Spec.ClusterRole)
		if instanceGroup.Spec.ClusterAutoscalerSpec != nil && instanceGroup.Spec.ClusterAutoscalerSpec.Enabled {
			instanceGroup.Spec.InstanceTemplate.Labels[ccetypes.ClusterAutoscalerEnabledLabelKey] = ""
		}
		instanceGroup.Spec.InstanceTemplate.InstanceGroupID = cceInstanceGroupID
		instanceGroup.Spec.InstanceTemplate.InstanceGroupName = instanceGroup.Spec.InstanceGroupName
		instanceGroup.Spec.Selector = &ccetypes.InstanceSelector{
			LabelSelector: metav1.LabelSelector{
				MatchLabels: map[string]string{
					ccetypes.InstanceGroupIDLabelKey: cceInstanceGroupID,
				},
			},
		}

		if instanceGroup.Spec.InstanceTemplate.Annotations == nil {
			instanceGroup.Spec.InstanceTemplate.Annotations = map[string]string{}
		}

		if err := db.Table(c.cceInstanceGroupTableName).Create(instanceGroup).Error; err != nil {
			logger.Errorf(ctx, "instanceGroup %v in DB failed: %v", utils.ToJSON(instanceGroup), err)
			return cceInstanceGroupIDList, err
		}

		cceInstanceGroupIDList = append(cceInstanceGroupIDList, cceInstanceGroupID)
	}
	return cceInstanceGroupIDList, nil
}
