// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	gorm "github.com/jinzhu/gorm"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	validate "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/gotag/validate"
	models "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	v10 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// MockInterface is a mock of Interface interface.
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface.
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance.
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// AddCaCert mocks base method.
func (m *MockInterface) AddCaCert(arg0 context.Context, arg1 *models.CaCert) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCaCert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCaCert indicates an expected call of AddCaCert.
func (mr *MockInterfaceMockRecorder) AddCaCert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCaCert", reflect.TypeOf((*MockInterface)(nil).AddCaCert), arg0, arg1)
}

// AddClusterReport mocks base method.
func (m *MockInterface) AddClusterReport(arg0 context.Context, arg1 *models.ClusterReport) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddClusterReport", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddClusterReport indicates an expected call of AddClusterReport.
func (mr *MockInterfaceMockRecorder) AddClusterReport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddClusterReport", reflect.TypeOf((*MockInterface)(nil).AddClusterReport), arg0, arg1)
}

// AddClusterSecurity mocks base method.
func (m *MockInterface) AddClusterSecurity(arg0 context.Context, arg1 *models.ClusterSecurity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddClusterSecurity", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddClusterSecurity indicates an expected call of AddClusterSecurity.
func (mr *MockInterfaceMockRecorder) AddClusterSecurity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddClusterSecurity", reflect.TypeOf((*MockInterface)(nil).AddClusterSecurity), arg0, arg1)
}

// AddK8SEventCluster mocks base method.
func (m *MockInterface) AddK8SEventCluster(arg0 context.Context, arg1 *models.K8SEventCluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddK8SEventCluster", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddK8SEventCluster indicates an expected call of AddK8SEventCluster.
func (mr *MockInterfaceMockRecorder) AddK8SEventCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddK8SEventCluster", reflect.TypeOf((*MockInterface)(nil).AddK8SEventCluster), arg0, arg1)
}

// AddKubeConfig mocks base method.
func (m *MockInterface) AddKubeConfig(arg0 context.Context, arg1 *models.KubeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddKubeConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddKubeConfig indicates an expected call of AddKubeConfig.
func (mr *MockInterfaceMockRecorder) AddKubeConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddKubeConfig", reflect.TypeOf((*MockInterface)(nil).AddKubeConfig), arg0, arg1)
}

// CreateAutoscaler mocks base method.
func (m *MockInterface) CreateAutoscaler(arg0 context.Context, arg1 *models.Autoscaler, arg2 models.deployPluginFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAutoscaler", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAutoscaler indicates an expected call of CreateAutoscaler.
func (mr *MockInterfaceMockRecorder) CreateAutoscaler(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAutoscaler", reflect.TypeOf((*MockInterface)(nil).CreateAutoscaler), arg0, arg1, arg2)
}

// CreateCluster mocks base method.
func (m *MockInterface) CreateCluster(arg0 context.Context, arg1 *models.Cluster, arg2, arg3 []*models.Instance, arg4 models.CallbackBeforeCreate) (string, []string, []string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCluster", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].([]string)
	ret2, _ := ret[2].([]string)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// CreateCluster indicates an expected call of CreateCluster.
func (mr *MockInterfaceMockRecorder) CreateCluster(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCluster", reflect.TypeOf((*MockInterface)(nil).CreateCluster), arg0, arg1, arg2, arg3, arg4)
}

// CreateDuplicateCluster mocks base method.
func (m *MockInterface) CreateDuplicateCluster(arg0 context.Context, arg1 *models.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDuplicateCluster", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDuplicateCluster indicates an expected call of CreateDuplicateCluster.
func (mr *MockInterfaceMockRecorder) CreateDuplicateCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDuplicateCluster", reflect.TypeOf((*MockInterface)(nil).CreateDuplicateCluster), arg0, arg1)
}

// CreateInstanceGroupReplicasTask mocks base method.
func (m *MockInterface) CreateInstanceGroupReplicasTask(arg0 context.Context, arg1 *models.InstanceGroupReplicasTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroupReplicasTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstanceGroupReplicasTask indicates an expected call of CreateInstanceGroupReplicasTask.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroupReplicasTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroupReplicasTask", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroupReplicasTask), arg0, arg1)
}

// CreateInstanceGroupSyncMetaTask mocks base method.
func (m *MockInterface) CreateInstanceGroupSyncMetaTask(arg0 context.Context, arg1 *models.InstanceGroupSyncMetaTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroupSyncMetaTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstanceGroupSyncMetaTask indicates an expected call of CreateInstanceGroupSyncMetaTask.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroupSyncMetaTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroupSyncMetaTask", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroupSyncMetaTask), arg0, arg1)
}

// CreateInstanceGroups mocks base method.
func (m *MockInterface) CreateInstanceGroups(arg0 context.Context, arg1 []*models.InstanceGroup) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceGroups", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstanceGroups indicates an expected call of CreateInstanceGroups.
func (mr *MockInterfaceMockRecorder) CreateInstanceGroups(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceGroups", reflect.TypeOf((*MockInterface)(nil).CreateInstanceGroups), arg0, arg1)
}

// CreateInstances mocks base method.
func (m *MockInterface) CreateInstances(arg0 context.Context, arg1 string, arg2 []*models.Instance) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInstances indicates an expected call of CreateInstances.
func (mr *MockInterfaceMockRecorder) CreateInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstances", reflect.TypeOf((*MockInterface)(nil).CreateInstances), arg0, arg1, arg2)
}

// CreateStatistics mocks base method.
func (m *MockInterface) CreateStatistics(arg0 context.Context, arg1 *models.Statistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStatistics", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateStatistics indicates an expected call of CreateStatistics.
func (mr *MockInterfaceMockRecorder) CreateStatistics(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStatistics", reflect.TypeOf((*MockInterface)(nil).CreateStatistics), arg0, arg1)
}

// CreateTestResult mocks base method.
func (m *MockInterface) CreateTestResult(arg0 context.Context, arg1 *models.TestResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTestResult", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTestResult indicates an expected call of CreateTestResult.
func (mr *MockInterfaceMockRecorder) CreateTestResult(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTestResult", reflect.TypeOf((*MockInterface)(nil).CreateTestResult), arg0, arg1)
}

// CreateToken mocks base method.
func (m *MockInterface) CreateToken(arg0 context.Context, arg1 *models.Token) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateToken", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateToken indicates an expected call of CreateToken.
func (mr *MockInterfaceMockRecorder) CreateToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateToken", reflect.TypeOf((*MockInterface)(nil).CreateToken), arg0, arg1)
}

// CreateUserScript mocks base method.
func (m *MockInterface) CreateUserScript(arg0 context.Context, arg1 *models.UserScript) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserScript", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUserScript indicates an expected call of CreateUserScript.
func (mr *MockInterfaceMockRecorder) CreateUserScript(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserScript", reflect.TypeOf((*MockInterface)(nil).CreateUserScript), arg0, arg1)
}

// CreateWorkflow mocks base method.
func (m *MockInterface) CreateWorkflow(arg0 context.Context, arg1 *models.Workflow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWorkflow indicates an expected call of CreateWorkflow.
func (mr *MockInterfaceMockRecorder) CreateWorkflow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflow", reflect.TypeOf((*MockInterface)(nil).CreateWorkflow), arg0, arg1)
}

// DB mocks base method.
func (m *MockInterface) DB(arg0 context.Context) (*gorm.DB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DB", arg0)
	ret0, _ := ret[0].(*gorm.DB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DB indicates an expected call of DB.
func (mr *MockInterfaceMockRecorder) DB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DB", reflect.TypeOf((*MockInterface)(nil).DB), arg0)
}

// DeleteCaaSCluster mocks base method.
func (m *MockInterface) DeleteCaaSCluster(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCaaSCluster", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCaaSCluster indicates an expected call of DeleteCaaSCluster.
func (mr *MockInterfaceMockRecorder) DeleteCaaSCluster(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCaaSCluster", reflect.TypeOf((*MockInterface)(nil).DeleteCaaSCluster), arg0, arg1, arg2)
}

// DeleteCluster mocks base method.
func (m *MockInterface) DeleteCluster(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.DeleteOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCluster", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCluster indicates an expected call of DeleteCluster.
func (mr *MockInterfaceMockRecorder) DeleteCluster(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCluster", reflect.TypeOf((*MockInterface)(nil).DeleteCluster), arg0, arg1, arg2, arg3)
}

// DeleteClusterReportsByClusterID mocks base method.
func (m *MockInterface) DeleteClusterReportsByClusterID(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterReportsByClusterID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterReportsByClusterID indicates an expected call of DeleteClusterReportsByClusterID.
func (mr *MockInterfaceMockRecorder) DeleteClusterReportsByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterReportsByClusterID", reflect.TypeOf((*MockInterface)(nil).DeleteClusterReportsByClusterID), arg0, arg1)
}

// DeleteClusterReportsByDate mocks base method.
func (m *MockInterface) DeleteClusterReportsByDate(arg0 context.Context, arg1 time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterReportsByDate", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterReportsByDate indicates an expected call of DeleteClusterReportsByDate.
func (mr *MockInterfaceMockRecorder) DeleteClusterReportsByDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterReportsByDate", reflect.TypeOf((*MockInterface)(nil).DeleteClusterReportsByDate), arg0, arg1)
}

// DeleteClusterSecurityByClusterID mocks base method.
func (m *MockInterface) DeleteClusterSecurityByClusterID(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteClusterSecurityByClusterID", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteClusterSecurityByClusterID indicates an expected call of DeleteClusterSecurityByClusterID.
func (mr *MockInterfaceMockRecorder) DeleteClusterSecurityByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteClusterSecurityByClusterID", reflect.TypeOf((*MockInterface)(nil).DeleteClusterSecurityByClusterID), arg0, arg1)
}

// DeleteInstanceGroups mocks base method.
func (m *MockInterface) DeleteInstanceGroups(arg0 context.Context, arg1, arg2 string, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteInstanceGroups", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteInstanceGroups indicates an expected call of DeleteInstanceGroups.
func (mr *MockInterfaceMockRecorder) DeleteInstanceGroups(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteInstanceGroups", reflect.TypeOf((*MockInterface)(nil).DeleteInstanceGroups), arg0, arg1, arg2, arg3)
}

// DeleteK8SEventCluster mocks base method.
func (m *MockInterface) DeleteK8SEventCluster(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteK8SEventCluster", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteK8SEventCluster indicates an expected call of DeleteK8SEventCluster.
func (mr *MockInterfaceMockRecorder) DeleteK8SEventCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteK8SEventCluster", reflect.TypeOf((*MockInterface)(nil).DeleteK8SEventCluster), arg0, arg1)
}

// DeleteServiceCluster mocks base method.
func (m *MockInterface) DeleteServiceCluster(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceCluster", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceCluster indicates an expected call of DeleteServiceCluster.
func (mr *MockInterfaceMockRecorder) DeleteServiceCluster(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceCluster", reflect.TypeOf((*MockInterface)(nil).DeleteServiceCluster), arg0, arg1, arg2)
}

// DeleteServiceInstance mocks base method.
func (m *MockInterface) DeleteServiceInstance(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceInstance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceInstance indicates an expected call of DeleteServiceInstance.
func (mr *MockInterfaceMockRecorder) DeleteServiceInstance(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceInstance", reflect.TypeOf((*MockInterface)(nil).DeleteServiceInstance), arg0, arg1, arg2, arg3)
}

// DeleteWorkflow mocks base method.
func (m *MockInterface) DeleteWorkflow(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkflow", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkflow indicates an expected call of DeleteWorkflow.
func (mr *MockInterfaceMockRecorder) DeleteWorkflow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkflow", reflect.TypeOf((*MockInterface)(nil).DeleteWorkflow), arg0, arg1)
}

// GenerateClusterID mocks base method.
func (m *MockInterface) GenerateClusterID(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateClusterID", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateClusterID indicates an expected call of GenerateClusterID.
func (mr *MockInterfaceMockRecorder) GenerateClusterID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateClusterID", reflect.TypeOf((*MockInterface)(nil).GenerateClusterID), arg0)
}

// GenerateInstanceID mocks base method.
func (m *MockInterface) GenerateInstanceID(arg0 context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateInstanceID", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateInstanceID indicates an expected call of GenerateInstanceID.
func (mr *MockInterfaceMockRecorder) GenerateInstanceID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateInstanceID", reflect.TypeOf((*MockInterface)(nil).GenerateInstanceID), arg0)
}

// GetAdminKubeConfig mocks base method.
func (m *MockInterface) GetAdminKubeConfig(arg0 context.Context, arg1 string, arg2 models.KubeConfigType) (*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfig indicates an expected call of GetAdminKubeConfig.
func (mr *MockInterfaceMockRecorder) GetAdminKubeConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfig", reflect.TypeOf((*MockInterface)(nil).GetAdminKubeConfig), arg0, arg1, arg2)
}

// GetAdminKubeConfigCompatibility mocks base method.
func (m *MockInterface) GetAdminKubeConfigCompatibility(arg0 context.Context, arg1 string, arg2 models.KubeConfigType) (*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAdminKubeConfigCompatibility", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAdminKubeConfigCompatibility indicates an expected call of GetAdminKubeConfigCompatibility.
func (mr *MockInterfaceMockRecorder) GetAdminKubeConfigCompatibility(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAdminKubeConfigCompatibility", reflect.TypeOf((*MockInterface)(nil).GetAdminKubeConfigCompatibility), arg0, arg1, arg2)
}

// GetAllClusterBrief mocks base method.
func (m *MockInterface) GetAllClusterBrief(arg0 context.Context) ([]*models.ClusterBrief, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClusterBrief", arg0)
	ret0, _ := ret[0].([]*models.ClusterBrief)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClusterBrief indicates an expected call of GetAllClusterBrief.
func (mr *MockInterfaceMockRecorder) GetAllClusterBrief(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClusterBrief", reflect.TypeOf((*MockInterface)(nil).GetAllClusterBrief), arg0)
}

// GetAllClustersID mocks base method.
func (m *MockInterface) GetAllClustersID(arg0 context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllClustersID", arg0)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllClustersID indicates an expected call of GetAllClustersID.
func (mr *MockInterfaceMockRecorder) GetAllClustersID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllClustersID", reflect.TypeOf((*MockInterface)(nil).GetAllClustersID), arg0)
}

// GetAllInstanceGroups mocks base method.
func (m *MockInterface) GetAllInstanceGroups(arg0 context.Context) ([]*models.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllInstanceGroups", arg0)
	ret0, _ := ret[0].([]*models.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllInstanceGroups indicates an expected call of GetAllInstanceGroups.
func (mr *MockInterfaceMockRecorder) GetAllInstanceGroups(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllInstanceGroups", reflect.TypeOf((*MockInterface)(nil).GetAllInstanceGroups), arg0)
}

// GetAllInstances mocks base method.
func (m *MockInterface) GetAllInstances(arg0 context.Context) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllInstances", arg0)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllInstances indicates an expected call of GetAllInstances.
func (mr *MockInterfaceMockRecorder) GetAllInstances(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllInstances", reflect.TypeOf((*MockInterface)(nil).GetAllInstances), arg0)
}

// GetAllRunningClusterIDsCompatibility mocks base method.
func (m *MockInterface) GetAllRunningClusterIDsCompatibility(arg0 context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRunningClusterIDsCompatibility", arg0)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRunningClusterIDsCompatibility indicates an expected call of GetAllRunningClusterIDsCompatibility.
func (mr *MockInterfaceMockRecorder) GetAllRunningClusterIDsCompatibility(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRunningClusterIDsCompatibility", reflect.TypeOf((*MockInterface)(nil).GetAllRunningClusterIDsCompatibility), arg0)
}

// GetAuditStateByClusterID mocks base method.
func (m *MockInterface) GetAuditStateByClusterID(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuditStateByClusterID", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuditStateByClusterID indicates an expected call of GetAuditStateByClusterID.
func (mr *MockInterfaceMockRecorder) GetAuditStateByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuditStateByClusterID", reflect.TypeOf((*MockInterface)(nil).GetAuditStateByClusterID), arg0, arg1)
}

// GetAutoscaler mocks base method.
func (m *MockInterface) GetAutoscaler(arg0 context.Context, arg1, arg2 string) (*models.Autoscaler, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoscaler", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Autoscaler)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAutoscaler indicates an expected call of GetAutoscaler.
func (mr *MockInterfaceMockRecorder) GetAutoscaler(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoscaler", reflect.TypeOf((*MockInterface)(nil).GetAutoscaler), arg0, arg1, arg2)
}

// GetBackupList mocks base method.
func (m *MockInterface) GetBackupList(arg0 context.Context, arg1 string, arg2 *models.BackupListOption) (*models.BackupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBackupList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.BackupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBackupList indicates an expected call of GetBackupList.
func (mr *MockInterfaceMockRecorder) GetBackupList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBackupList", reflect.TypeOf((*MockInterface)(nil).GetBackupList), arg0, arg1, arg2)
}

// GetCaCert mocks base method.
func (m *MockInterface) GetCaCert(arg0 context.Context, arg1 string) (*models.CaCert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCaCert", arg0, arg1)
	ret0, _ := ret[0].(*models.CaCert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCaCert indicates an expected call of GetCaCert.
func (mr *MockInterfaceMockRecorder) GetCaCert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCaCert", reflect.TypeOf((*MockInterface)(nil).GetCaCert), arg0, arg1)
}

// GetCaaSCluster mocks base method.
func (m *MockInterface) GetCaaSCluster(arg0 context.Context, arg1 string) (*models.CaaSCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCaaSCluster", arg0, arg1)
	ret0, _ := ret[0].(*models.CaaSCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCaaSCluster indicates an expected call of GetCaaSCluster.
func (mr *MockInterfaceMockRecorder) GetCaaSCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCaaSCluster", reflect.TypeOf((*MockInterface)(nil).GetCaaSCluster), arg0, arg1)
}

// GetCaaSClusterWithDeleted mocks base method.
func (m *MockInterface) GetCaaSClusterWithDeleted(arg0 context.Context, arg1 string) (*models.CaaSCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCaaSClusterWithDeleted", arg0, arg1)
	ret0, _ := ret[0].(*models.CaaSCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCaaSClusterWithDeleted indicates an expected call of GetCaaSClusterWithDeleted.
func (mr *MockInterfaceMockRecorder) GetCaaSClusterWithDeleted(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCaaSClusterWithDeleted", reflect.TypeOf((*MockInterface)(nil).GetCaaSClusterWithDeleted), arg0, arg1)
}

// GetCluster mocks base method.
func (m *MockInterface) GetCluster(arg0 context.Context, arg1, arg2 string) (*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCluster", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCluster indicates an expected call of GetCluster.
func (mr *MockInterfaceMockRecorder) GetCluster(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCluster", reflect.TypeOf((*MockInterface)(nil).GetCluster), arg0, arg1, arg2)
}

// GetClusterByClusterID mocks base method.
func (m *MockInterface) GetClusterByClusterID(arg0 context.Context, arg1 string) (*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterByClusterID", arg0, arg1)
	ret0, _ := ret[0].(*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterByClusterID indicates an expected call of GetClusterByClusterID.
func (mr *MockInterfaceMockRecorder) GetClusterByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterByClusterID", reflect.TypeOf((*MockInterface)(nil).GetClusterByClusterID), arg0, arg1)
}

// GetClusterByClusterIDWithDeleted mocks base method.
func (m *MockInterface) GetClusterByClusterIDWithDeleted(arg0 context.Context, arg1 string) (*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterByClusterIDWithDeleted", arg0, arg1)
	ret0, _ := ret[0].(*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterByClusterIDWithDeleted indicates an expected call of GetClusterByClusterIDWithDeleted.
func (mr *MockInterfaceMockRecorder) GetClusterByClusterIDWithDeleted(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterByClusterIDWithDeleted", reflect.TypeOf((*MockInterface)(nil).GetClusterByClusterIDWithDeleted), arg0, arg1)
}

// GetClusterK8SVersionCompatibility mocks base method.
func (m *MockInterface) GetClusterK8SVersionCompatibility(arg0 context.Context, arg1 string) (ccetypes.K8SVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterK8SVersionCompatibility", arg0, arg1)
	ret0, _ := ret[0].(ccetypes.K8SVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterK8SVersionCompatibility indicates an expected call of GetClusterK8SVersionCompatibility.
func (mr *MockInterfaceMockRecorder) GetClusterK8SVersionCompatibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterK8SVersionCompatibility", reflect.TypeOf((*MockInterface)(nil).GetClusterK8SVersionCompatibility), arg0, arg1)
}

// GetClusterList mocks base method.
func (m *MockInterface) GetClusterList(arg0 context.Context, arg1 string) ([]*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterList", arg0, arg1)
	ret0, _ := ret[0].([]*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterList indicates an expected call of GetClusterList.
func (mr *MockInterfaceMockRecorder) GetClusterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterList", reflect.TypeOf((*MockInterface)(nil).GetClusterList), arg0, arg1)
}

// GetClusterNum mocks base method.
func (m *MockInterface) GetClusterNum(arg0 context.Context, arg1, arg2, arg3 string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterNum", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterNum indicates an expected call of GetClusterNum.
func (mr *MockInterfaceMockRecorder) GetClusterNum(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterNum", reflect.TypeOf((*MockInterface)(nil).GetClusterNum), arg0, arg1, arg2, arg3)
}

// GetClusterReportsByClusterID mocks base method.
func (m *MockInterface) GetClusterReportsByClusterID(arg0 context.Context, arg1 string) ([]*models.ClusterReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterReportsByClusterID", arg0, arg1)
	ret0, _ := ret[0].([]*models.ClusterReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterReportsByClusterID indicates an expected call of GetClusterReportsByClusterID.
func (mr *MockInterfaceMockRecorder) GetClusterReportsByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterReportsByClusterID", reflect.TypeOf((*MockInterface)(nil).GetClusterReportsByClusterID), arg0, arg1)
}

// GetClusterReportsByDate mocks base method.
func (m *MockInterface) GetClusterReportsByDate(arg0 context.Context, arg1 string, arg2 time.Time) ([]*models.ClusterReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterReportsByDate", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.ClusterReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterReportsByDate indicates an expected call of GetClusterReportsByDate.
func (mr *MockInterfaceMockRecorder) GetClusterReportsByDate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterReportsByDate", reflect.TypeOf((*MockInterface)(nil).GetClusterReportsByDate), arg0, arg1, arg2)
}

// GetClusterReportsByResourceType mocks base method.
func (m *MockInterface) GetClusterReportsByResourceType(arg0 context.Context, arg1 string, arg2 models.ReportResourceType) ([]*models.ClusterReport, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterReportsByResourceType", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.ClusterReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterReportsByResourceType indicates an expected call of GetClusterReportsByResourceType.
func (mr *MockInterfaceMockRecorder) GetClusterReportsByResourceType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterReportsByResourceType", reflect.TypeOf((*MockInterface)(nil).GetClusterReportsByResourceType), arg0, arg1, arg2)
}

// GetClusterSecurityByClusterID mocks base method.
func (m *MockInterface) GetClusterSecurityByClusterID(arg0 context.Context, arg1 string) (*models.ClusterSecurity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterSecurityByClusterID", arg0, arg1)
	ret0, _ := ret[0].(*models.ClusterSecurity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterSecurityByClusterID indicates an expected call of GetClusterSecurityByClusterID.
func (mr *MockInterfaceMockRecorder) GetClusterSecurityByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterSecurityByClusterID", reflect.TypeOf((*MockInterface)(nil).GetClusterSecurityByClusterID), arg0, arg1)
}

// GetClusterSubnets mocks base method.
func (m *MockInterface) GetClusterSubnets(arg0 context.Context, arg1, arg2 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterSubnets", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterSubnets indicates an expected call of GetClusterSubnets.
func (mr *MockInterfaceMockRecorder) GetClusterSubnets(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterSubnets", reflect.TypeOf((*MockInterface)(nil).GetClusterSubnets), arg0, arg1, arg2)
}

// GetClustersByBatchQuery mocks base method.
func (m *MockInterface) GetClustersByBatchQuery(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7, arg8 int) ([]*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersByBatchQuery", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].([]*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersByBatchQuery indicates an expected call of GetClustersByBatchQuery.
func (mr *MockInterfaceMockRecorder) GetClustersByBatchQuery(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersByBatchQuery", reflect.TypeOf((*MockInterface)(nil).GetClustersByBatchQuery), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// GetClustersByPage mocks base method.
func (m *MockInterface) GetClustersByPage(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7, arg8 int) ([]*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersByPage", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].([]*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersByPage indicates an expected call of GetClustersByPage.
func (mr *MockInterfaceMockRecorder) GetClustersByPage(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersByPage", reflect.TypeOf((*MockInterface)(nil).GetClustersByPage), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// GetClustersByVPC mocks base method.
func (m *MockInterface) GetClustersByVPC(arg0 context.Context, arg1, arg2 string) ([]*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClustersByVPC", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClustersByVPC indicates an expected call of GetClustersByVPC.
func (mr *MockInterfaceMockRecorder) GetClustersByVPC(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClustersByVPC", reflect.TypeOf((*MockInterface)(nil).GetClustersByVPC), arg0, arg1, arg2)
}

// GetInstanceByBCCID mocks base method.
func (m *MockInterface) GetInstanceByBCCID(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByBCCID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByBCCID indicates an expected call of GetInstanceByBCCID.
func (mr *MockInterfaceMockRecorder) GetInstanceByBCCID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByBCCID", reflect.TypeOf((*MockInterface)(nil).GetInstanceByBCCID), arg0, arg1, arg2)
}

// GetInstanceByCCEID mocks base method.
func (m *MockInterface) GetInstanceByCCEID(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByCCEID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByCCEID indicates an expected call of GetInstanceByCCEID.
func (mr *MockInterfaceMockRecorder) GetInstanceByCCEID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByCCEID", reflect.TypeOf((*MockInterface)(nil).GetInstanceByCCEID), arg0, arg1, arg2)
}

// GetInstanceByCCEIDWithDeleted mocks base method.
func (m *MockInterface) GetInstanceByCCEIDWithDeleted(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByCCEIDWithDeleted", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByCCEIDWithDeleted indicates an expected call of GetInstanceByCCEIDWithDeleted.
func (mr *MockInterfaceMockRecorder) GetInstanceByCCEIDWithDeleted(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByCCEIDWithDeleted", reflect.TypeOf((*MockInterface)(nil).GetInstanceByCCEIDWithDeleted), arg0, arg1, arg2)
}

// GetInstanceByCCEIDWithoutAccount mocks base method.
func (m *MockInterface) GetInstanceByCCEIDWithoutAccount(arg0 context.Context, arg1 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByCCEIDWithoutAccount", arg0, arg1)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByCCEIDWithoutAccount indicates an expected call of GetInstanceByCCEIDWithoutAccount.
func (mr *MockInterfaceMockRecorder) GetInstanceByCCEIDWithoutAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByCCEIDWithoutAccount", reflect.TypeOf((*MockInterface)(nil).GetInstanceByCCEIDWithoutAccount), arg0, arg1)
}

// GetInstanceByClusterIDAndHostname mocks base method.
func (m *MockInterface) GetInstanceByClusterIDAndHostname(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByClusterIDAndHostname", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByClusterIDAndHostname indicates an expected call of GetInstanceByClusterIDAndHostname.
func (mr *MockInterfaceMockRecorder) GetInstanceByClusterIDAndHostname(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByClusterIDAndHostname", reflect.TypeOf((*MockInterface)(nil).GetInstanceByClusterIDAndHostname), arg0, arg1, arg2)
}

// GetInstanceByClusterIDAndInstanceGroupName mocks base method.
func (m *MockInterface) GetInstanceByClusterIDAndInstanceGroupName(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByClusterIDAndInstanceGroupName", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByClusterIDAndInstanceGroupName indicates an expected call of GetInstanceByClusterIDAndInstanceGroupName.
func (mr *MockInterfaceMockRecorder) GetInstanceByClusterIDAndInstanceGroupName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByClusterIDAndInstanceGroupName", reflect.TypeOf((*MockInterface)(nil).GetInstanceByClusterIDAndInstanceGroupName), arg0, arg1, arg2)
}

// GetInstanceByClusterIDAndInstanceName mocks base method.
func (m *MockInterface) GetInstanceByClusterIDAndInstanceName(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByClusterIDAndInstanceName", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByClusterIDAndInstanceName indicates an expected call of GetInstanceByClusterIDAndInstanceName.
func (mr *MockInterfaceMockRecorder) GetInstanceByClusterIDAndInstanceName(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByClusterIDAndInstanceName", reflect.TypeOf((*MockInterface)(nil).GetInstanceByClusterIDAndInstanceName), arg0, arg1, arg2)
}

// GetInstanceByClusterIDAndVPCIP mocks base method.
func (m *MockInterface) GetInstanceByClusterIDAndVPCIP(arg0 context.Context, arg1, arg2 string) (*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceByClusterIDAndVPCIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceByClusterIDAndVPCIP indicates an expected call of GetInstanceByClusterIDAndVPCIP.
func (mr *MockInterfaceMockRecorder) GetInstanceByClusterIDAndVPCIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceByClusterIDAndVPCIP", reflect.TypeOf((*MockInterface)(nil).GetInstanceByClusterIDAndVPCIP), arg0, arg1, arg2)
}

// GetInstanceEx mocks base method.
func (m *MockInterface) GetInstanceEx(arg0 context.Context, arg1 models.InstanceListOption) (*models.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceEx", arg0, arg1)
	ret0, _ := ret[0].(*models.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceEx indicates an expected call of GetInstanceEx.
func (mr *MockInterfaceMockRecorder) GetInstanceEx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceEx", reflect.TypeOf((*MockInterface)(nil).GetInstanceEx), arg0, arg1)
}

// GetInstanceGroupByCCEID mocks base method.
func (m *MockInterface) GetInstanceGroupByCCEID(arg0 context.Context, arg1, arg2 string) (*models.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroupByCCEID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroupByCCEID indicates an expected call of GetInstanceGroupByCCEID.
func (mr *MockInterfaceMockRecorder) GetInstanceGroupByCCEID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroupByCCEID", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroupByCCEID), arg0, arg1, arg2)
}

// GetInstanceGroupReplicasTaskByTaskID mocks base method.
func (m *MockInterface) GetInstanceGroupReplicasTaskByTaskID(arg0 context.Context, arg1 string) (*models.InstanceGroupReplicasTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroupReplicasTaskByTaskID", arg0, arg1)
	ret0, _ := ret[0].(*models.InstanceGroupReplicasTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroupReplicasTaskByTaskID indicates an expected call of GetInstanceGroupReplicasTaskByTaskID.
func (mr *MockInterfaceMockRecorder) GetInstanceGroupReplicasTaskByTaskID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroupReplicasTaskByTaskID", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroupReplicasTaskByTaskID), arg0, arg1)
}

// GetInstanceGroupSyncMetaTaskByTaskID mocks base method.
func (m *MockInterface) GetInstanceGroupSyncMetaTaskByTaskID(arg0 context.Context, arg1 string) (*models.InstanceGroupSyncMetaTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroupSyncMetaTaskByTaskID", arg0, arg1)
	ret0, _ := ret[0].(*models.InstanceGroupSyncMetaTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroupSyncMetaTaskByTaskID indicates an expected call of GetInstanceGroupSyncMetaTaskByTaskID.
func (mr *MockInterfaceMockRecorder) GetInstanceGroupSyncMetaTaskByTaskID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroupSyncMetaTaskByTaskID", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroupSyncMetaTaskByTaskID), arg0, arg1)
}

// GetInstanceGroups mocks base method.
func (m *MockInterface) GetInstanceGroups(arg0 context.Context, arg1, arg2 string) ([]*models.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroups", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroups indicates an expected call of GetInstanceGroups.
func (mr *MockInterfaceMockRecorder) GetInstanceGroups(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroups", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroups), arg0, arg1, arg2)
}

// GetInstanceGroupsByRole mocks base method.
func (m *MockInterface) GetInstanceGroupsByRole(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterRole) ([]*models.InstanceGroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroupsByRole", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.InstanceGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroupsByRole indicates an expected call of GetInstanceGroupsByRole.
func (mr *MockInterfaceMockRecorder) GetInstanceGroupsByRole(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroupsByRole", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroupsByRole), arg0, arg1, arg2, arg3)
}

// GetInstanceGroupsEx mocks base method.
func (m *MockInterface) GetInstanceGroupsEx(arg0 context.Context, arg1 models.InstanceGroupListOption) (*models.InstanceGroupList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceGroupsEx", arg0, arg1)
	ret0, _ := ret[0].(*models.InstanceGroupList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceGroupsEx indicates an expected call of GetInstanceGroupsEx.
func (mr *MockInterfaceMockRecorder) GetInstanceGroupsEx(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceGroupsEx", reflect.TypeOf((*MockInterface)(nil).GetInstanceGroupsEx), arg0, arg1)
}

// GetInstanceNum mocks base method.
func (m *MockInterface) GetInstanceNum(arg0 context.Context, arg1, arg2, arg3, arg4 string, arg5 bool) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstanceNum", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstanceNum indicates an expected call of GetInstanceNum.
func (mr *MockInterfaceMockRecorder) GetInstanceNum(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstanceNum", reflect.TypeOf((*MockInterface)(nil).GetInstanceNum), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetInstances mocks base method.
func (m *MockInterface) GetInstances(arg0 context.Context, arg1, arg2 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstances indicates an expected call of GetInstances.
func (mr *MockInterfaceMockRecorder) GetInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstances", reflect.TypeOf((*MockInterface)(nil).GetInstances), arg0, arg1, arg2)
}

// GetInstancesByAccountID mocks base method.
func (m *MockInterface) GetInstancesByAccountID(arg0 context.Context, arg1 string, arg2 ccetypes.ClusterRole) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByAccountID", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByAccountID indicates an expected call of GetInstancesByAccountID.
func (mr *MockInterfaceMockRecorder) GetInstancesByAccountID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByAccountID", reflect.TypeOf((*MockInterface)(nil).GetInstancesByAccountID), arg0, arg1, arg2)
}

// GetInstancesByBCCIDs mocks base method.
func (m *MockInterface) GetInstancesByBCCIDs(arg0 context.Context, arg1 string, arg2 []string, arg3 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByBCCIDs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByBCCIDs indicates an expected call of GetInstancesByBCCIDs.
func (mr *MockInterfaceMockRecorder) GetInstancesByBCCIDs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByBCCIDs", reflect.TypeOf((*MockInterface)(nil).GetInstancesByBCCIDs), arg0, arg1, arg2, arg3)
}

// GetInstancesByBatchQuery mocks base method.
func (m *MockInterface) GetInstancesByBatchQuery(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7, arg8 int, arg9 ccetypes.ClusterRole, arg10 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByBatchQuery", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByBatchQuery indicates an expected call of GetInstancesByBatchQuery.
func (mr *MockInterfaceMockRecorder) GetInstancesByBatchQuery(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByBatchQuery", reflect.TypeOf((*MockInterface)(nil).GetInstancesByBatchQuery), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
}

// GetInstancesByCCEIDs mocks base method.
func (m *MockInterface) GetInstancesByCCEIDs(arg0 context.Context, arg1 string, arg2 []string, arg3 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByCCEIDs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByCCEIDs indicates an expected call of GetInstancesByCCEIDs.
func (mr *MockInterfaceMockRecorder) GetInstancesByCCEIDs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByCCEIDs", reflect.TypeOf((*MockInterface)(nil).GetInstancesByCCEIDs), arg0, arg1, arg2, arg3)
}

// GetInstancesByHpas mocks base method.
func (m *MockInterface) GetInstancesByHpas(arg0 context.Context, arg1 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByHpas", arg0, arg1)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByHpas indicates an expected call of GetInstancesByHpas.
func (mr *MockInterfaceMockRecorder) GetInstancesByHpas(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByHpas", reflect.TypeOf((*MockInterface)(nil).GetInstancesByHpas), arg0, arg1)
}

// GetInstancesByInstanceGroupID mocks base method.
func (m *MockInterface) GetInstancesByInstanceGroupID(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6, arg7 string, arg8, arg9 int) (*models.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByInstanceGroupID", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
	ret0, _ := ret[0].(*models.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByInstanceGroupID indicates an expected call of GetInstancesByInstanceGroupID.
func (mr *MockInterfaceMockRecorder) GetInstancesByInstanceGroupID(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByInstanceGroupID", reflect.TypeOf((*MockInterface)(nil).GetInstancesByInstanceGroupID), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
}

// GetInstancesByPage mocks base method.
func (m *MockInterface) GetInstancesByPage(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6 string, arg7, arg8 int, arg9 ccetypes.ClusterRole, arg10 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByPage", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByPage indicates an expected call of GetInstancesByPage.
func (mr *MockInterfaceMockRecorder) GetInstancesByPage(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByPage", reflect.TypeOf((*MockInterface)(nil).GetInstancesByPage), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10)
}

// GetInstancesByRole mocks base method.
func (m *MockInterface) GetInstancesByRole(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterRole) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesByRole", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesByRole indicates an expected call of GetInstancesByRole.
func (mr *MockInterfaceMockRecorder) GetInstancesByRole(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesByRole", reflect.TypeOf((*MockInterface)(nil).GetInstancesByRole), arg0, arg1, arg2, arg3)
}

// GetInstancesWithBatchQueryByInstanceGroupID mocks base method.
func (m *MockInterface) GetInstancesWithBatchQueryByInstanceGroupID(arg0 context.Context, arg1, arg2, arg3, arg4, arg5, arg6, arg7 string, arg8, arg9 int) (*models.InstanceList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesWithBatchQueryByInstanceGroupID", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
	ret0, _ := ret[0].(*models.InstanceList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesWithBatchQueryByInstanceGroupID indicates an expected call of GetInstancesWithBatchQueryByInstanceGroupID.
func (mr *MockInterfaceMockRecorder) GetInstancesWithBatchQueryByInstanceGroupID(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesWithBatchQueryByInstanceGroupID", reflect.TypeOf((*MockInterface)(nil).GetInstancesWithBatchQueryByInstanceGroupID), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
}

// GetK8SEventCluster mocks base method.
func (m *MockInterface) GetK8SEventCluster(arg0 context.Context, arg1 string) (*models.K8SEventCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetK8SEventCluster", arg0, arg1)
	ret0, _ := ret[0].(*models.K8SEventCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetK8SEventCluster indicates an expected call of GetK8SEventCluster.
func (mr *MockInterfaceMockRecorder) GetK8SEventCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetK8SEventCluster", reflect.TypeOf((*MockInterface)(nil).GetK8SEventCluster), arg0, arg1)
}

// GetK8SEventClusterList mocks base method.
func (m *MockInterface) GetK8SEventClusterList(arg0 context.Context, arg1 string) ([]*models.K8SEventCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetK8SEventClusterList", arg0, arg1)
	ret0, _ := ret[0].([]*models.K8SEventCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetK8SEventClusterList indicates an expected call of GetK8SEventClusterList.
func (mr *MockInterfaceMockRecorder) GetK8SEventClusterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetK8SEventClusterList", reflect.TypeOf((*MockInterface)(nil).GetK8SEventClusterList), arg0, arg1)
}

// GetKubeConfig mocks base method.
func (m *MockInterface) GetKubeConfig(arg0 context.Context, arg1, arg2 string, arg3 models.KubeConfigType) (*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubeConfig", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubeConfig indicates an expected call of GetKubeConfig.
func (mr *MockInterfaceMockRecorder) GetKubeConfig(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubeConfig", reflect.TypeOf((*MockInterface)(nil).GetKubeConfig), arg0, arg1, arg2, arg3)
}

// GetKubeConfigByClusterID mocks base method.
func (m *MockInterface) GetKubeConfigByClusterID(arg0 context.Context, arg1 string) ([]*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubeConfigByClusterID", arg0, arg1)
	ret0, _ := ret[0].([]*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubeConfigByClusterID indicates an expected call of GetKubeConfigByClusterID.
func (mr *MockInterfaceMockRecorder) GetKubeConfigByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubeConfigByClusterID", reflect.TypeOf((*MockInterface)(nil).GetKubeConfigByClusterID), arg0, arg1)
}

// GetKubeConfigCompatibility mocks base method.
func (m *MockInterface) GetKubeConfigCompatibility(arg0 context.Context, arg1, arg2 string, arg3 models.KubeConfigType) (*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubeConfigCompatibility", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubeConfigCompatibility indicates an expected call of GetKubeConfigCompatibility.
func (mr *MockInterfaceMockRecorder) GetKubeConfigCompatibility(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubeConfigCompatibility", reflect.TypeOf((*MockInterface)(nil).GetKubeConfigCompatibility), arg0, arg1, arg2, arg3)
}

// GetMasterType mocks base method.
func (m *MockInterface) GetMasterType(arg0 context.Context, arg1 string, arg2 int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMasterType", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMasterType indicates an expected call of GetMasterType.
func (mr *MockInterfaceMockRecorder) GetMasterType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMasterType", reflect.TypeOf((*MockInterface)(nil).GetMasterType), arg0, arg1, arg2)
}

// GetServiceCluster mocks base method.
func (m *MockInterface) GetServiceCluster(arg0 context.Context, arg1 string) (*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceCluster", arg0, arg1)
	ret0, _ := ret[0].(*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceCluster indicates an expected call of GetServiceCluster.
func (mr *MockInterfaceMockRecorder) GetServiceCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceCluster", reflect.TypeOf((*MockInterface)(nil).GetServiceCluster), arg0, arg1)
}

// GetServiceClusterByStatus mocks base method.
func (m *MockInterface) GetServiceClusterByStatus(arg0 context.Context, arg1 string, arg2 []string, arg3 int64, arg4, arg5 string) ([]*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceClusterByStatus", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceClusterByStatus indicates an expected call of GetServiceClusterByStatus.
func (mr *MockInterfaceMockRecorder) GetServiceClusterByStatus(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceClusterByStatus", reflect.TypeOf((*MockInterface)(nil).GetServiceClusterByStatus), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetServiceClusterCompatibility mocks base method.
func (m *MockInterface) GetServiceClusterCompatibility(arg0 context.Context, arg1 string) (*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceClusterCompatibility", arg0, arg1)
	ret0, _ := ret[0].(*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceClusterCompatibility indicates an expected call of GetServiceClusterCompatibility.
func (mr *MockInterfaceMockRecorder) GetServiceClusterCompatibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceClusterCompatibility", reflect.TypeOf((*MockInterface)(nil).GetServiceClusterCompatibility), arg0, arg1)
}

// GetServiceClusterWithAccount mocks base method.
func (m *MockInterface) GetServiceClusterWithAccount(arg0 context.Context, arg1, arg2 string) (*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceClusterWithAccount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceClusterWithAccount indicates an expected call of GetServiceClusterWithAccount.
func (mr *MockInterfaceMockRecorder) GetServiceClusterWithAccount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceClusterWithAccount", reflect.TypeOf((*MockInterface)(nil).GetServiceClusterWithAccount), arg0, arg1, arg2)
}

// GetServiceClusters mocks base method.
func (m *MockInterface) GetServiceClusters(arg0 context.Context, arg1 string) ([]*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceClusters", arg0, arg1)
	ret0, _ := ret[0].([]*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceClusters indicates an expected call of GetServiceClusters.
func (mr *MockInterfaceMockRecorder) GetServiceClusters(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceClusters", reflect.TypeOf((*MockInterface)(nil).GetServiceClusters), arg0, arg1)
}

// GetServiceClustersCompatibility mocks base method.
func (m *MockInterface) GetServiceClustersCompatibility(arg0 context.Context, arg1 string) ([]*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceClustersCompatibility", arg0, arg1)
	ret0, _ := ret[0].([]*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceClustersCompatibility indicates an expected call of GetServiceClustersCompatibility.
func (mr *MockInterfaceMockRecorder) GetServiceClustersCompatibility(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceClustersCompatibility", reflect.TypeOf((*MockInterface)(nil).GetServiceClustersCompatibility), arg0, arg1)
}

// GetServiceInstance mocks base method.
func (m *MockInterface) GetServiceInstance(arg0 context.Context, arg1, arg2 string) (*models.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstance indicates an expected call of GetServiceInstance.
func (mr *MockInterfaceMockRecorder) GetServiceInstance(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstance", reflect.TypeOf((*MockInterface)(nil).GetServiceInstance), arg0, arg1, arg2)
}

// GetServiceInstanceNum mocks base method.
func (m *MockInterface) GetServiceInstanceNum(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterRole) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstanceNum", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstanceNum indicates an expected call of GetServiceInstanceNum.
func (mr *MockInterfaceMockRecorder) GetServiceInstanceNum(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstanceNum", reflect.TypeOf((*MockInterface)(nil).GetServiceInstanceNum), arg0, arg1, arg2, arg3)
}

// GetServiceInstances mocks base method.
func (m *MockInterface) GetServiceInstances(arg0 context.Context, arg1, arg2 string) ([]*models.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstances indicates an expected call of GetServiceInstances.
func (mr *MockInterfaceMockRecorder) GetServiceInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstances", reflect.TypeOf((*MockInterface)(nil).GetServiceInstances), arg0, arg1, arg2)
}

// GetServiceInstancesByAccountID mocks base method.
func (m *MockInterface) GetServiceInstancesByAccountID(arg0 context.Context, arg1 string, arg2 ccetypes.ClusterRole) ([]*models.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstancesByAccountID", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstancesByAccountID indicates an expected call of GetServiceInstancesByAccountID.
func (mr *MockInterfaceMockRecorder) GetServiceInstancesByAccountID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstancesByAccountID", reflect.TypeOf((*MockInterface)(nil).GetServiceInstancesByAccountID), arg0, arg1, arg2)
}

// GetServiceInstancesByRole mocks base method.
func (m *MockInterface) GetServiceInstancesByRole(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterRole) ([]*models.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstancesByRole", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstancesByRole indicates an expected call of GetServiceInstancesByRole.
func (mr *MockInterfaceMockRecorder) GetServiceInstancesByRole(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstancesByRole", reflect.TypeOf((*MockInterface)(nil).GetServiceInstancesByRole), arg0, arg1, arg2, arg3)
}

// GetServiceInstancesCompatibility mocks base method.
func (m *MockInterface) GetServiceInstancesCompatibility(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterRole) ([]*models.ServiceInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceInstancesCompatibility", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*models.ServiceInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceInstancesCompatibility indicates an expected call of GetServiceInstancesCompatibility.
func (mr *MockInterfaceMockRecorder) GetServiceInstancesCompatibility(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceInstancesCompatibility", reflect.TypeOf((*MockInterface)(nil).GetServiceInstancesCompatibility), arg0, arg1, arg2, arg3)
}

// GetStatisticsByDate mocks base method.
func (m *MockInterface) GetStatisticsByDate(arg0 context.Context, arg1 time.Time) ([]*models.Statistics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatisticsByDate", arg0, arg1)
	ret0, _ := ret[0].([]*models.Statistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatisticsByDate indicates an expected call of GetStatisticsByDate.
func (mr *MockInterfaceMockRecorder) GetStatisticsByDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatisticsByDate", reflect.TypeOf((*MockInterface)(nil).GetStatisticsByDate), arg0, arg1)
}

// GetSyncCCEInstanceIDs mocks base method.
func (m *MockInterface) GetSyncCCEInstanceIDs(arg0 context.Context, arg1 time.Duration, arg2 []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncCCEInstanceIDs", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncCCEInstanceIDs indicates an expected call of GetSyncCCEInstanceIDs.
func (mr *MockInterfaceMockRecorder) GetSyncCCEInstanceIDs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncCCEInstanceIDs", reflect.TypeOf((*MockInterface)(nil).GetSyncCCEInstanceIDs), arg0, arg1, arg2)
}

// GetSyncClusterIDs mocks base method.
func (m *MockInterface) GetSyncClusterIDs(arg0 context.Context, arg1 time.Duration, arg2 []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncClusterIDs", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncClusterIDs indicates an expected call of GetSyncClusterIDs.
func (mr *MockInterfaceMockRecorder) GetSyncClusterIDs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncClusterIDs", reflect.TypeOf((*MockInterface)(nil).GetSyncClusterIDs), arg0, arg1, arg2)
}

// GetSyncClusters mocks base method.
func (m *MockInterface) GetSyncClusters(arg0 context.Context, arg1 time.Duration, arg2 []string) ([]*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncClusters", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncClusters indicates an expected call of GetSyncClusters.
func (mr *MockInterfaceMockRecorder) GetSyncClusters(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncClusters", reflect.TypeOf((*MockInterface)(nil).GetSyncClusters), arg0, arg1, arg2)
}

// GetSyncInstances mocks base method.
func (m *MockInterface) GetSyncInstances(arg0 context.Context, arg1 time.Duration, arg2 []string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncInstances", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSyncInstances indicates an expected call of GetSyncInstances.
func (mr *MockInterfaceMockRecorder) GetSyncInstances(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncInstances", reflect.TypeOf((*MockInterface)(nil).GetSyncInstances), arg0, arg1, arg2)
}

// GetTestResultsByBatchID mocks base method.
func (m *MockInterface) GetTestResultsByBatchID(arg0 context.Context, arg1 string) ([]*models.TestResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestResultsByBatchID", arg0, arg1)
	ret0, _ := ret[0].([]*models.TestResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTestResultsByBatchID indicates an expected call of GetTestResultsByBatchID.
func (mr *MockInterfaceMockRecorder) GetTestResultsByBatchID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestResultsByBatchID", reflect.TypeOf((*MockInterface)(nil).GetTestResultsByBatchID), arg0, arg1)
}

// GetTestResultsByDate mocks base method.
func (m *MockInterface) GetTestResultsByDate(arg0 context.Context, arg1 time.Time) ([]*models.TestResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTestResultsByDate", arg0, arg1)
	ret0, _ := ret[0].([]*models.TestResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTestResultsByDate indicates an expected call of GetTestResultsByDate.
func (mr *MockInterfaceMockRecorder) GetTestResultsByDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTestResultsByDate", reflect.TypeOf((*MockInterface)(nil).GetTestResultsByDate), arg0, arg1)
}

// GetTokenByRefreshToken mocks base method.
func (m *MockInterface) GetTokenByRefreshToken(arg0 context.Context, arg1, arg2 string) (*models.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenByRefreshToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenByRefreshToken indicates an expected call of GetTokenByRefreshToken.
func (mr *MockInterfaceMockRecorder) GetTokenByRefreshToken(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenByRefreshToken", reflect.TypeOf((*MockInterface)(nil).GetTokenByRefreshToken), arg0, arg1, arg2)
}

// GetTokenByUser mocks base method.
func (m *MockInterface) GetTokenByUser(arg0 context.Context, arg1, arg2, arg3 string) (*models.Token, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenByUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*models.Token)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenByUser indicates an expected call of GetTokenByUser.
func (mr *MockInterfaceMockRecorder) GetTokenByUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenByUser", reflect.TypeOf((*MockInterface)(nil).GetTokenByUser), arg0, arg1, arg2, arg3)
}

// GetUserScript mocks base method.
func (m *MockInterface) GetUserScript(arg0 context.Context, arg1 string) (*models.UserScript, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserScript", arg0, arg1)
	ret0, _ := ret[0].(*models.UserScript)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserScript indicates an expected call of GetUserScript.
func (mr *MockInterfaceMockRecorder) GetUserScript(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserScript", reflect.TypeOf((*MockInterface)(nil).GetUserScript), arg0, arg1)
}

// GetV1ClustersByID mocks base method.
func (m *MockInterface) GetV1ClustersByID(arg0 context.Context, arg1 []string) ([]*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetV1ClustersByID", arg0, arg1)
	ret0, _ := ret[0].([]*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetV1ClustersByID indicates an expected call of GetV1ClustersByID.
func (mr *MockInterfaceMockRecorder) GetV1ClustersByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetV1ClustersByID", reflect.TypeOf((*MockInterface)(nil).GetV1ClustersByID), arg0, arg1)
}

// GetV2ServiceClustersByID mocks base method.
func (m *MockInterface) GetV2ServiceClustersByID(arg0 context.Context, arg1 []string) ([]*models.ServiceCluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetV2ServiceClustersByID", arg0, arg1)
	ret0, _ := ret[0].([]*models.ServiceCluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetV2ServiceClustersByID indicates an expected call of GetV2ServiceClustersByID.
func (mr *MockInterfaceMockRecorder) GetV2ServiceClustersByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetV2ServiceClustersByID", reflect.TypeOf((*MockInterface)(nil).GetV2ServiceClustersByID), arg0, arg1)
}

// GetWorkflow mocks base method.
func (m *MockInterface) GetWorkflow(arg0 context.Context, arg1 string) (*models.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflow", arg0, arg1)
	ret0, _ := ret[0].(*models.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflow indicates an expected call of GetWorkflow.
func (mr *MockInterfaceMockRecorder) GetWorkflow(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflow", reflect.TypeOf((*MockInterface)(nil).GetWorkflow), arg0, arg1)
}

// GetWorkflowByClusterID mocks base method.
func (m *MockInterface) GetWorkflowByClusterID(arg0 context.Context, arg1 string) ([]*models.Workflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowByClusterID", arg0, arg1)
	ret0, _ := ret[0].([]*models.Workflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowByClusterID indicates an expected call of GetWorkflowByClusterID.
func (mr *MockInterfaceMockRecorder) GetWorkflowByClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowByClusterID", reflect.TypeOf((*MockInterface)(nil).GetWorkflowByClusterID), arg0, arg1)
}

// IfK8SEventClusterExists mocks base method.
func (m *MockInterface) IfK8SEventClusterExists(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IfK8SEventClusterExists", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IfK8SEventClusterExists indicates an expected call of IfK8SEventClusterExists.
func (mr *MockInterfaceMockRecorder) IfK8SEventClusterExists(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IfK8SEventClusterExists", reflect.TypeOf((*MockInterface)(nil).IfK8SEventClusterExists), arg0, arg1)
}

// IfK8SEventClusterIndexExists mocks base method.
func (m *MockInterface) IfK8SEventClusterIndexExists(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IfK8SEventClusterIndexExists", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IfK8SEventClusterIndexExists indicates an expected call of IfK8SEventClusterIndexExists.
func (mr *MockInterfaceMockRecorder) IfK8SEventClusterIndexExists(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IfK8SEventClusterIndexExists", reflect.TypeOf((*MockInterface)(nil).IfK8SEventClusterIndexExists), arg0, arg1)
}

// ListInstanceGroupReplicasTasks mocks base method.
func (m *MockInterface) ListInstanceGroupReplicasTasks(arg0 context.Context, arg1 models.ListInstanceGroupReplicasTaskOptions) (*models.InstanceGroupReplicasTaskList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstanceGroupReplicasTasks", arg0, arg1)
	ret0, _ := ret[0].(*models.InstanceGroupReplicasTaskList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstanceGroupReplicasTasks indicates an expected call of ListInstanceGroupReplicasTasks.
func (mr *MockInterfaceMockRecorder) ListInstanceGroupReplicasTasks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstanceGroupReplicasTasks", reflect.TypeOf((*MockInterface)(nil).ListInstanceGroupReplicasTasks), arg0, arg1)
}

// ListK8SEventClustersWithStatus mocks base method.
func (m *MockInterface) ListK8SEventClustersWithStatus(arg0 context.Context, arg1 models.EventClusterStatus) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListK8SEventClustersWithStatus", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListK8SEventClustersWithStatus indicates an expected call of ListK8SEventClustersWithStatus.
func (mr *MockInterfaceMockRecorder) ListK8SEventClustersWithStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListK8SEventClustersWithStatus", reflect.TypeOf((*MockInterface)(nil).ListK8SEventClustersWithStatus), arg0, arg1)
}

// ListKubeConfig mocks base method.
func (m *MockInterface) ListKubeConfig(arg0 context.Context, arg1 string) ([]*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKubeConfig", arg0, arg1)
	ret0, _ := ret[0].([]*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKubeConfig indicates an expected call of ListKubeConfig.
func (mr *MockInterfaceMockRecorder) ListKubeConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKubeConfig", reflect.TypeOf((*MockInterface)(nil).ListKubeConfig), arg0, arg1)
}

// ListKubeConfigByRole mocks base method.
func (m *MockInterface) ListKubeConfigByRole(arg0 context.Context, arg1, arg2 string) ([]*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListKubeConfigByRole", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListKubeConfigByRole indicates an expected call of ListKubeConfigByRole.
func (mr *MockInterfaceMockRecorder) ListKubeConfigByRole(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListKubeConfigByRole", reflect.TypeOf((*MockInterface)(nil).ListKubeConfigByRole), arg0, arg1, arg2)
}

// UpdateAuditStateByClusterID mocks base method.
func (m *MockInterface) UpdateAuditStateByClusterID(arg0 context.Context, arg1 string, arg2 bool, arg3 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAuditStateByClusterID", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAuditStateByClusterID indicates an expected call of UpdateAuditStateByClusterID.
func (mr *MockInterfaceMockRecorder) UpdateAuditStateByClusterID(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAuditStateByClusterID", reflect.TypeOf((*MockInterface)(nil).UpdateAuditStateByClusterID), arg0, arg1, arg2, arg3)
}

// UpdateAutoscaler mocks base method.
func (m *MockInterface) UpdateAutoscaler(arg0 context.Context, arg1 *models.Autoscaler, arg2 models.updatePluginFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAutoscaler", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAutoscaler indicates an expected call of UpdateAutoscaler.
func (mr *MockInterfaceMockRecorder) UpdateAutoscaler(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAutoscaler", reflect.TypeOf((*MockInterface)(nil).UpdateAutoscaler), arg0, arg1, arg2)
}

// UpdateCa mocks base method.
func (m *MockInterface) UpdateCa(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCa", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCa indicates an expected call of UpdateCa.
func (mr *MockInterfaceMockRecorder) UpdateCa(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCa", reflect.TypeOf((*MockInterface)(nil).UpdateCa), arg0, arg1, arg2)
}

// UpdateClusterAllowDelete mocks base method.
func (m *MockInterface) UpdateClusterAllowDelete(arg0 context.Context, arg1, arg2 string, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterAllowDelete", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterAllowDelete indicates an expected call of UpdateClusterAllowDelete.
func (mr *MockInterfaceMockRecorder) UpdateClusterAllowDelete(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterAllowDelete", reflect.TypeOf((*MockInterface)(nil).UpdateClusterAllowDelete), arg0, arg1, arg2, arg3)
}

// UpdateClusterDeleteTime mocks base method.
func (m *MockInterface) UpdateClusterDeleteTime(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterDeleteTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterDeleteTime indicates an expected call of UpdateClusterDeleteTime.
func (mr *MockInterfaceMockRecorder) UpdateClusterDeleteTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterDeleteTime", reflect.TypeOf((*MockInterface)(nil).UpdateClusterDeleteTime), arg0, arg1, arg2)
}

// UpdateClusterDescription mocks base method.
func (m *MockInterface) UpdateClusterDescription(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterDescription", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterDescription indicates an expected call of UpdateClusterDescription.
func (mr *MockInterfaceMockRecorder) UpdateClusterDescription(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterDescription", reflect.TypeOf((*MockInterface)(nil).UpdateClusterDescription), arg0, arg1, arg2, arg3)
}

// UpdateClusterInfoConfig mocks base method.
func (m *MockInterface) UpdateClusterInfoConfig(arg0 context.Context, arg1, arg2, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterInfoConfig", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterInfoConfig indicates an expected call of UpdateClusterInfoConfig.
func (mr *MockInterfaceMockRecorder) UpdateClusterInfoConfig(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterInfoConfig", reflect.TypeOf((*MockInterface)(nil).UpdateClusterInfoConfig), arg0, arg1, arg2, arg3, arg4)
}

// UpdateClusterPhase mocks base method.
func (m *MockInterface) UpdateClusterPhase(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.ClusterPhase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterPhase", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterPhase indicates an expected call of UpdateClusterPhase.
func (mr *MockInterfaceMockRecorder) UpdateClusterPhase(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterPhase", reflect.TypeOf((*MockInterface)(nil).UpdateClusterPhase), arg0, arg1, arg2, arg3)
}

// UpdateClusterReport mocks base method.
func (m *MockInterface) UpdateClusterReport(arg0 context.Context, arg1 *models.ClusterReport) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterReport", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterReport indicates an expected call of UpdateClusterReport.
func (mr *MockInterfaceMockRecorder) UpdateClusterReport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterReport", reflect.TypeOf((*MockInterface)(nil).UpdateClusterReport), arg0, arg1)
}

// UpdateClusterSpecDBAndCRD mocks base method.
func (m *MockInterface) UpdateClusterSpecDBAndCRD(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.ClusterSpec, arg4 *v10.ObjectMeta, arg5 map[validate.ValidateCondition]interface{}, arg6 bool, arg7 models.updateClusterSpecFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterSpecDBAndCRD", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterSpecDBAndCRD indicates an expected call of UpdateClusterSpecDBAndCRD.
func (mr *MockInterfaceMockRecorder) UpdateClusterSpecDBAndCRD(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterSpecDBAndCRD", reflect.TypeOf((*MockInterface)(nil).UpdateClusterSpecDBAndCRD), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// UpdateClusterStatus mocks base method.
func (m *MockInterface) UpdateClusterStatus(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.ClusterStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateClusterStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateClusterStatus indicates an expected call of UpdateClusterStatus.
func (mr *MockInterfaceMockRecorder) UpdateClusterStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClusterStatus", reflect.TypeOf((*MockInterface)(nil).UpdateClusterStatus), arg0, arg1, arg2, arg3)
}

// UpdateEventClusterStatus mocks base method.
func (m *MockInterface) UpdateEventClusterStatus(arg0 context.Context, arg1 string, arg2 models.EventClusterStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEventClusterStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEventClusterStatus indicates an expected call of UpdateEventClusterStatus.
func (mr *MockInterfaceMockRecorder) UpdateEventClusterStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEventClusterStatus", reflect.TypeOf((*MockInterface)(nil).UpdateEventClusterStatus), arg0, arg1, arg2)
}

// UpdateIDToken mocks base method.
func (m *MockInterface) UpdateIDToken(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateIDToken", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateIDToken indicates an expected call of UpdateIDToken.
func (mr *MockInterfaceMockRecorder) UpdateIDToken(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateIDToken", reflect.TypeOf((*MockInterface)(nil).UpdateIDToken), arg0, arg1, arg2, arg3)
}

// UpdateInstanceDeleteTime mocks base method.
func (m *MockInterface) UpdateInstanceDeleteTime(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceDeleteTime", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceDeleteTime indicates an expected call of UpdateInstanceDeleteTime.
func (mr *MockInterfaceMockRecorder) UpdateInstanceDeleteTime(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceDeleteTime", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceDeleteTime), arg0, arg1, arg2)
}

// UpdateInstanceGroupAutoscalerSpec mocks base method.
func (m *MockInterface) UpdateInstanceGroupAutoscalerSpec(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.ClusterAutoscalerSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupAutoscalerSpec", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupAutoscalerSpec indicates an expected call of UpdateInstanceGroupAutoscalerSpec.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupAutoscalerSpec(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupAutoscalerSpec", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupAutoscalerSpec), arg0, arg1, arg2, arg3)
}

// UpdateInstanceGroupDBAndCRD mocks base method.
func (m *MockInterface) UpdateInstanceGroupDBAndCRD(arg0 context.Context, arg1, arg2 string, arg3 *v1.InstanceGroup, arg4 models.updateInstanceGroupCRDFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupDBAndCRD", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupDBAndCRD indicates an expected call of UpdateInstanceGroupDBAndCRD.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupDBAndCRD(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupDBAndCRD", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupDBAndCRD), arg0, arg1, arg2, arg3, arg4)
}

// UpdateInstanceGroupReplicas mocks base method.
func (m *MockInterface) UpdateInstanceGroupReplicas(arg0 context.Context, arg1, arg2 string, arg3 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupReplicas", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupReplicas indicates an expected call of UpdateInstanceGroupReplicas.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupReplicas(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupReplicas", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupReplicas), arg0, arg1, arg2, arg3)
}

// UpdateInstanceGroupReplicasTask mocks base method.
func (m *MockInterface) UpdateInstanceGroupReplicasTask(arg0 context.Context, arg1 *models.InstanceGroupReplicasTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupReplicasTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupReplicasTask indicates an expected call of UpdateInstanceGroupReplicasTask.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupReplicasTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupReplicasTask", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupReplicasTask), arg0, arg1)
}

// UpdateInstanceGroupSpec mocks base method.
func (m *MockInterface) UpdateInstanceGroupSpec(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceGroupSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupSpec", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupSpec indicates an expected call of UpdateInstanceGroupSpec.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupSpec(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupSpec", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupSpec), arg0, arg1, arg2, arg3)
}

// UpdateInstanceGroupStatus mocks base method.
func (m *MockInterface) UpdateInstanceGroupStatus(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceGroupStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupStatus indicates an expected call of UpdateInstanceGroupStatus.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupStatus", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupStatus), arg0, arg1, arg2, arg3)
}

// UpdateInstanceGroupSyncMetaTask mocks base method.
func (m *MockInterface) UpdateInstanceGroupSyncMetaTask(arg0 context.Context, arg1 *models.InstanceGroupSyncMetaTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceGroupSyncMetaTask", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceGroupSyncMetaTask indicates an expected call of UpdateInstanceGroupSyncMetaTask.
func (mr *MockInterfaceMockRecorder) UpdateInstanceGroupSyncMetaTask(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceGroupSyncMetaTask", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceGroupSyncMetaTask), arg0, arg1)
}

// UpdateInstancePhase mocks base method.
func (m *MockInterface) UpdateInstancePhase(arg0 context.Context, arg1, arg2 string, arg3 ccetypes.InstancePhase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstancePhase", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstancePhase indicates an expected call of UpdateInstancePhase.
func (mr *MockInterfaceMockRecorder) UpdateInstancePhase(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstancePhase", reflect.TypeOf((*MockInterface)(nil).UpdateInstancePhase), arg0, arg1, arg2, arg3)
}

// UpdateInstanceSpecDBAndCRD mocks base method.
func (m *MockInterface) UpdateInstanceSpecDBAndCRD(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceSpec, arg4 *v10.ObjectMeta, arg5 map[validate.ValidateCondition]interface{}, arg6 bool, arg7 models.updateInstanceSpecFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceSpecDBAndCRD", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceSpecDBAndCRD indicates an expected call of UpdateInstanceSpecDBAndCRD.
func (mr *MockInterfaceMockRecorder) UpdateInstanceSpecDBAndCRD(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceSpecDBAndCRD", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceSpecDBAndCRD), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7)
}

// UpdateInstanceStatus mocks base method.
func (m *MockInterface) UpdateInstanceStatus(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstanceStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstanceStatus indicates an expected call of UpdateInstanceStatus.
func (mr *MockInterfaceMockRecorder) UpdateInstanceStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstanceStatus", reflect.TypeOf((*MockInterface)(nil).UpdateInstanceStatus), arg0, arg1, arg2, arg3)
}

// UpdateInstancesDeleteOptionAndPhase mocks base method.
func (m *MockInterface) UpdateInstancesDeleteOptionAndPhase(arg0 context.Context, arg1 string, arg2 []string, arg3 string, arg4 *ccetypes.DeleteOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateInstancesDeleteOptionAndPhase", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateInstancesDeleteOptionAndPhase indicates an expected call of UpdateInstancesDeleteOptionAndPhase.
func (mr *MockInterfaceMockRecorder) UpdateInstancesDeleteOptionAndPhase(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateInstancesDeleteOptionAndPhase", reflect.TypeOf((*MockInterface)(nil).UpdateInstancesDeleteOptionAndPhase), arg0, arg1, arg2, arg3, arg4)
}

// UpdateKubeConfig mocks base method.
func (m *MockInterface) UpdateKubeConfig(arg0 context.Context, arg1 *models.KubeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateKubeConfig", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateKubeConfig indicates an expected call of UpdateKubeConfig.
func (mr *MockInterfaceMockRecorder) UpdateKubeConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateKubeConfig", reflect.TypeOf((*MockInterface)(nil).UpdateKubeConfig), arg0, arg1)
}

// UpdateKubeConfigEIP mocks base method.
func (m *MockInterface) UpdateKubeConfigEIP(arg0 context.Context, arg1 *models.KubeConfig, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateKubeConfigEIP", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateKubeConfigEIP indicates an expected call of UpdateKubeConfigEIP.
func (mr *MockInterfaceMockRecorder) UpdateKubeConfigEIP(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateKubeConfigEIP", reflect.TypeOf((*MockInterface)(nil).UpdateKubeConfigEIP), arg0, arg1, arg2)
}

// UpdatePartClusterStatus mocks base method.
func (m *MockInterface) UpdatePartClusterStatus(arg0 context.Context, arg1, arg2 string, arg3 map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartClusterStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartClusterStatus indicates an expected call of UpdatePartClusterStatus.
func (mr *MockInterfaceMockRecorder) UpdatePartClusterStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartClusterStatus", reflect.TypeOf((*MockInterface)(nil).UpdatePartClusterStatus), arg0, arg1, arg2, arg3)
}

// UpdatePartInstanceDBAndCRD mocks base method.
func (m *MockInterface) UpdatePartInstanceDBAndCRD(arg0 context.Context, arg1, arg2 string, arg3 *models.Instance, arg4 models.getInstanceCRDFunc, arg5 models.updateInstanceCRDFunc) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartInstanceDBAndCRD", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartInstanceDBAndCRD indicates an expected call of UpdatePartInstanceDBAndCRD.
func (mr *MockInterfaceMockRecorder) UpdatePartInstanceDBAndCRD(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartInstanceDBAndCRD", reflect.TypeOf((*MockInterface)(nil).UpdatePartInstanceDBAndCRD), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UpdatePartInstanceGPUInfo mocks base method.
func (m *MockInterface) UpdatePartInstanceGPUInfo(arg0 context.Context, arg1, arg2, arg3 string, arg4 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartInstanceGPUInfo", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartInstanceGPUInfo indicates an expected call of UpdatePartInstanceGPUInfo.
func (mr *MockInterfaceMockRecorder) UpdatePartInstanceGPUInfo(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartInstanceGPUInfo", reflect.TypeOf((*MockInterface)(nil).UpdatePartInstanceGPUInfo), arg0, arg1, arg2, arg3, arg4)
}

// UpdatePartInstanceSpec mocks base method.
func (m *MockInterface) UpdatePartInstanceSpec(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartInstanceSpec", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartInstanceSpec indicates an expected call of UpdatePartInstanceSpec.
func (mr *MockInterfaceMockRecorder) UpdatePartInstanceSpec(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartInstanceSpec", reflect.TypeOf((*MockInterface)(nil).UpdatePartInstanceSpec), arg0, arg1, arg2, arg3)
}

// UpdatePartInstanceStatus mocks base method.
func (m *MockInterface) UpdatePartInstanceStatus(arg0 context.Context, arg1, arg2 string, arg3 *ccetypes.InstanceStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePartInstanceStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePartInstanceStatus indicates an expected call of UpdatePartInstanceStatus.
func (mr *MockInterfaceMockRecorder) UpdatePartInstanceStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePartInstanceStatus", reflect.TypeOf((*MockInterface)(nil).UpdatePartInstanceStatus), arg0, arg1, arg2, arg3)
}

// UpdateWorkflow mocks base method.
func (m *MockInterface) UpdateWorkflow(arg0 context.Context, arg1 string, arg2 *models.Workflow) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWorkflow", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWorkflow indicates an expected call of UpdateWorkflow.
func (mr *MockInterfaceMockRecorder) UpdateWorkflow(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWorkflow", reflect.TypeOf((*MockInterface)(nil).UpdateWorkflow), arg0, arg1, arg2)
}
