// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/06 17:09:00, by <EMAIL>, create
*/
/*
Instance Model 相关单元测试
*/

package models

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	corev1 "k8s.io/api/core/v1"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

func instanceSpec(name, existedInstanceID, accountID, clusterID string, role ccetypes.ClusterRole) *ccetypes.InstanceSpec {
	rebuild := true

	spec := &ccetypes.InstanceSpec{
		Handler:        "chenhuan",
		InstanceName:   name,
		RuntimeType:    ccetypes.RuntimeTypeDocker,
		RuntimeVersion: "18.9.01",
		ClusterRole:    role,
		UserID:         "user-id",
		AccountID:      accountID,
		MasterType:     ccetypes.MasterTypeCustom,
		Existed:        true,
		ExistedOption: ccetypes.ExistedOption{
			ExistedInstanceID: existedInstanceID,
			Rebuild:           &rebuild,
		},
		MachineType:  ccetypes.MachineTypeBCC,
		InstanceType: bcc.InstanceTypeC2,
		BBCOption: ccetypes.BBCOption{
			ReserveData: true,
			RaidID:      "raid-id",
			SysDiskSize: 1000,
		},
		BECOption: ccetypes.BECOption{
			BECRegion: "bj",
		},
		VPCConfig: ccetypes.VPCConfig{
			VPCID:             "vpc-id",
			VPCSubnetID:       "vpc-subnet-id",
			SecurityGroupID:   "security-group-id",
			VPCUUID:           "vpc-uuid",
			VPCSubnetUUID:     "vpc-subnet-uuid",
			SecurityGroupUUID: "security-group-uuid",
			VPCSubnetType:     vpc.SubnetTypeBCC,
			VPCSubnetCIDR:     "10.0.0.0/8",
			VPCSubnetCIDRIPv6: "F001:1e2r:131f:112341",
			AvailableZone:     internalvpc.ZoneA,
		},
		InstanceResource: ccetypes.InstanceResource{
			CPU:           10,
			MEM:           128,
			RootDiskType:  bcc.StorageTypeCloudHP1,
			RootDiskSize:  100,
			LocalDiskSize: 40,
			CDSList: ccetypes.CDSConfigList{
				ccetypes.CDSConfig{
					Path:        "path",
					StorageType: bcc.StorageTypeDCCSSD,
					CDSSize:     100000,
					SnapshotID:  "snapshort-id",
					EncryptKey:  "EncryptKey",
				},
				ccetypes.CDSConfig{
					Path:        "path",
					StorageType: bcc.StorageTypeDCCSSD,
					CDSSize:     100000,
					SnapshotID:  "snapshort-id",
				},
			},
			GPUType:  bcc.GPUTypeK40,
			GPUCount: 8,
		},
		ImageID:   "image-id",
		ImageUUID: "image-uuid",
		InstanceOS: ccetypes.InstanceOS{
			ImageType: bccimage.ImageTypeSystem,
			ImageName: "image-name",
			OSType:    bccimage.OSTypeLinux,
			OSName:    bccimage.OSNameCentOS,
			OSVersion: "7.5",
			OSArch:    "os-arch",
			OSBuild:   "os-build",
		},
		NeedEIP: true,
		EIPOption: ccetypes.EIPOption{
			EIPName:         "eip-name",
			EIPChargingType: eip.BillingMethodByBandwidth,
			EIPBandwidth:    1000,
		},
		AdminPassword:        "admin-password",
		SSHKeyID:             "ssh-key-id",
		InstanceChargingType: bcc.PaymentTimingPrepaid,
		InstancePreChargingOption: ccetypes.InstancePreChargingOption{
			PurchaseTime:      10,
			AutoRenew:         true,
			AutoRenewTimeUnit: "auto-renew-time-unit",
			AutoRenewTime:     10,
		},
		DeleteOption: &ccetypes.DeleteOption{
			MoveOut:           false,
			DeleteResource:    true,
			DeleteCDSSnapshot: true,
		},
		Tags: ccetypes.TagList{
			ccetypes.Tag{
				TagKey:   "name",
				TagValue: "chenhuan",
			},
			ccetypes.Tag{
				TagKey:   "name",
				TagValue: "chenhuan",
			},
		},
	}

	if clusterID != "" {
		spec.ClusterID = clusterID
	}

	return spec
}

// instanceSpecWithChargingType 创建指定计费方式的实例规格（用于测试计费方式筛选功能）
func instanceSpecWithChargingType(name, existedInstanceID, accountID, clusterID string, role ccetypes.ClusterRole, chargingType bcc.PaymentTiming) *ccetypes.InstanceSpec {
	spec := instanceSpec(name, existedInstanceID, accountID, clusterID, role)
	spec.InstanceChargingType = chargingType
	return spec
}

func instanceStatus(instanceID string, instancePhase ccetypes.InstancePhase) *ccetypes.InstanceStatus {
	return &ccetypes.InstanceStatus{
		Machine: ccetypes.Machine{
			InstanceID:    instanceID,
			InstanceUUID:  "instance-uuid",
			MachineStatus: logicbcc.ServerStatusActive,
			OrderID:       "order-id",
			DCC:           false,
			DCCID:         "dcc-id",
			DCCUUID:       "dcc-uuid",
			VPCIP:         "vpc-ip",
			VPCIPIPv6:     "vpc-ip-ipv6",
			FloatingIP:    "floating-ip",
			EIP:           "eip",
		},
		InstancePhase:       instancePhase,
		ReinstallOSAlready:  true,
		InfrastructureReady: true,
		DeploySuccess:       true,
	}
}

func TestInstanceModel_DeepCopy(t *testing.T) {
	type fields struct {
		Spec   *ccetypes.InstanceSpec
		Status *ccetypes.InstanceStatus
	}
	tests := []struct {
		name    string
		fields  fields
		want    *Instance
		wantErr bool
	}{
		{
			name: "正常情况",
			fields: fields{
				Spec: &ccetypes.InstanceSpec{
					CCEInstanceID: "xxxxxxxxxxxxxxxxxxxxxxxxxxx",
					InstanceName:  "instance-name",
					ClusterRole:   ccetypes.ClusterRoleNode,
				},
				Status: &ccetypes.InstanceStatus{
					Machine: ccetypes.Machine{
						InstanceID: "i-adfacaDdx",
					},
				},
			},
			want: &Instance{
				Spec: &ccetypes.InstanceSpec{
					CCEInstanceID: "xxxxxxxxxxxxxxxxxxxxxxxxxxx",
					InstanceName:  "instance-name",
					ClusterRole:   ccetypes.ClusterRoleNode,
				},
				Status: &ccetypes.InstanceStatus{
					Machine: ccetypes.Machine{
						InstanceID: "i-adfacaDdx",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &Instance{
				Spec:   tt.fields.Spec,
				Status: tt.fields.Status,
			}
			got, err := i.DeepCopy()
			if (err != nil) != tt.wantErr {
				t.Errorf("InstanceModel.DeepCopy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("InstanceModel.DeepCopy() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CreateInstances(t *testing.T) {
	type fields struct {
		client *Client
	}

	type args struct {
		ctx       context.Context
		clusterID string
		instances []*Instance
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常写入",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-cluster",
				instances: func() []*Instance {
					instances := []*Instance{}

					for _, instanceName := range []string{
						"instance-name-0",
						"instance-name-1",
						"instance-name-2",
						"instance-name-3",
					} {
						spec := instanceSpec(instanceName, instanceName, "account-id", "cluster-id", ccetypes.ClusterRoleMaster)

						instances = append(instances, &Instance{
							Spec: spec,
						})
					}

					return instances
				}(),
			},
			wantErr: false,
		},
		{
			name: "已有实例, 插入中存在 ExistedInstanceID 重复, 插入报错",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-cluster",
				instances: func() []*Instance {
					instances := []*Instance{}

					for _, instanceName := range []string{
						"instance-name-0",
						"instance-name-1",
						"instance-name-2",
						"instance-name-3",
					} {
						spec := instanceSpec(instanceName, "instance-id", "account-id", "cluster-id", ccetypes.ClusterRoleMaster)

						instances = append(instances, &Instance{
							Spec: spec,
						})
					}

					return instances
				}(),
			},
			wantErr: true,
		},
		{
			name: "已有实例, 数据库中存在 ExistInstanceID = instance-id, 插入报错",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				if err := client.db.Table(client.cceInstanceTableName).Create(
					&Instance{
						Spec: instanceSpec("instance-name-0", "instance-id", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					},
				).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-cluster",
				instances: []*Instance{
					{
						Spec: instanceSpec("instance-name-0", "instance-id", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "已有实例, 数据库中存在 InstanceID = instance-id, 插入报错",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				if err := client.db.Table(client.cceInstanceTableName).Create(
					&Instance{
						Spec: instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: &ccetypes.InstanceStatus{
							Machine: ccetypes.Machine{
								InstanceID: "instance-id",
							},
						},
					},
				).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-cluster",
				instances: []*Instance{
					{
						Spec: instanceSpec("instance-name-0", "instance-id", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					},
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.fields.client
			defer unitTestDBGC(tt.args.ctx, c)

			cceInstanceIDs, err := c.CreateInstances(tt.args.ctx, tt.args.clusterID, tt.args.instances)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("Client.CreateInstances() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.wantErr == true {
				t.Errorf("Client.CreateInstances() failed, wantErr=true got=false")
				return
			}

			ctx := context.TODO()

			// 查询 cceInstanceIDs 进行对比
			for i, cceInstanceID := range cceInstanceIDs {
				instance, err := c.GetInstanceByCCEID(ctx, cceInstanceID, "account-id")
				if err != nil {
					t.Errorf("Client.GetInstanceByCCEID() clusterID %s failed: %s", cceInstanceID, err)
					return
				}

				if !cmpInstance(instance, tt.args.instances[i]) {
					t.Errorf("Client.CreateCluster() cceInstanceID %s failed: Diff=%s", cceInstanceID, cmp.Diff(instance, tt.args.instances[i]))
					return
				}
			}
		})
	}
}

func TestClient_GetInstancesByRole(t *testing.T) {
	type fields struct {
		client *Client
	}
	type args struct {
		ctx       context.Context
		accountID string
		clusterID string
		role      ccetypes.ClusterRole
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*Instance
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleNode),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "user-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				} {
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				accountID: "account-id",
				clusterID: "cluster-id",
				role:      ccetypes.ClusterRoleMaster,
			},
			want: []*Instance{
				{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
				},
				{
					Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
				},
			},
			wantErr: false,
		},
		{
			name: "查询返回为空",
			fields: func() fields {
				ctx := context.TODO()

				client, err := newUnitTestClient(ctx)
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				accountID: "account-id",
				clusterID: "cluster-id",
				role:      ccetypes.ClusterRoleMaster,
			},
			want:    []*Instance{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.fields.client
			defer unitTestDBGC(tt.args.ctx, c)

			got, err := c.GetInstancesByRole(tt.args.ctx, tt.args.accountID, tt.args.clusterID, tt.args.role)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("Client.GetInstancesByRole() error = %v, wantErr %v", err, tt.wantErr)
			}

			if tt.wantErr == true {
				t.Errorf("Client.GetInstancesByRole() failed, wantErr=true got=false")
				return
			}

			if !cmpInstanceList(got, tt.want) {
				t.Errorf("Client.GetInstancesByRole() Got=%s", utils.ToJSON(got))
				t.Errorf("Client.GetInstancesByRole() Want=%s", utils.ToJSON(tt.want))
				t.Errorf("Client.GetInstancesByRole() failed, Diff=%s", cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestClient_GetInstancesByInstanceGroupID(t *testing.T) {
	type fields struct {
		client *Client
	}

	testCases := []struct {
		name               string
		fields             fields
		accountID          string
		clusterID          string
		instanceGroupID    string
		pageSize           int
		pageNo             int
		expectedErr        bool
		expectedItemCount  int
		expectedTotalCount int
	}{
		{
			name: "正常读取",
			fields: func() fields {
				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-1"
					instance.Spec.InstanceGroupName = "instanceGroupID-1"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-2", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-2", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-2"
					instance.Spec.InstanceGroupName = "instanceGroupID-2"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				return fields{
					client: client,
				}
			}(),
			accountID:          "account-id",
			clusterID:          "cluster-id",
			instanceGroupID:    "instanceGroupID-1",
			expectedErr:        false,
			expectedItemCount:  2,
			expectedTotalCount: 2,
		},
		{
			name: "正常读取，分页",
			fields: func() fields {
				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-2", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-3", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-1"
					instance.Spec.InstanceGroupName = "instanceGroupID-1"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				return fields{
					client: client,
				}
			}(),
			accountID:          "account-id",
			clusterID:          "cluster-id",
			instanceGroupID:    "instanceGroupID-1",
			expectedErr:        false,
			pageNo:             1,
			pageSize:           2,
			expectedItemCount:  2,
			expectedTotalCount: 4,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			c := tc.fields.client
			defer unitTestDBGC(context.Background(), c)

			got, err := c.GetInstancesByInstanceGroupID(context.Background(), tc.accountID, tc.clusterID, tc.instanceGroupID, "", "", "", "", tc.pageSize, tc.pageNo)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)

			}

			if tc.expectedItemCount != len(got.Items) {
				t.Errorf("expected item count: %d, actual: %d", tc.expectedItemCount, len(got.Items))
			}
			if tc.expectedTotalCount != got.TotalCount {
				t.Errorf("expected total count: %d, actual: %d", tc.expectedTotalCount, got.TotalCount)
			}
		})
	}
}

func TestClient_GetInstancesWithBatchQueryByInstanceGroupID(t *testing.T) {
	type fields struct {
		client *Client
	}

	testCases := []struct {
		name               string
		fields             fields
		accountID          string
		clusterID          string
		instanceGroupID    string
		pageSize           int
		pageNo             int
		expectedErr        bool
		expectedItemCount  int
		expectedTotalCount int
	}{
		{
			name: "正常读取",
			fields: func() fields {
				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-1"
					instance.Spec.InstanceGroupName = "instanceGroupID-1"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-2", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-2", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-2"
					instance.Spec.InstanceGroupName = "instanceGroupID-2"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				return fields{
					client: client,
				}
			}(),
			accountID:          "account-id",
			clusterID:          "cluster-id",
			instanceGroupID:    "instanceGroupID-1",
			expectedErr:        false,
			expectedItemCount:  2,
			expectedTotalCount: 2,
		},
		{
			name: "正常读取，分页",
			fields: func() fields {
				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				for _, instance := range []*Instance{
					{
						Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-1", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-2", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
					},
					{
						Spec:   instanceSpec("instance-name-3", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				} {
					instance.Spec.InstanceGroupID = "instanceGroupID-1"
					instance.Spec.InstanceGroupName = "instanceGroupID-1"
					if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
						t.Fatalf("CreateInstance failed: %s", err)
					}
				}

				return fields{
					client: client,
				}
			}(),
			accountID:          "account-id",
			clusterID:          "cluster-id",
			instanceGroupID:    "instanceGroupID-1",
			expectedErr:        false,
			pageNo:             1,
			pageSize:           2,
			expectedItemCount:  2,
			expectedTotalCount: 4,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			c := tc.fields.client
			defer unitTestDBGC(context.Background(), c)

			got, err := c.GetInstancesWithBatchQueryByInstanceGroupID(context.Background(), tc.accountID, tc.clusterID, tc.instanceGroupID, "", "", "", "", tc.pageSize, tc.pageNo)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)

			}

			if tc.expectedItemCount != len(got.Items) {
				t.Errorf("expected item count: %d, actual: %d", tc.expectedItemCount, len(got.Items))
			}
			if tc.expectedTotalCount != got.TotalCount {
				t.Errorf("expected total count: %d, actual: %d", tc.expectedTotalCount, got.TotalCount)
			}
		})
	}
}

func TestClient_GetInstanceByCCEID(t *testing.T) {
	type fields struct {
		client *Client
	}
	type args struct {
		ctx           context.Context
		cceInstanceID string
		accountID     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *Instance
		wantErr bool
	}{
		{
			name: "正常，能够查询到",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				instance := &Instance{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
				}
				if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want: &Instance{
				Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
				Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
			},
			wantErr: false,
		},
		{
			name: "not exist with instancePhase deleted",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				instance := &Instance{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseDeleted),
				}
				if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "not exist",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.fields.client
			defer unitTestDBGC(context.Background(), c)

			got, err := c.GetInstanceByCCEID(tt.args.ctx, tt.args.cceInstanceID, tt.args.accountID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceByCCEID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInstanceByCCEID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestClient_GetInstanceByCCEIDWithDeleted 测试函数Client_GetInstanceByCCEIDWithDeleted，用于查询指定CCE实例ID的CCE实例信息，包括已删除状态的实例。
// 参数t是*testing.T类型，用于存储对单元测试的引用。
// 返回值没有，使用defer unitTestDBGC(context.Background(), c)来清理数据库。
func TestClient_GetInstanceByCCEIDWithDeleted(t *testing.T) {
	type fields struct {
		client *Client
	}
	type args struct {
		ctx           context.Context
		cceInstanceID string
		accountID     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *Instance
		wantErr bool
	}{
		{
			name: "正常，能够查询到",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				instance := &Instance{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
				}
				if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want: &Instance{
				Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
				Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
			},
			wantErr: false,
		},
		{
			name: "exist with instancePhase deleted",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				instance := &Instance{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseDeleted),
				}
				if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want: &Instance{
				Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
				Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseDeleted),
			},
			wantErr: false,
		},
		{
			name: "not exist",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.fields.client
			defer unitTestDBGC(context.Background(), c)

			got, err := c.GetInstanceByCCEID(tt.args.ctx, tt.args.cceInstanceID, tt.args.accountID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceByCCEID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInstanceByCCEID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetInstanceByCCEIDWithoutStatusDeleted(t *testing.T) {
	type fields struct {
		client *Client
	}
	type args struct {
		ctx           context.Context
		cceInstanceID string
		accountID     string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *Instance
		wantErr bool
	}{
		{
			name: "正常，能够查询到",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				instance := &Instance{
					Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
					Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
				}
				if err := client.db.Table(client.cceInstanceTableName).Create(instance).Error; err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want: &Instance{
				Spec:   instanceSpec("instance-name-0", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
				Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
			},
			wantErr: false,
		},
		{
			name: "not exist",
			fields: func() fields {

				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}
				return fields{
					client: client,
				}
			}(),
			args: args{
				ctx:           context.Background(),
				cceInstanceID: "instance-id-0",
				accountID:     "account-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.fields.client
			defer unitTestDBGC(context.Background(), c)

			got, err := c.GetInstanceByCCEIDWithoutStatusDeleted(tt.args.ctx, tt.args.cceInstanceID, tt.args.accountID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceByCCEID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetInstanceByCCEID() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_UpdatePartInstanceSpec(t *testing.T) {
	type fields struct {
		client     *Client
		InstanceID string
	}

	testCases := []struct {
		name        string
		fields      fields
		accountID   string
		spec        *ccetypes.InstanceSpec
		expectedErr bool
	}{
		{
			name: "正常更新",
			fields: func() fields {
				client, err := newUnitTestClient(context.Background())
				if err != nil {
					t.Fatalf("newUnitTestClient failed: %s", err)
				}

				instances := []*Instance{
					{
						Spec:   instanceSpec("instance-name-3", "", "account-id", "cluster-id", ccetypes.ClusterRoleMaster),
						Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
					},
				}
				instances[0].Spec.InstanceGroupID = "instanceGroupID-1"
				instances[0].Spec.InstanceGroupName = "instanceGroupID-1"
				ids, err := client.CreateInstances(context.Background(), "cce-cluster", instances)
				if err != nil {
					t.Fatalf("CreateInstance failed: %s", err)
				}

				return fields{
					client:     client,
					InstanceID: ids[0],
				}
			}(),
			accountID: "account-id",
			spec: &ccetypes.InstanceSpec{
				Handler:        "chenhuan",
				InstanceName:   "",
				RuntimeType:    ccetypes.RuntimeTypeDocker,
				RuntimeVersion: "18.9.01",
				ClusterRole:    ccetypes.ClusterRoleNode,
				UserID:         "user-id",
				AccountID:      "account-id",
				MasterType:     ccetypes.MasterTypeCustom,
				Existed:        true,
				MachineType:    ccetypes.MachineTypeBCC,
				InstanceType:   bcc.InstanceTypeC2,
				BBCOption: ccetypes.BBCOption{
					ReserveData: true,
					RaidID:      "raid-id",
					SysDiskSize: 1000,
				},
				VPCConfig: ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "10.0.0.0/8",
					VPCSubnetCIDRIPv6: "F001:1e2r:131f:112341",
					AvailableZone:     internalvpc.ZoneA,
				},
				InstanceResource: ccetypes.InstanceResource{
					CPU:           10,
					MEM:           128,
					RootDiskType:  bcc.StorageTypeCloudHP1,
					RootDiskSize:  100,
					LocalDiskSize: 40,
					CDSList: ccetypes.CDSConfigList{
						ccetypes.CDSConfig{
							Path:        "path",
							StorageType: bcc.StorageTypeDCCSSD,
							CDSSize:     100000,
							SnapshotID:  "snapshort-id",
						},
						ccetypes.CDSConfig{
							Path:        "path",
							StorageType: bcc.StorageTypeDCCSSD,
							CDSSize:     100000,
							SnapshotID:  "snapshort-id",
						},
					},
					GPUType:  bcc.GPUTypeK40,
					GPUCount: 8,
				},
				ImageID:   "image-id",
				ImageUUID: "image-uuid",
				InstanceOS: ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "7.5",
					OSArch:    "os-arch",
					OSBuild:   "os-build",
				},
				NeedEIP: true,
				EIPOption: ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    1000,
				},
				AdminPassword:        "admin-password",
				SSHKeyID:             "ssh-key-id",
				InstanceChargingType: bcc.PaymentTimingPrepaid,
				InstancePreChargingOption: ccetypes.InstancePreChargingOption{
					PurchaseTime:      10,
					AutoRenew:         true,
					AutoRenewTimeUnit: "auto-renew-time-unit",
					AutoRenewTime:     10,
				},
				DeleteOption: &ccetypes.DeleteOption{
					MoveOut:           false,
					DeleteResource:    true,
					DeleteCDSSnapshot: true,
				},
				Tags: ccetypes.TagList{
					ccetypes.Tag{
						TagKey:   "name",
						TagValue: "chenhuan",
					},
					ccetypes.Tag{
						TagKey:   "name",
						TagValue: "chenhuan",
					},
				},
				Labels: map[string]string{
					"instance-group-id": "xxx",
				},
				Taints: []corev1.Taint{
					{
						Key:    "node",
						Value:  "node-1",
						Effect: corev1.TaintEffectNoExecute,
					},
				},
			},
			expectedErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			c := tc.fields.client
			defer unitTestDBGC(context.Background(), c)

			tc.spec.CCEInstanceID = tc.fields.InstanceID
			err := c.UpdatePartInstanceSpec(context.Background(), tc.accountID, tc.fields.InstanceID, tc.spec)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}

			if err != nil {
				instance, err := c.GetInstanceByCCEID(context.Background(), tc.accountID, tc.fields.InstanceID)
				if err != nil {
					t.Errorf("failed to get instance: %v", err)
					return
				}
				if !cmp.Equal(instance.Spec, tc.spec) {
					t.Errorf("diff: %s", cmp.Diff(instance.Spec, tc.spec))
				}
			}
		})
	}
}

func Test_getInstancesByBatchQuery(t *testing.T) {
	type args struct {
		ctx         context.Context
		keywordType string
		clusterRole ccetypes.ClusterRole
		becRegion   string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常,node",
			args: args{
				ctx:         context.TODO(),
				keywordType: "name",
				clusterRole: "node",
				becRegion:   "A",
			},
			want:    "account_id = ? AND cluster_id = ? AND name IN (?) AND instance_phase <> ? AND cluster_role = 'node' AND bec_region = ?",
			wantErr: false,
		},
		{
			name: "正常,master",
			args: args{
				ctx:         context.TODO(),
				keywordType: "name",
				clusterRole: "master",
				becRegion:   "A",
			},
			want:    "account_id = ? AND cluster_id = ? AND name IN (?) AND instance_phase <> ? AND cluster_role = 'master' AND bec_region = ?",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getInstancesByBatchQuery(tt.args.ctx, tt.args.keywordType, tt.args.clusterRole, tt.args.becRegion)
			if (err != nil) != tt.wantErr {
				t.Errorf("getInstancesByBatchQuery() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getInstancesByBatchQuery() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getInstanceField(t *testing.T) {
	tests := []struct {
		name      string
		fieldName string
		want      string
	}{
		{
			name:      "正常,clusterRole",
			fieldName: "clusterRole",
			want:      "cluster_role",
		},
		{
			name:      "正常,instanceName",
			fieldName: "instanceName",
			want:      "instance_name",
		},
		{
			name:      "正常,instance_name",
			fieldName: "instance_name",
			want:      "instance_name",
		},
		{
			name:      "正常,instanceID",
			fieldName: "instanceID",
			want:      "instance_id",
		},
		{
			name:      "正常,hostname",
			fieldName: "hostname",
			want:      "hostname",
		},
		{
			name:      "正常,vpcIP",
			fieldName: "vpcIP",
			want:      "vpc_ip",
		},
		{
			name:      "正常,createdAt",
			fieldName: "createdAt",
			want:      "created_at",
		},
		{
			name:      "正常,instanceGroupID",
			fieldName: "instanceGroupID",
			want:      "instance_group_id",
		},
		{
			name:      "正常,instanceGroupName",
			fieldName: "instanceGroupName",
			want:      "instance_group_name",
		},
		{
			name:      "正常,instanceId",
			fieldName: "instanceId",
			want:      "instance_id",
		},
		{
			name:      "正常,fixIp",
			fieldName: "fixIp",
			want:      "vpc_ip",
		},
		{
			name:      "正常,becRegion",
			fieldName: "becRegion",
			want:      "bec_region",
		},
		{
			name:      "正常,serviceID",
			fieldName: "serviceID",
			want:      "order_id",
		},
		{
			name:      "异常,不存在字段",
			fieldName: "nonExistentField",
			want:      "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getInstanceField(tt.fieldName); got != tt.want {
				t.Errorf("getInstanceField() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_GetInstanceEx(t *testing.T) {
	var (
		ast        = assert.New(t)
		ctx        = context.Background()
		accountID  = uuid.NewString()
		clusterID1 = uuid.NewString()
	)
	client, err := newUTClientWithLocalDB(context.Background())
	if err != nil {
		t.Fatalf("newUTClientWithLocalDB failed: %s", err)
	}
	instances := []*Instance{
		{
			Spec:   instanceSpec("instance-name-0", "", accountID, clusterID1, ccetypes.ClusterRoleMaster),
			Status: instanceStatus("instance-id-0", ccetypes.InstancePhaseRunning),
		},
		{
			Spec:   instanceSpec("instance-name-1", "", accountID, clusterID1, ccetypes.ClusterRoleNode),
			Status: instanceStatus("instance-id-1", ccetypes.InstancePhaseRunning),
		},
		{
			Spec:   instanceSpec("instance-name-2", "", accountID, clusterID1, ccetypes.ClusterRoleNode),
			Status: instanceStatus("instance-id-2", ccetypes.InstancePhaseRunning),
		},
	}

	for i, item := range instances {
		item.Spec.CCEInstanceID = fmt.Sprintf("cce-%d", i)
		item.Spec.Existed = false
		_, err := client.CreateInstances(ctx, clusterID1, []*Instance{item})
		ast.Nil(err)
		time.Sleep(time.Millisecond * 50)
	}

	option := InstanceListOption{}
	// 不传accoutID报错
	_, err = client.GetInstanceEx(ctx, option)
	ast.NotNil(err)

	// 不传clusterID报错
	option.AccountID = accountID
	_, err = client.GetInstanceEx(ctx, option)
	ast.NotNil(err)

	// 传默认参数查节点
	option.ClusterID = clusterID1
	queryRes, err := client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryRes.Items, 3)
	ast.Equal(queryRes.TotalCount, 3)

	// 传分页信息
	option.PageNo = 1
	option.PageSize = 2
	queryRes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryRes.Items, 2)

	// 传默认参数查master
	option.ClusterRole = ccetypes.ClusterRoleMaster
	queryMasters, err := client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryMasters.Items, 1)

	// 传默认参数查node
	option.ClusterRole = ccetypes.ClusterRoleNode
	queryNodes, err := client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)
	ast.Equal(queryNodes.Items[0].Spec.InstanceName, "instance-name-1")
	ast.Equal(queryNodes.Items[1].Spec.InstanceName, "instance-name-2")

	// 传region查node
	option.BecRegion = "bj"
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)
	ast.Equal(queryNodes.Items[0].Spec.InstanceName, "instance-name-1")

	// 传orderBy查node
	option.OrderBy = "instance_name"
	option.Order = "DESC"
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)
	ast.Equal(queryNodes.Items[0].Spec.InstanceName, "instance-name-2")
	ast.Equal(queryNodes.Items[1].Spec.InstanceName, "instance-name-1")

	// 传orderBy查node
	option.OrderBy = "instanceName"
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)

	// 默认按创建时间降序
	option.OrderBy = ""
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)
	ast.Equal(queryNodes.Items[0].Spec.InstanceName, "instance-name-2")

	// 传keyword查node
	option.KeywordType = "instance_name"
	option.Keyword = "instance-name"
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)

	// 传keyword查node
	option.KeywordType = "instanceName"
	option.Keyword = "instance-name-1,"
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 1)
	ast.Equal(queryNodes.Items[0].Spec.InstanceName, "instance-name-1")

	// 传keyword=,
	option.Keyword = ","
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 0)

	// 传cceInstanceIDs, 优先使用
	option.CCEInstanceIDs = []string{"instance-id-1", "instance-id-2"}
	queryNodes, err = client.GetInstanceEx(ctx, option)
	ast.Nil(err)
	ast.Len(queryNodes.Items, 2)

}

// TestClient_GetInstanceEx_ChargingTypeFilter 专门测试计费方式筛选功能
func TestClient_GetInstanceEx_ChargingTypeFilter(t *testing.T) {
	var (
		ast       = assert.New(t)
		ctx       = context.Background()
		accountID = uuid.NewString()
		clusterID = uuid.NewString()
	)

	client, err := newUTClientWithLocalDB(context.Background())
	if err != nil {
		t.Fatalf("newUTClientWithLocalDB failed: %s", err)
	}

	// 创建不同计费方式的测试实例
	instances := []*Instance{
		{
			Spec:   instanceSpecWithChargingType("prepaid-instance-1", "", accountID, clusterID, ccetypes.ClusterRoleNode, bcc.PaymentTimingPrepaid),
			Status: instanceStatus("prepaid-id-1", ccetypes.InstancePhaseRunning),
		},
		{
			Spec:   instanceSpecWithChargingType("prepaid-instance-2", "", accountID, clusterID, ccetypes.ClusterRoleNode, bcc.PaymentTimingPrepaid),
			Status: instanceStatus("prepaid-id-2", ccetypes.InstancePhaseRunning),
		},
		{
			Spec:   instanceSpecWithChargingType("postpaid-instance", "", accountID, clusterID, ccetypes.ClusterRoleNode, bcc.PaymentTimingPostpaid),
			Status: instanceStatus("postpaid-id", ccetypes.InstancePhaseRunning),
		},
		{
			Spec:   instanceSpecWithChargingType("bid-instance", "", accountID, clusterID, ccetypes.ClusterRoleNode, bcc.PaymentTimingBid),
			Status: instanceStatus("bid-id", ccetypes.InstancePhaseRunning),
		},
	}

	// 创建实例
	for i, instance := range instances {
		instance.Spec.CCEInstanceID = fmt.Sprintf("cce-charging-test-%d", i)
		instance.Spec.Existed = false
		_, err := client.CreateInstances(ctx, clusterID, []*Instance{instance})
		ast.Nil(err)
		time.Sleep(time.Millisecond * 50)
	}

	// 测试用例
	testCases := []struct {
		name          string
		chargingType  *string
		expectedLen   int
		expectedNames []string
	}{
		{
			name:          "筛选Prepaid实例",
			chargingType:  func() *string { s := "Prepaid"; return &s }(),
			expectedLen:   2,
			expectedNames: []string{"prepaid-instance-1", "prepaid-instance-2"},
		},
		{
			name:          "筛选Postpaid实例",
			chargingType:  func() *string { s := "Postpaid"; return &s }(),
			expectedLen:   1,
			expectedNames: []string{"postpaid-instance"},
		},
		{
			name:          "筛选bid实例",
			chargingType:  func() *string { s := "bid"; return &s }(),
			expectedLen:   1,
			expectedNames: []string{"bid-instance"},
		},
		{
			name:          "筛选无效计费方式",
			chargingType:  func() *string { s := "invalid"; return &s }(),
			expectedLen:   0,
			expectedNames: []string{},
		},
		{
			name:          "不筛选计费方式（返回所有）",
			chargingType:  nil,
			expectedLen:   4,
			expectedNames: []string{"prepaid-instance-1", "prepaid-instance-2", "postpaid-instance", "bid-instance"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			option := InstanceListOption{
				AccountID:    accountID,
				ClusterID:    clusterID,
				ChargingType: tc.chargingType,
			}

			result, err := client.GetInstanceEx(ctx, option)
			ast.Nil(err)
			ast.Len(result.Items, tc.expectedLen)

			if tc.expectedLen > 0 {
				actualNames := make([]string, len(result.Items))
				for i, item := range result.Items {
					actualNames[i] = item.Spec.InstanceName
				}

				// 验证返回的实例名称包含期望的名称
				for _, expectedName := range tc.expectedNames {
					found := false
					for _, actualName := range actualNames {
						if actualName == expectedName {
							found = true
							break
						}
					}
					ast.True(found, "Expected instance name %s not found in results", expectedName)
				}
			}
		})
	}
}
