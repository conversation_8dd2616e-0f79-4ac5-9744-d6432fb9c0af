# CCE 代码开发提交流程规范

## 1. 需求管理阶段

### 1.1 从产品拿到Story卡片
- 产品经理在项目管理系统中创建Story卡片
- 开发人员接收并理解需求内容
- 确认Story的验收标准和优先级

### 1.2 新建Task子卡片
- 在Story下创建具体的开发Task
- 将大的Story拆分为可执行的小任务
- 每个Task应该是一个独立的开发单元

### 1.3 复制类型标题
- 从Task卡片中复制标准化的标题格式
- 标题格式通常为：`[项目代号-编号] [类型] 具体描述`
- 例如：`CCE-23537 [Task] 添加已有节点实例选择过滤"PFS挂载服务/挂载点下的管理节点"`

## 2. 分支管理和开发准备

### 2.1 分支命名规范
根据 CCE 分支开发规范，分支命名应遵循以下格式：

| **提交类型** | **说明** | **分支命名示例** |
|-------------|----------|-----------------|
| feature | 需求功能迭代，包括白名单、chart文件的变更 | feature-support-cluster-update |
| bugfix | 在准出后发现的问题，以及线上bug的修复 | bugfix-cce-lb-controller-1-29-2 |
| doc | 文档、sop、备份等 | doc-custom-cluster-migration-managed-cluster-sop |
| test | 测试回归、补充单测等 | test-add-lb-controller-test |
| chore | 构建过程或辅助工具的变动 | chore-add-network-tool |

### 2.2 在 iCode 上创建分支（重要！）
**⚠️ 注意：必须先在 iCode 上创建分支，再在本地开发**

1. 登录 [百度 iCode](https://icode.baidu.com)
2. 进入 cce-stack 项目
3. 从 master 分支创建新的功能分支
4. 分支名称按照上述命名规范

### 2.3 本地拉取分支并开发
```bash
# 切换到master分支并拉取最新代码
git checkout master
git pull origin master

# 拉取远程创建的功能分支
git fetch origin
git checkout feature-your-branch-name

# 或者创建本地分支跟踪远程分支
git checkout -b feature-your-branch-name origin/feature-your-branch-name
```

### 2.4 如果忘记先创建分支的补救措施
如果已经在本地开发但忘记先在 iCode 创建分支：

```bash
# 1. 暂存当前修改
git stash

# 2. 切换到 master 并拉取最新代码
git checkout master
git pull origin master

# 3. 在 iCode 上创建分支后，拉取该分支
git fetch origin
git checkout feature-your-branch-name

# 4. 恢复之前的修改
git stash pop
```

## 3. 代码开发阶段

### 3.1 开发代码
- 按照需求进行代码开发
- 遵循项目的编码规范
- 编写必要的单元测试
- 进行本地测试验证

### 3.2 本地构建和测试
```bash
# 运行单元测试
go test ./...

# 本地构建验证（Go 项目）
./build.sh

# 或者使用新的构建脚本
./new_build.sh

# 代码格式检查
go fmt ./...
go vet ./...
```

### 3.3 Docker 镜像构建（用于测试部署）
```bash
# 构建特定服务的 Docker 镜像
sh build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service cce-service-dev zhangxiuqi

# 构建命令格式：
# sh build_docker_image.sh [服务路径] [服务名] [镜像仓库] [标签]
```

## 4. Git 提交阶段

### 4.1 查看修改状态
```bash
# 查看当前修改状态
git status

# 查看具体修改内容
git diff

# 查看暂存区的修改
git diff --staged
```

### 4.2 添加修改文件
```bash
# 添加指定文件
git add path/to/modified/file.go

# 添加多个文件
git add file1.go file2.go

# 添加所有修改（谨慎使用）
git add .

# 交互式添加（推荐）
git add -p
```

### 4.3 提交代码
```bash
# 提交代码，使用从Task卡片复制的标题
git commit -m "CCE-23537 [Task] 添加已有节点实例选择过滤PFS挂载服务/挂载点下的管理节点"

# 如果需要更详细的提交信息
git commit -m "CCE-23537 [Task] 添加已有节点实例选择过滤PFS挂载服务/挂载点下的管理节点

- 在 instance_controller.go 中添加 XPU 驱动检查逻辑
- 更新相关的类型定义和常量
- 添加必要的单元测试"
```

### 4.4 推送到远程仓库

#### 方式一：推送到功能分支（推荐）
```bash
# 首次推送功能分支
git push --set-upstream origin feature-your-branch-name

# 后续推送
git push origin feature-your-branch-name
```

#### 方式二：通过 Gerrit 代码评审（百度 iCode 特有）
```bash
# 推送代码评审到master分支
git push origin HEAD:refs/for/master

# 推送到特定分支的代码评审
git push origin HEAD:refs/for/feature-your-branch-name
```

## 5. 代码评审阶段

### 5.1 创建 Pull Request/Merge Request
- 在 iCode 上创建代码评审请求
- 填写详细的变更说明，包括：
  - 功能描述
  - 测试方法
  - 影响范围
  - 相关文档链接
- 指定合适的评审人员

### 5.2 响应评审意见
```bash
# 如果需要修改代码
git add modified-files
git commit -m "fix: 根据评审意见修改XXX问题"

# 推送到功能分支
git push origin feature-your-branch-name

# 或者推送到 Gerrit 评审
git push origin HEAD:refs/for/master
```

### 5.3 合并到主分支
- 评审通过后，由有权限的人员合并到 master 分支
- 或者评审系统自动合并
- 合并后流水线会自动删除功能分支

## 6. 测试环境部署验证

### 6.1 CCE 测试环境概览
CCE 有多套测试环境，根据开发阶段选择合适的环境：

| **测试环境** | **用途** | **灰度环境** |
|-------------|----------|-------------|
| 开发分支的 changepipeline | 自测环境（容易产生冲突） | bjtest |
| 提测分支，QA 的测试环境 | 提测分支的回归执行 | bdtest |
| master 分支 | 分支同步 master 后的预发环境 | gztest |

### 6.2 构建和推送镜像
```bash
# 构建 cluster-controller 镜像（包含 XPU 驱动检查逻辑）
sh build_docker_image.sh ./services/cluster/cluster-controller cce-cluster-controller cce-service-dev zhangxiuqi

# 构建 cluster-service 镜像
sh build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service cce-service-dev zhangxiuqi

# 构建命令会执行以下步骤：
# 1. 编译 Go 二进制文件
# 2. 构建 Docker 镜像
# 3. 推送到 CCR 镜像仓库

# 验证镜像构建成功
docker images | grep zhangxiuqi
```

### 6.3 在测试环境部署
#### 6.3.1 登录测试环境
```bash
# 登录测试环境（需要先申请权限）
ssh <EMAIL>
```

#### 6.3.2 查看当前部署状态
```bash
# 查看 CCE 系统的 Pod 状态
kubectl get pods -n cce-system

# 查看特定服务的详细信息
kubectl get pod -n cce-system cce-cluster-service-xxx -o yaml

# 查看 DaemonSet 状态
kubectl -n cce-system get ds

# 查看 DaemonSet 详细状态和镜像信息
kubectl -n cce-system get ds -o wide

# 检查特定 DaemonSet 的镜像版本
kubectl -n cce-system describe ds cce-cluster-service | grep Image
kubectl -n cce-system describe ds cce-cluster-controller | grep Image
```

#### 6.3.3 更新服务镜像
```bash
# 编辑 cluster-controller DaemonSet 配置，更新镜像地址
kubectl -n cce-system edit ds cce-cluster-controller

# 在编辑器中找到 image 字段，替换为你的镜像：
# image: ccr-registry.baidubce.com/cce-service-dev/cce-cluster-controller:zhangxiuqi

# 编辑 cluster-service DaemonSet 配置，更新镜像地址
kubectl -n cce-system edit ds cce-cluster-service

# 在编辑器中找到 image 字段，替换为你的镜像：
# image: ccr-registry.baidubce.com/cce-service-dev/cce-cluster-service:zhangxiuqi
```

#### 6.3.4 验证部署结果
```bash
# 查看 Pod 重启状态
kubectl get pods -n cce-system -w

# 查看 cluster-controller 服务日志
kubectl logs -n cce-system cce-cluster-controller-xxx -f

# 查看 cluster-service 服务日志
kubectl logs -n cce-system cce-cluster-service-xxx -f

# 检查服务健康状态
kubectl describe pod -n cce-system cce-cluster-controller-xxx
kubectl describe pod -n cce-system cce-cluster-service-xxx

# 验证镜像版本是否正确更新
kubectl -n cce-system describe ds cce-cluster-controller | grep Image
kubectl -n cce-system describe ds cce-cluster-service | grep Image
```

### 6.3.5 日志查看与验证
- 日志路径
  - 进程参数指定：`-log-file=/home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log`
  - 通过 hostPath 映射到宿主机同路径：`/home/<USER>/cce/cce-cluster-service/logs/`

- 宿主机直接查看（推荐）
```bash
ssh root@<Pod所在节点IP>
# 最近关键日志（示例：autoscalerEnabled 新增逻辑）
grep -nE "autoscalerEnabled filter applied|invalid autoscalerEnabled parameter" /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | tail

# 实时过滤
tail -F /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | grep -E "autoscalerEnabled filter applied|invalid autoscalerEnabled parameter"

# 按 requestID 精确过滤
grep "<requestID>" /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log
```

- 容器内查看（不依赖宿主机权限）
```bash
kubectl -n cce-system exec -it <pod> -c cce-cluster-service -- \
  sh -c 'tail -F /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | grep -E "autoscalerEnabled filter applied|invalid autoscalerEnabled parameter"'

# 或一次性拉取最近相关行
kubectl -n cce-system exec -it <pod> -c cce-cluster-service -- \
  sh -c 'grep -nE "autoscalerEnabled filter applied|invalid autoscalerEnabled parameter" /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | tail'
```

- 标准输出查看（若应用也打到 stdout）
```bash
kubectl -n cce-system logs -f <pod> | grep autoscalerEnabled
```

- 直连服务触发日志（hostNetwork + hostPort）
```bash
# 获取 Pod 节点与端口
kubectl -n cce-system get pod <pod> -o wide        # 记下 NODE/IP
# 发起请求（示例）
curl -s "http://<NODE_IP>:8793/api/cce/service/v2/cluster/<clusterID>/instancegroups?pageNo=1&pageSize=10&autoscalerEnabled=true" | jq .
```

- Port-forward（无法直连宿主机时）
```bash
kubectl -n cce-system port-forward <pod> 19090:8793
curl -s "http://127.0.0.1:19090/api/cce/service/v2/cluster/<clusterID>/instancegroups?autoscalerEnabled=false" | jq .
```

- 常见精准筛选样例（避免被 HTTP Dump 命中）
```bash
# 仅看新增两类日志
grep -nE "autoscalerEnabled filter applied|invalid autoscalerEnabled parameter" /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | tail

# 精准到源文件行号（示例：instancegroup_controller.go:376/378）
grep -nE "instancegroup_controller\.go:37(6|8).*(autoscalerEnabled filter applied|invalid autoscalerEnabled parameter)" /home/<USER>/cce/cce-cluster-service/logs/cce-cluster-service.log | tail
```

## 7. 功能测试和验证

### 7.1 API 测试
```bash
# 测试 CCE API 接口
curl -X GET "http://test-endpoint/api/v1/clusters" \
  -H "Authorization: Bearer your-token"

# 使用 CCE 资源账号进行测试
# 登录地址：https://login.bce.baidu.com/login?account=cce_00
```

### 7.2 集群功能测试
- 创建测试集群验证新功能
- 测试节点组的扩缩容功能
- 验证实例模板的配置生效
- 检查相关的事件和日志

### 7.3 回归测试
- 确保现有功能不受影响
- 运行自动化测试套件
- 验证关键业务流程

## 8. 发布和上线流程

### 8.1 准出标准
- [ ] 功能测试通过
- [ ] 代码评审通过
- [ ] 单元测试覆盖率达标
- [ ] 集成测试通过
- [ ] 性能测试通过（如需要）
- [ ] 安全扫描通过

### 8.2 发布流程
1. **准出后发布**：分支准出后，需要发布才能合入 master
2. **同步主干**：发布需要同步主干代码
3. **合入主干**：分支发布后，合入主干
4. **自动清理**：合入后流水线会自动删除该分支

### 8.3 发布流水线
- 开发流水线：[DevelopPipline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/1737672/builds/list?branchName=branches)
- 上线流水线：[BranchPipeline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/635522/builds/list?branchName=branches)
- CCE组件发布流水线：[K8SPluginReleasePipeline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/716430/builds/list?branchName=branches)

### 8.4 上线时间要求
- **后端功能**：QA 测试准出后，RD 需要在每周二晚上 7 点前把代码合入 master
- **统一发版**：值班同学统一发起上线，后端全地域上线完成后，FE 同学再上线前端代码
- **发版频率**：CCE 采用每周统一发版上线的模式

## 9. 常用 Git 命令参考

### 9.1 分支操作
```bash
# 查看所有分支
git branch -a

# 切换分支
git checkout branch-name

# 创建并切换到新分支
git checkout -b new-branch-name

# 删除本地分支
git branch -d branch-name

# 删除远程分支
git push origin --delete branch-name

# 重命名分支
git branch -m old-name new-name
```

### 9.2 撤销操作
```bash
# 撤销工作区修改
git restore file-name

# 撤销暂存区修改
git restore --staged file-name

# 撤销最后一次提交（保留修改）
git reset HEAD~1

# 撤销最后一次提交（丢弃修改）
git reset --hard HEAD~1

# 撤销到指定提交
git reset --hard commit-hash
```

### 9.3 查看历史和状态
```bash
# 查看提交历史
git log --oneline

# 查看某个文件的修改历史
git log -p file-name

# 查看分支图
git log --graph --oneline --all

# 查看远程分支状态
git remote -v
git branch -r
```

### 9.4 暂存操作
```bash
# 暂存当前修改
git stash

# 暂存时添加描述
git stash save "work in progress on feature X"

# 查看暂存列表
git stash list

# 恢复最近的暂存
git stash pop

# 恢复指定的暂存
git stash apply stash@{0}

# 删除暂存
git stash drop stash@{0}
```

## 10. 注意事项和最佳实践

### 10.1 提交规范
- **提交信息格式**：必须使用 Task 卡片的标准格式
- **提交粒度**：每次提交应该是一个完整的功能点
- **代码清理**：避免提交调试代码、临时文件和个人配置
- **提交频率**：小步快跑，频繁提交，便于问题定位

### 10.2 分支管理最佳实践
- **分支命名**：使用有意义的分支名，遵循命名规范
- **分支生命周期**：及时删除已合并的功能分支
- **代码同步**：定期同步 master 分支的最新代码
- **分支创建**：⚠️ **必须先在 iCode 上创建分支，再在本地开发**

### 10.3 代码质量保证
- **自测要求**：提交前进行充分的自测
- **编码规范**：确保代码符合 Go 项目规范
- **测试覆盖**：编写必要的单元测试和集成测试
- **文档更新**：及时更新相关文档和注释

### 10.4 权限和安全
- **账户使用**：使用正确的用户账户进行操作
- **权限申请**：提前申请必要的测试环境权限
- **评审流程**：不要绕过代码评审流程
- **敏感信息**：不要在代码中硬编码敏感信息

### 10.5 环境管理
- **资源账号**：合理使用 CCE 测试资源账号
- **环境隔离**：在正确的测试环境进行验证
- **镜像管理**：使用个人标签构建测试镜像

## 11. 故障排除和常见问题

### 11.1 分支相关问题
```bash
# 忘记先在 iCode 创建分支的补救
git stash                    # 暂存修改
git checkout master          # 切换到 master
git pull origin master       # 拉取最新代码
# 在 iCode 上创建分支后
git fetch origin             # 获取远程分支
git checkout feature-branch  # 切换到新分支
git stash pop               # 恢复修改

# 分支落后于 master 的处理
git checkout master
git pull origin master
git checkout feature-branch
git rebase master           # 或者 git merge master
```

### 11.2 推送相关问题
```bash
# 推送被拒绝 - 权限不足
git push origin HEAD:refs/for/master  # 使用 Gerrit 评审

# 推送被拒绝 - 分支不存在
git push --set-upstream origin feature-branch

# 推送被拒绝 - 需要先拉取
git pull origin feature-branch
git push origin feature-branch
```

### 11.3 合并冲突处理
```bash
# 拉取时出现冲突
git pull origin master
# 手动解决冲突后
git add .
git commit -m "resolve merge conflicts"

# rebase 时出现冲突
git rebase master
# 解决冲突后
git add .
git rebase --continue
```

### 11.4 部署相关问题
```bash
# 镜像拉取失败
docker login ccr-registry.baidubce.com
docker pull your-image

# Pod 启动失败
kubectl describe pod -n cce-system pod-name
kubectl logs -n cce-system pod-name

# 服务不可用
kubectl get svc -n cce-system
kubectl get endpoints -n cce-system
```

### 11.5 回滚操作
```bash
# 回滚最后一次提交
git revert HEAD

# 回滚到指定版本
git revert commit-hash

# 强制回滚（谨慎使用）
git reset --hard commit-hash
git push --force origin feature-branch

# 回滚部署
kubectl rollout undo deployment/deployment-name -n cce-system
```

## 12. 实际案例参考

### 12.1 完整开发流程示例
```bash
# 1. 在 iCode 上创建功能分支 feature-wait-xpudriverready

# 2. 本地拉取并开发
git checkout master
git pull origin master
git fetch origin
git checkout feature-wait-xpudriverready

# 3. 开发过程中的提交
git add pkg/bcesdk/bcc/bcc.go
git add pkg/types/instance.go
git add services/cluster/cluster-controller/controllers/instance_controller.go
git commit -m "CCE-XXXX [Task] 添加XPU驱动就绪等待功能"

# 4. 构建测试镜像
sh build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service cce-service-dev zhangxiuqi

# 5. 推送到功能分支
git push origin feature-wait-xpudriverready

# 6. 在测试环境验证
# 登录测试环境，更新 DaemonSet 镜像进行测试

# 7. 创建 Pull Request 进行代码评审

# 8. 评审通过后合并到 master
```

### 12.2 紧急修复流程示例
```bash
# 1. 创建 bugfix 分支
git checkout master
git pull origin master
git checkout -b bugfix-instance-controller-crash

# 2. 快速修复
git add services/cluster/cluster-controller/controllers/instance_controller.go
git commit -m "CCE-XXXX [Bugfix] 修复实例控制器空指针异常"

# 3. 快速构建和部署测试
sh build_docker_image.sh ./services/cluster/cluster-controller cce-cluster-controller cce-service-dev hotfix

# 4. 验证修复效果后推送
git push origin bugfix-instance-controller-crash
```

### 12.3 多服务协同开发示例
```bash
# 同时修改多个服务
git add services/cluster/cluster-service/
git add services/cluster/cluster-controller/
git add pkg/types/

# 分别构建不同服务的镜像
sh build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service cce-service-dev zhangxiuqi
sh build_docker_image.sh ./services/cluster/cluster-controller cce-cluster-controller cce-service-dev zhangxiuqi

# 在测试环境同时更新多个服务
kubectl -n cce-system edit ds cce-cluster-service
kubectl -n cce-system edit ds cce-cluster-controller
```

### 12.4 百度 iCode 特殊说明
- **Gerrit 系统**：百度 iCode 使用 Gerrit 代码评审系统
- **评审推送**：使用 `refs/for/master` 格式推送代码评审
- **权限限制**：直接推送到 master 分支需要特殊权限
- **推送格式**：`git push origin HEAD:refs/for/target-branch`
- **分支保护**：master 主干不允许直接提交代码

### 12.5 测试环境访问
- **权限申请**：需要先申请 Noah 节点的门神权限
- **环境选择**：根据开发阶段选择合适的测试环境
- **资源账号**：使用 CCE 专用的测试资源账号
- **灰度插件**：使用 QA 提供的灰度插件进行测试

## 13. 相关链接和资源

### 13.1 开发相关
- [CCE 分支开发分支发布开发手册](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/0m-qJrk6wi/1vuBg6kqvW/0uRZrLGpgURyVY)
- [CCE 测试环境总览](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/0m-qJrk6wi/1vuBg6kqvW/xTN7T5MFQNfx3M)
- [前端灰度插件使用方法](http://wiki.baidu.com/pages/viewpage.action?pageId=1106567204)

### 13.2 流水线地址
- [开发流水线：DevelopPipline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/1737672/builds/list?branchName=branches)
- [上线流水线：BranchPipeline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/635522/builds/list?branchName=branches)
- [CCE组件发布流水线：K8SPluginReleasePipeline](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/317932/pipelines/716430/builds/list?branchName=branches)

### 13.3 测试资源
- [CCE 资源账号登录](https://login.bce.baidu.com/login?account=cce_00)
- [QA 资源账号登录](https://login.bce.baidu.com/login?account=2e1be1eb99e946c3a543ec5a4eaa7d39)
- CCE 组件发布群：********

### 13.4 线上环境
- bjrs-cce-online-control00.bjrs.baidu.com
- bdbl-cce-events00.bdbl.baidu.com
- gzns-cce-events00.gzns.baidu.com
- whgg-cce-events00.whgg.baidu.com
- hkg03-cce-events00.hkg03.baidu.com
- szth-cce-events00.szth.baidu.com

## 14. 常用 kubectl 命令参考

### 14.1 基础查看命令
```bash
# 查看所有命名空间
kubectl get ns
kubectl get namespaces

# 查看当前命名空间的 Pod
kubectl get pod
kubectl get pods

# 查看指定命名空间的 Pod
kubectl get pod -n cce-system
kubectl get pods -n cce-system

# 查看所有命名空间的 Pod
kubectl get pod --all-namespaces
kubectl get pod -A

# 查看 Pod 详细信息
kubectl get pod -o wide
kubectl get pod -n cce-system -o wide

# 实时监控 Pod 状态变化
kubectl get pod -n cce-system -w
kubectl get pod -n cce-system --watch
```

### 14.2 服务和部署查看
```bash
# 查看 DaemonSet
kubectl get ds -n cce-system
kubectl get daemonset -n cce-system

# 查看 Deployment
kubectl get deploy -n cce-system
kubectl get deployment -n cce-system

# 查看 Service
kubectl get svc -n cce-system
kubectl get service -n cce-system

# 查看 ConfigMap
kubectl get cm -n cce-system
kubectl get configmap -n cce-system

# 查看 Secret
kubectl get secret -n cce-system

# 查看 Endpoints
kubectl get ep -n cce-system
kubectl get endpoints -n cce-system
```

### 14.3 详细信息和描述
```bash
# 查看 Pod 详细信息
kubectl describe pod pod-name -n cce-system
kubectl describe pod -n cce-system pod-name

# 查看 DaemonSet 详细信息
kubectl describe ds cce-cluster-service -n cce-system

# 查看 Deployment 详细信息
kubectl describe deploy deployment-name -n cce-system

# 查看 Service 详细信息
kubectl describe svc service-name -n cce-system

# 查看节点信息
kubectl get nodes
kubectl describe node node-name
```

### 14.4 日志查看
```bash
# 查看 Pod 日志
kubectl logs pod-name -n cce-system

# 实时查看日志
kubectl logs -f pod-name -n cce-system
kubectl logs --follow pod-name -n cce-system

# 查看最近的日志（最近100行）
kubectl logs --tail=100 pod-name -n cce-system

# 查看指定时间范围的日志
kubectl logs --since=1h pod-name -n cce-system
kubectl logs --since=2024-08-07T10:00:00Z pod-name -n cce-system

# 查看多容器 Pod 中指定容器的日志
kubectl logs pod-name -c container-name -n cce-system

# 查看之前重启的容器日志
kubectl logs pod-name --previous -n cce-system
```

### 14.5 编辑和更新
```bash
# 编辑 DaemonSet（常用于更新镜像）
kubectl edit ds cce-cluster-service -n cce-system

# 编辑 Deployment
kubectl edit deploy deployment-name -n cce-system

# 编辑 ConfigMap
kubectl edit cm config-name -n cce-system

# 编辑 Service
kubectl edit svc service-name -n cce-system

# 获取资源的 YAML 定义
kubectl get pod pod-name -n cce-system -o yaml
kubectl get ds cce-cluster-service -n cce-system -o yaml
```

### 14.6 执行和调试
```bash
# 进入 Pod 执行命令
kubectl exec -it pod-name -n cce-system -- /bin/bash
kubectl exec -it pod-name -n cce-system -- /bin/sh

# 在 Pod 中执行单个命令
kubectl exec pod-name -n cce-system -- ls -la
kubectl exec pod-name -n cce-system -- ps aux

# 多容器 Pod 中指定容器
kubectl exec -it pod-name -c container-name -n cce-system -- /bin/bash

# 端口转发（用于本地调试）
kubectl port-forward pod-name 8080:8080 -n cce-system
kubectl port-forward svc/service-name 8080:8080 -n cce-system
```

### 14.7 资源管理
```bash
# 删除 Pod（会自动重建）
kubectl delete pod pod-name -n cce-system

# 强制删除 Pod
kubectl delete pod pod-name -n cce-system --force --grace-period=0

# 重启 DaemonSet（通过添加注解触发重启）
kubectl patch ds cce-cluster-service -n cce-system -p '{"spec":{"template":{"metadata":{"annotations":{"kubectl.kubernetes.io/restartedAt":"'$(date +%Y-%m-%dT%H:%M:%S%z)'"}}}}}'

# 扩缩容 Deployment
kubectl scale deploy deployment-name --replicas=3 -n cce-system

# 查看资源使用情况
kubectl top nodes
kubectl top pod -n cce-system
```

### 14.8 标签和选择器
```bash
# 根据标签查看资源
kubectl get pod -l app=cce -n cce-system
kubectl get pod --selector=app=cce -n cce-system

# 查看资源的标签
kubectl get pod --show-labels -n cce-system

# 添加标签
kubectl label pod pod-name env=test -n cce-system

# 删除标签
kubectl label pod pod-name env- -n cce-system
```

### 14.9 事件和故障排查
```bash
# 查看事件
kubectl get events -n cce-system
kubectl get events --sort-by=.metadata.creationTimestamp -n cce-system

# 查看特定资源的事件
kubectl describe pod pod-name -n cce-system | grep Events -A 10

# 查看集群信息
kubectl cluster-info
kubectl version

# 查看 API 资源
kubectl api-resources
kubectl api-versions
```

### 14.10 CCE 特定命令
```bash
# 查看 CCE 系统组件状态
kubectl get pod -n cce-system | grep cce

# 查看 CCE 集群服务
kubectl get pod -n cce-system | grep cluster-service

# 查看 CCE 控制器
kubectl get pod -n cce-system | grep cluster-controller

# 查看 CCE 相关的 DaemonSet
kubectl get ds -n cce-system | grep cce

# 查看 CCE 配置
kubectl get cm -n cce-system | grep cce

# 快速检查 CCE 服务健康状态
kubectl get pod -n cce-system -o wide | grep -E "(cce-cluster-service|cce-cluster-controller)"
```

### 14.11 常用组合命令
```bash
# 查看所有非 Running 状态的 Pod
kubectl get pod -A --field-selector=status.phase!=Running

# 查看最近重启的 Pod
kubectl get pod -A --sort-by=.status.containerStatuses[0].restartCount

# 查看资源使用情况并排序
kubectl top pod -n cce-system --sort-by=cpu
kubectl top pod -n cce-system --sort-by=memory

# 批量删除 Evicted 状态的 Pod
kubectl get pod -A | grep Evicted | awk '{print $1, $2}' | xargs -n2 kubectl delete pod -n

# 查看镜像信息
kubectl get pod -n cce-system -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[*].image}{"\n"}{end}'
```

---

**最后更新时间**：2025-08-07
**文档维护者**：CCE 研发团队