# InstanceGroup相关接口

## 创建节点组

**描述**

创建节点组

**请求结构**

```Plain Text
POST /v2/cluster/{clusterID}/instancegroup HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称              | 类型                                                         | 是否必须 | 参数位置          | 描述                                                         |
| --------------------- | ------------------------------------------------------------ | -------- | ----------------- | ------------------------------------------------------------ |
| clusterID             | String                                                       | 是       | URL 参数          | 集群 ID                                                      |
| instanceGroupName     | String                                                       | 是       | Request Body 参数 | 节点组名称，不可为空                                         |
| clusterRole           | String                                                       | 否       | Request Body 参数 | 节点在集群中的角色. 目前仅支持Node类型阶段组, 默认值为node   |
| shrinkPolicy          | String                                                       | 否       | Request Body 参数 | 节点组收缩规则. 可选 [ Priority, Uniform]. 默认为 Priority. Priority 优先收缩掉节点优先值低的节点, Uniform 多子网平均缩容 |
| updatePolicy          | String                                                       | 否       | Request Body 参数 | 节点组更新规则. 可选 [ Rolling, Concurrency ]. 默认为 Concurrency. Concurrency 并发更新, Rolling 滚动更新. 该参数暂未启用 |
| cleanPolicy           | String                                                       | 否       | Request Body 参数 | 节点清理规则. 可选 [ Remain, Delete ]. 默认为 Delete.        |
| instanceTemplate      | [InstanceTemplate](https://cloud.baidu.com/doc/CCE/s/Jkgajlwaz#instancetemplate) | 是       | Request Body 参数 | 节点组的节点配置                                             |
| replicas              | Integer                                                      | 是       | Request Body 参数 | 节点组节点要求的副本数. 取值范围是自然数集                   |
| clusterAutoscalerSpec | [ClusterAutoscalerSpec](https://cloud.baidu.com/doc/CCE/s/Jkgajlwaz#clusterautoscalerspec) | 否       | Request Body 参数 | 集群自动伸缩配置                                             |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称        | 类型   | 是否必须 | 描述                       |
| --------------- | ------ | -------- | -------------------------- |
| instanceGroupID | String | 是       | 节点组 ID                  |
| requestID       | String | 是       | 请求 ID, 问题定位提供该 ID |

**请求示例**

```text
POST /v2/cluster/cce-f7zeyx1u/instancegroup  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"instanceGroupName": "sdk-testcase",
	"selector": null,
	"cleanPolicy": "Delete",
	"instanceTemplate": {
		"instanceName": "",
		"runtimeType": "docker",
		"clusterRole": "node",
		"existedOption": {},
		"machineType": "BCC",
		"instanceType": "N3",
		"vpcConfig": {
			"vpcID": "vpc-pi9fghaxcpnf",
			"vpcSubnetID": "sbn-ww1xf6a5fi88",
			"securityGroupID": "g-4mnvpnrfscm1",
			"availableZone": "zoneA"
		},
		"instanceResource": {
			"cpu": 1,
			"mem": 4,
			"rootDiskSize": 40
		},
		"imageID": "305eeb25-a693-4cc0-832a-57bccf855771",
		"instanceOS": {
			"imageType": "System"
		},
		"instanceChargingType": "Postpaid",
		"instancePreChargingOption": {},
		"deployCustomConfig": {
			"dockerConfig": {},
			"preUserScript": "ls",
			"postUserScript": "ls"
		}
	},
	"replicas": 3
}
```

**返回示例**

```text
HTTP/1.1 200 OK
X-Bce-Request-Id: aef503ab-66e2-4b7f-9044-e922389ed03f
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "aef503ab-66e2-4b7f-9044-e922389ed03f",
	"instanceGroupID": "cce-ig-dvej1d3y"
}
```

## 获取节点组详情

**描述**

获取节点组详情

**请求结构**

```text
GET /v2/cluster/{clusterID}/instancegroup/{instanceGroupID} HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称        | 类型   | 是否必须 | 参数位置 | 描述      |
| --------------- | ------ | -------- | -------- | --------- |
| clusterID       | String | 是       | URL 参数 | 集群 ID   |
| instanceGroupID | String | 是       | URL 参数 | 节点组 ID |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称      | 类型                                                         | 是否必须 | 描述                       |
| ------------- | ------------------------------------------------------------ | -------- | -------------------------- |
| requestID     | String                                                       | 是       | 请求 ID, 问题定位提供该 ID |
| instanceGroup | [InstanceGroup](https://cloud.baidu.com/doc/CCE/s/Jkgajlwaz#instancegroup) | 是       | 查询到的节点组详情         |

**请求示例**

```text
GET /v2/cluster/cce-47bqnhmj/instancegroup/cce-ig-796lmt7a  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```text
HTTP/1.1 200 OK
X-Bce-Request-Id: 9ccdfbcf-f989-49e6-9701-6996dee804b1
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "9ccdfbcf-f989-49e6-9701-6996dee804b1",
	"instanceGroup": {
		"spec": {
			"cceInstanceGroupID": "cce-ig-dvej1d3y",
			"instanceGroupName": "sdk-testcase",
			"clusterID": "cce-z6qjgcq7",
			"clusterRole": "node",
			"shrinkPolicy": "Priority",
			"updatePolicy": "Concurrency",
			"cleanPolicy": "Delete",
			"instanceTemplate": {
				"instanceName": "",
				"runtimeType": "docker",
				"runtimeVersion": "18.9.2",
				"clusterID": "cce-z6qjgcq7",
				"clusterRole": "node",
				"instanceGroupID": "cce-ig-dvej1d3y",
				"instanceGroupName": "sdk-testcase",
				"existedOption": {},
				"machineType": "BCC",
				"instanceType": "N3",
				"bbcOption": {},
				"vpcConfig": {
					"vpcID": "vpc-pi9fghaxcpnf",
					"vpcSubnetID": "sbn-ww1xf6a5fi88",
					"securityGroupID": "g-4mnvpnrfscm1",
					"vpcSubnetType": "BCC",
					"vpcSubnetCIDR": "************/24",
					"availableZone": "zoneA"
				},
				"instanceResource": {
					"cpu": 1,
					"mem": 4,
					"rootDiskType": "hp1",
					"rootDiskSize": 40
				},
				"imageID": "m-4Umtt2i5",
				"instanceOS": {
					"imageType": "System",
					"imageName": "centos-8u0-x86_64-20200601205040",
					"osType": "linux",
					"osName": "CentOS",
					"osVersion": "8.0",
					"osArch": "x86_64 (64bit)",
					"osBuild": "2020060100"
				},
				"eipOption": {},
				"instanceChargingType": "Postpaid",
				"instancePreChargingOption": {},
				"deleteOption": {
					"deleteResource": true,
					"deleteCDSSnapshot": true
				},
				"deployCustomConfig": {
					"dockerConfig": {},
					"preUserScript": "bHM=",
					"postUserScript": "bHM="
				},
				"labels": {
					"cluster-id": "cce-z6qjgcq7",
					"cluster-role": "node",
					"instance-group-id": "cce-ig-dvej1d3y"
				},
				"cceInstancePriority": 5
			},
			"replicas": 3,
			"clusterAutoscalerSpec": {
				"enabled": false,
				"minReplicas": 0,
				"maxReplicas": 0,
				"scalingGroupPriority": 0
			}
		},
		"status": {
			"readyReplicas": 3,
			"pause": {
				"paused": false,
				"reason": ""
			}
		},
		"createdAt": "2020-09-27T06:34:51Z"
	}
}
```

## 删除节点组

**描述**

删除节点组

**请求结构**

```text
DELETE /v2/cluster/{clusterID}/instancegroup/{instanceGroupID} HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称           | 类型    | 是否必须 | 参数位置   | 描述                                                         |
| ------------------ | ------- | -------- | ---------- | ------------------------------------------------------------ |
| clusterID          | String  | 是       | URL 参数   | 集群 ID                                                      |
| instanceGroupID    | String  | 是       | URL 参数   | 节点组 ID                                                    |
| deleteInstances    | Boolean | 否       | Query 参数 | 是否删除节点组内节点. 默认为false                            |
| releaseAllResource | Boolean | 否       | Query 参数 | 是否将该节点组中的节点移出集群，并释放虚机资源、后付费公网IP和云磁盘. 默认为false |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称  | 类型   | 是否必须 | 描述                       |
| --------- | ------ | -------- | -------------------------- |
| requestID | String | 是       | 请求 ID, 问题定位提供该 ID |

**请求示例**

```text
DELETE /v2/cluster/cce-f7zeyx1u/instancegroup/cce-ig-dvej1d3y?deleteInstances=true  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```text
HTTP/1.1 200 OK
X-Bce-Request-Id: b1a9e426-c0ca-4668-a1d8-624c5000d365
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "b1a9e426-c0ca-4668-a1d8-624c5000d365"
}
```

## 修改节点组节点自动扩缩容配置

**描述**

修改节点组节点自动扩缩容配置

**请求结构**

```text
PUT /v2/cluster/{clusterID}/instancegroup/{instanceGroupID}/autoscaler HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称             | 类型    | 是否必须 | 参数位置          | 描述                                               |
| -------------------- | ------- | -------- | ----------------- | -------------------------------------------------- |
| clusterID            | String  | 是       | URL 参数          | 集群 ID                                            |
| instanceGroupID      | String  | 是       | URL 参数          | 节点组 ID                                          |
| enabled              | Boolean | 是       | Request Body 参数 | 是否启用Autoscaler                                 |
| minReplicas          | Integer | 是       | Request Body 参数 | 最小副本数. 取值范围是自然数集.                    |
| maxReplicas          | Integer | 是       | Request Body 参数 | 最大副本数. 取值范围是自然数集, 需大于minReplicas. |
| scalingGroupPriority | Integer | 是       | Request Body 参数 | 伸缩组优先级. 取值范围是自然数集.                  |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称  | 类型   | 是否必须 | 描述                       |
| --------- | ------ | -------- | -------------------------- |
| requestID | String | 是       | 请求 ID, 问题定位提供该 ID |

**请求示例**

```text
PUT /v2/cluster/cce-f7zeyx1u/instancegroup/cce-ig-dvej1d3y/autoscaler  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"enabled": true,
	"minReplicas": 2,
	"maxReplicas": 5,
	"scalingGroupPriority": 1
}
```

**返回示例**

```text
HTTP/1.1 200 OK
X-Bce-Request-Id: 27f91a44-7257-48e7-a8d8-849f45a32da4
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "27f91a44-7257-48e7-a8d8-849f45a32da4"
}
```



