
## 1. 需求背景

### 1.1 需求描述
* 针对节点组列表、节点列表中，增加支持按照计费方式进行筛选，默认按照包年包月、按量付费、抢占实例；


### 1.2 业务价值
* 提升用户体验：用户可以通过计费方式快速筛选和定位节点组/节点



## 2. 现状分析

### 2.1 现有接口分析
#### 2.1.1 节点组列表接口
- **接口路径**: `GET /v2/cluster/{clusterID}/instancegroups`
- **当前筛选参数**: 
  - `pageNo`: 页码
  - `pageSize`: 每页大小
  - `instanceGroupName`: 节点组名称
  - `instanceGroupID`: 节点组ID
  - `orderBy`: 排序字段
  - `order`: 排序方式
- **缺失筛选参数**: 计费方式

#### 2.1.2 节点列表接口
- **接口路径**: `GET /v2/cluster/{clusterID}/instances`
- **当前筛选参数**:
  - `keywordType`: 查询字段类型
  - `keyword`: 查询关键词
  - `orderBy`: 排序字段
  - `order`: 排序方式
  - `pageNo`: 页码
  - `pageSize`: 每页大小
- **缺失筛选参数**: 计费方式

### 2.2 现有数据结构分析
#### 2.2.1 计费方式相关
- **枚举值定义**:
  ```go
  // 预付费
  PaymentTimingPrepaid PaymentTiming = "Prepaid"
  // 后付费
  PaymentTimingPostpaid PaymentTiming = "Postpaid"
  // 竞价实例
  PaymentTimingBid PaymentTiming = "bid"
  ```
- **字段映射**:
  - 数据库字段: `instance_charging_type`
  - API字段: `instanceChargingType`
  - 显示字段: 当前为"支付方式"



## 3. 技术方案设计

### 3.1 整体架构
```
前端界面
    ↓
API网关
    ↓
CCE Service Layer (Controller → Service → Model)
    ↓
Database (t_cce_instancegroup, t_cce_instance)
```

### 3.2 接口设计

#### 3.2.1 节点组列表接口增强
**接口路径**: `GET /v2/cluster/{clusterID}/instancegroups`

**请求参数扩展**:
```go
// pkg/models/t_cce_instancegroup.go
type InstanceGroupListOption struct {
    // 现有字段
    AccountID         string
    ClusterID         string
    Role              ccetypes.ClusterRole
    CAEnabled         *bool
    PageNo            int
    PageSize          int
    TotalCountOnly    bool
    InstanceGroupName string
    InstanceGroupID   string
    OrderBy           string
    Order             string

    // 新增字段
    ChargingType      *string  // 计费方式筛选
}
```

**新增查询参数**:
- `chargingType`: 计费方式筛选，可选值：`Prepaid`、`Postpaid`、`bid`

#### 3.2.2 节点列表接口增强
**接口路径**: `GET /v2/cluster/{clusterID}/instances`

**请求参数扩展**:
```go
// pkg/models/t_cce_instance.go
type InstanceListOption struct {
    // 现有字段
    AccountID       string
    ClusterID       string
    CCEInstanceIDs  []string
    ClusterRole     ccetypes.ClusterRole
    PageNo          int
    PageSize        int
    TotalCountOnly  bool
    KeywordType     string
    Keyword         string
    InstanceGroupID string
    BecRegion       string
    OrderBy         string
    Order           string

    // 新增字段
    ChargingType    *string  // 计费方式筛选
}
```

**新增查询参数**:
- `chargingType`: 计费方式筛选，可选值：`Prepaid`、`Postpaid`、`bid`

### 3.3 数据库查询增强

#### 3.3.1 节点组查询增强
**文件**: `pkg/models/t_cce_instancegroup.go`

**修改方法**: `GetInstanceGroupsEx`

**查询逻辑**:
```sql
-- 基于现有查询添加计费方式筛选
SELECT * FROM t_cce_instancegroup
WHERE deleted = false
  AND account_id = ?
  AND cluster_id = ?
  AND instance_charging_type = ?  -- 新增筛选条件
```

#### 3.3.2 节点查询增强
**文件**: `pkg/models/t_cce_instance.go`

**修改方法**: `GetInstanceEx`, `GetInstancesByPage`, `GetInstancesByBatchQuery`

**查询逻辑**:
```sql
-- 基于现有查询添加计费方式筛选
SELECT * FROM t_cce_instance
WHERE account_id = ?
  AND cluster_id = ?
  AND instance_charging_type = ?  -- 新增筛选条件
```

### 3.4 计费方式枚举映射

#### 3.4.1 枚举值定义
**文件**: `pkg/bcesdk/bcc/bcc.go`
```go
// PaymentTiming 付费时间选择
type PaymentTiming string

const (
    // PaymentTimingPrepaid 预付费
    PaymentTimingPrepaid PaymentTiming = "Prepaid"
    // PaymentTimingPostpaid 后付费
    PaymentTimingPostpaid PaymentTiming = "Postpaid"
    // PaymentTimingBid 竞价实例
    PaymentTimingBid PaymentTiming = "bid"
)
```

#### 3.4.2 前端显示映射
```go
// 计费方式显示名称映射
var ChargingTypeDisplayMap = map[string]string{
    "Prepaid":  "包年包月",
    "Postpaid": "按量付费",
    "bid":      "抢占实例",
}
```

## 4. 详细代码实现

### 4.1 Controller层修改

#### 4.1.1 节点组Controller增强
**文件**: `services/cluster/cluster-service/controllers/instancegroup_controller.go`

**修改方法**: `ListInstanceGroup`

**伪代码**:
```go
func (c *InstanceGroupController) ListInstanceGroup() {
    ctx := c.ctx
    clusterID := c.Ctx.Input.Param(":clusterID")
    accountID := c.accountID

    // 现有参数解析
    pageNo, _ := c.GetInt("pageNo", 0)
    pageSize, _ := c.GetInt("pageSize", 0)
    keywordType := c.GetString("keywordType")
    keyword := c.GetString("keyword")

    // 新增：计费方式参数解析
    chargingType := c.GetString("chargingType")
    var chargingTypePtr *string
    if chargingType != "" {
        // 参数校验
        if !isValidChargingType(chargingType) {
            c.errorHandlerV2(errorcode.NewInvalidParam("invalid chargingType"), errorcode.LevelByUser, "")
            return
        }
        chargingTypePtr = &chargingType
    }

    // 构建查询选项
    opt := instancegroup.ListOptions{
        AccountID:         accountID,
        ClusterID:         clusterID,
        Role:              ccetypes.ClusterRoleNode,
        PageNo:            pageNo,
        PageSize:          pageSize,
        InstanceGroupName: instanceGroupName,
        InstanceGroupID:   instanceGroupID,
        ChargingType:      chargingTypePtr,  // 新增字段
    }

    // 调用Service层
    list, err := c.instanceGroupService.List(c.ctx, opt)
    // ... 错误处理和响应返回
}

// 新增：计费方式参数校验函数
func isValidChargingType(chargingType string) bool {
    validTypes := []string{"Prepaid", "Postpaid", "bid"}
    for _, validType := range validTypes {
        if chargingType == validType {
            return true
        }
    }
    return false
}
```

#### 4.1.2 节点Controller增强
**文件**: `services/cluster/cluster-service/controllers/instance_controller.go`

**修改方法**: `ListInstancesByPage`

**伪代码**:
```go
func (c *InstanceController) ListInstancesByPage() {
    ctx := c.ctx
    clusterID := c.Ctx.Input.Param(":clusterID")

    // 现有参数解析
    keywordType := c.GetString("keywordType")
    keyword := c.GetString("keyword")
    orderBy := c.GetString("orderBy")
    order := c.GetString("order")
    phases := c.GetString("phases")
    pageNo, _ := c.GetInt("pageNo", 1)
    pageSize, _ := c.GetInt("pageSize", 10)

    // 新增：计费方式参数解析
    chargingType := c.GetString("chargingType")

    // 参数校验
    if chargingType != "" && !isValidChargingType(chargingType) {
        c.errorHandlerV2(errorcode.NewInvalidParam("invalid chargingType"), errorcode.LevelByUser, "")
        return
    }

    // 调用Service层（需要扩展方法签名）
    instancePage, err := instanceService.ListInstancesByPageWithChargingType(ctx,
        clusterID, keywordType, keyword, orderBy, order, phases,
        pageNo, pageSize, enableInternalFields, clusterRole, becRegion,
        enableUpgradeNodeFields, ipList, isK8sNodeName, gpuType,
        calculateGPUCountRequested, chargingType)  // 新增参数

    // ... 错误处理和响应返回
}
```

### 4.2 Service层修改

#### 4.2.1 节点组Service增强
**文件**: `services/cluster/cluster-service/services/instancegroup/types.go`

**修改结构体**: `ListOptions`

**伪代码**:
```go
type ListOptions struct {
    AccountID string
    ClusterID string
    Role      ccetypes.ClusterRole
    CAEnabled *bool
    PageNo            int
    PageSize          int
    InstanceGroupID   string
    InstanceGroupName string
    OrderBy           string
    Order             string

    // 新增字段
    ChargingType      *string  // 计费方式筛选
}
```

**文件**: `services/cluster/cluster-service/services/instancegroup/service.go`

**修改方法**: `List`

**伪代码**:
```go
func (s *service) List(ctx context.Context, opts ListOptions) (*models.InstanceGroupList, error) {
    // 构建Model层查询选项
    opt := models.InstanceGroupListOption{
        AccountID:         opts.AccountID,
        ClusterID:         opts.ClusterID,
        Role:              opts.Role,
        CAEnabled:         opts.CAEnabled,
        PageNo:            opts.PageNo,
        PageSize:          opts.PageSize,
        TotalCountOnly:    false,
        InstanceGroupName: opts.InstanceGroupName,
        InstanceGroupID:   opts.InstanceGroupID,
        OrderBy:           opts.OrderBy,
        Order:             opts.Order,
        ChargingType:      opts.ChargingType,  // 新增字段传递
    }

    list, err := s.model.GetInstanceGroupsEx(ctx, opt)
    if err != nil {
        log.Errorf(ctx, "failed to list InstanceGroup from db, err: %v", err)
        return nil, err
    }

    return list, nil
}
```

#### 4.2.2 节点Service增强
**文件**: `services/cluster/cluster-service/services/instance/service.go`

**新增方法**: `ListInstancesByPageWithChargingType`

**伪代码**:
```go
func (s *service) ListInstancesByPageWithChargingType(ctx context.Context,
    clusterID, keywordType, keyword, orderBy, order, phases string,
    pageNo, pageSize int, enableInternalFields bool, clusterRole ccetypes.ClusterRole,
    becRegion string, enableUpgradeNodeFields bool, ipList []string,
    isK8sNodeName bool, gpuType string, calculateGPUCountRequested bool,
    chargingType string) (*ccesdk.InstancePage, error) {

    // 参数校验
    if clusterID == "" {
        return nil, fmt.Errorf("clusterID is empty")
    }

    // 获取集群信息
    cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
    if err != nil {
        return nil, err
    }

    // 构建查询选项
    var chargingTypePtr *string
    if chargingType != "" {
        chargingTypePtr = &chargingType
    }

    // 查询数据库
    instanceOption := models.InstanceListOption{
        AccountID:    s.accountID,
        ClusterID:    clusterID,
        ClusterRole:  clusterRole,
        KeywordType:  keywordTypeDB,
        Keyword:      keyword,
        OrderBy:      orderByDB,
        Order:        order,
        PageNo:       pageNo,
        PageSize:     pageSize,
        BecRegion:    becRegion,
        ChargingType: chargingTypePtr,  // 新增字段
    }

    instancesDB, err := s.models.GetInstanceExWithChargingType(ctx, instanceOption)
    if err != nil {
        return nil, err
    }

    // ... 后续处理逻辑
    return instancePage, nil
}
```

### 4.3 Model层修改

#### 4.3.1 节点组Model增强
**文件**: `pkg/models/t_cce_instancegroup.go`

**修改结构体**: `InstanceGroupListOption`

**伪代码**:
```go
type InstanceGroupListOption struct {
    AccountID         string
    ClusterID         string
    Role              ccetypes.ClusterRole
    CAEnabled         *bool
    PageNo            int
    PageSize          int
    TotalCountOnly    bool
    InstanceGroupName string
    InstanceGroupID   string
    OrderBy           string
    Order             string

    // 新增字段
    ChargingType      *string  // 计费方式筛选
}
```

**修改方法**: `GetInstanceGroupsEx`

**伪代码**:
```go
func (c *Client) GetInstanceGroupsEx(ctx context.Context, option InstanceGroupListOption) (*InstanceGroupList, error) {
    list := InstanceGroupList{
        Items: make([]*InstanceGroup, 0),
    }

    q := c.db.Unscoped().Table(c.cceInstanceGroupTableName).Where("deleted = ?", false)

    // 现有筛选条件
    if option.AccountID != "" {
        q = q.Where("account_id = ?", option.AccountID)
    }
    if option.ClusterID != "" {
        q = q.Where("cluster_id = ?", option.ClusterID)
    }
    if option.Role != "" {
        q = q.Where("cluster_role = ?", option.Role)
    }

    // 新增：计费方式筛选
    if option.ChargingType != nil && *option.ChargingType != "" {
        q = q.Where("instance_charging_type = ?", *option.ChargingType)
    }

    // 其他现有筛选条件...
    if option.InstanceGroupID != "" {
        q = q.Where("cce_instance_group_id LIKE ?", "%"+option.InstanceGroupID+"%")
    }
    if option.InstanceGroupName != "" {
        q = q.Where("instance_group_name LIKE ?", "%"+option.InstanceGroupName+"%")
    }

    // 排序和分页
    if option.OrderBy != "" {
        orderClause := option.OrderBy
        if option.Order != "" {
            orderClause += " " + option.Order
        }
        q = q.Order(orderClause)
    }

    if option.TotalCountOnly {
        err := q.Count(&list.TotalCount).Error
        return &list, err
    }

    // 分页查询
    if option.PageSize > 0 {
        offset := (option.PageNo - 1) * option.PageSize
        q = q.Offset(offset).Limit(option.PageSize)
    }

    // 获取总数
    var countQuery = q
    err := countQuery.Count(&list.TotalCount).Error
    if err != nil {
        return nil, fmt.Errorf("count instancegroups failed: %w", err)
    }

    // 查询数据
    err = q.Find(&list.Items).Error
    if err != nil {
        return nil, fmt.Errorf("query instancegroups failed: %w", err)
    }

    return &list, nil
}
```

#### 4.3.2 节点Model增强
**文件**: `pkg/models/t_cce_instance.go`

**修改结构体**: `InstanceListOption`

**伪代码**:
```go
type InstanceListOption struct {
    AccountID       string
    ClusterID       string
    CCEInstanceIDs  []string
    ClusterRole     ccetypes.ClusterRole
    PageNo          int
    PageSize        int
    TotalCountOnly  bool
    KeywordType     string
    Keyword         string
    InstanceGroupID string
    BecRegion       string
    OrderBy         string
    Order           string

    // 新增字段
    ChargingType    *string  // 计费方式筛选
}
```

**新增方法**: `GetInstanceExWithChargingType`

**伪代码**:
```go
func (c *Client) GetInstanceExWithChargingType(ctx context.Context, option InstanceListOption) ([]*Instance, error) {
    var instances []*Instance

    q := c.db.Table(c.cceInstanceTableName)

    // 基础筛选条件
    if option.AccountID != "" {
        q = q.Where("account_id = ?", option.AccountID)
    }
    if option.ClusterID != "" {
        q = q.Where("cluster_id = ?", option.ClusterID)
    }
    if option.ClusterRole != "" {
        q = q.Where("cluster_role = ?", option.ClusterRole)
    }

    // 新增：计费方式筛选
    if option.ChargingType != nil && *option.ChargingType != "" {
        q = q.Where("instance_charging_type = ?", *option.ChargingType)
    }

    // 其他现有筛选条件
    if option.KeywordType != "" && option.Keyword != "" {
        switch option.KeywordType {
        case "instance_name":
            q = q.Where("instance_name LIKE ?", "%"+option.Keyword+"%")
        case "instance_id":
            q = q.Where("cce_instance_id LIKE ?", "%"+option.Keyword+"%")
        case "vpc_ip":
            q = q.Where("vpc_ip LIKE ?", "%"+option.Keyword+"%")
        case "hostname":
            q = q.Where("hostname LIKE ?", "%"+option.Keyword+"%")
        }
    }

    if option.InstanceGroupID != "" {
        q = q.Where("instance_group_id = ?", option.InstanceGroupID)
    }

    if option.BecRegion != "" {
        q = q.Where("bec_region = ?", option.BecRegion)
    }

    if len(option.CCEInstanceIDs) > 0 {
        q = q.Where("cce_instance_id IN (?)", option.CCEInstanceIDs)
    }

    // 排序
    if option.OrderBy != "" {
        orderClause := option.OrderBy
        if option.Order != "" {
            orderClause += " " + option.Order
        }
        q = q.Order(orderClause)
    }

    // 分页
    if option.PageSize > 0 {
        offset := (option.PageNo - 1) * option.PageSize
        q = q.Offset(offset).Limit(option.PageSize)
    }

    err := q.Find(&instances).Error
    if err != nil {
        return nil, fmt.Errorf("query instances failed: %w", err)
    }

    return instances, nil
}
```

**修改现有方法**: `GetInstancesByPage`, `GetInstancesByBatchQuery`

**伪代码**:
```go
// 在现有的 GetInstancesByPage 方法中添加计费方式筛选
func (c *Client) GetInstancesByPage(ctx context.Context, accountID, clusterID, keywordType,
    keyword, orderBy, order string, pageSize, pageNo int, clusterRole ccetypes.ClusterRole,
    becRegion string, chargingType *string) ([]*Instance, error) {

    option := InstanceListOption{
        AccountID:    accountID,
        ClusterID:    clusterID,
        KeywordType:  keywordType,
        Keyword:      keyword,
        OrderBy:      orderBy,
        Order:        order,
        PageSize:     pageSize,
        PageNo:       pageNo,
        ClusterRole:  clusterRole,
        BecRegion:    becRegion,
        ChargingType: chargingType,  // 新增参数
    }

    return c.GetInstanceExWithChargingType(ctx, option)
}
```

## 5. 实现步骤和文件修改清单

### 5.1 需要修改的文件列表

#### 5.1.1 Controller层
1. **services/cluster/cluster-service/controllers/instancegroup_controller.go**
   - 修改 `ListInstanceGroup` 方法
   - 新增 `isValidChargingType` 校验函数

2. **services/cluster/cluster-service/controllers/instance_controller.go**
   - 修改 `ListInstancesByPage` 方法
   - 新增计费方式参数解析和校验

#### 5.1.2 Service层
3. **services/cluster/cluster-service/services/instancegroup/types.go**
   - 修改 `ListOptions` 结构体，新增 `ChargingType` 字段

4. **services/cluster/cluster-service/services/instancegroup/service.go**
   - 修改 `List` 方法，传递计费方式参数

5. **services/cluster/cluster-service/services/instance/service.go**
   - 新增 `ListInstancesByPageWithChargingType` 方法
   - 或修改现有 `ListInstancesByPage` 方法签名

#### 5.1.3 Model层
6. **pkg/models/t_cce_instancegroup.go**
   - 修改 `InstanceGroupListOption` 结构体
   - 修改 `GetInstanceGroupsEx` 方法

7. **pkg/models/t_cce_instance.go**
   - 修改 `InstanceListOption` 结构体
   - 新增 `GetInstanceExWithChargingType` 方法
   - 修改 `GetInstancesByPage` 和 `GetInstancesByBatchQuery` 方法

### 5.2 实现优先级

#### 第一阶段：基础功能实现
1. Model层数据结构修改
2. 数据库查询逻辑增强
3. 基础参数校验

#### 第二阶段：Service层集成
1. Service层接口扩展
2. 参数传递链路打通
3. 错误处理完善

#### 第三阶段：Controller层完善
1. API参数解析
2. 参数校验逻辑
3. 响应格式统一

#### 第四阶段：测试和优化
1. 单元测试编写
2. 集成测试验证
3. 性能优化

### 5.3 关键注意事项

#### 5.3.1 向后兼容性
- 新增的 `chargingType` 参数为可选参数
- 不传该参数时保持原有查询逻辑不变
- 确保现有API调用不受影响

#### 5.3.2 参数校验
- 严格校验计费方式枚举值
- 提供清晰的错误提示信息
- 记录参数校验失败的日志

#### 5.3.3 性能考虑
- 数据库查询添加适当索引
- 避免全表扫描
- 考虑分页查询的性能影响

#### 5.3.4 日志记录
- 记录筛选条件和查询结果
- 便于问题排查和性能分析
- 遵循现有日志格式规范

## 6. 测试计划

### 6.1 单元测试清单

#### 6.1.1 Controller层测试
- 参数解析正确性测试
- 参数校验逻辑测试
- 错误处理测试
- 响应格式测试

#### 6.1.2 Service层测试
- 业务逻辑正确性测试
- 参数传递测试
- 异常场景处理测试

#### 6.1.3 Model层测试
- 数据库查询正确性测试
- 筛选条件组合测试
- 分页逻辑测试
- 性能测试

### 6.2 集成测试清单

#### 6.2.1 API接口测试
- 节点组列表筛选功能测试
- 节点列表筛选功能测试
- 参数组合测试
- 边界条件测试

#### 6.2.2 端到端测试
- 完整业务流程测试
- 多种计费方式数据准备
- 筛选结果准确性验证
- 性能基准测试

### 6.3 测试数据准备

#### 6.3.1 测试环境数据
- 创建不同计费方式的节点组
- 创建不同计费方式的节点
- 准备混合计费方式的集群

#### 6.3.2 测试用例覆盖
- 单一计费方式筛选
- 无筛选条件（兼容性测试）
- 无效参数处理
- 空结果集处理

## 7. 部署和上线计划

### 7.1 部署步骤
1. 代码审查和合并
2. 单元测试执行
3. 集成测试验证
4. 预发布环境部署
5. 生产环境灰度发布

### 7.2 回滚方案
- 保持API向后兼容
- 数据库结构无破坏性变更
- 支持快速代码回滚

### 7.3 监控和告警
- API调用成功率监控
- 响应时间监控
- 错误率告警
- 数据库查询性能监控


