# 节点组和节点列表筛选功能技术方案（最小修改版）

## 1. 需求概述

针对节点组列表、节点列表中，增加支持按照计费方式进行筛选：
- `Prepaid`：包年包月
- `Postpaid`：按量付费  
- `bid`：抢占实例

## 2. 技术方案（最小修改原则）

### 2.1 API层修改

#### 2.1.1 节点组列表API
- **接口**: `GET /v2/clusters/{cluster_id}/instancegroups`
- **新增参数**: `chargingType` (可选，值：Prepaid/Postpaid/bid)

#### 2.1.2 节点列表API  
- **接口**: `GET /v2/clusters/{cluster_id}/instances`
- **新增参数**: `chargingType` (可选，值：Prepaid/Postpaid/bid)

### 2.2 核心修改点

#### 2.2.1 Controller层
1. **instancegroup_controller.go**: 
   - 解析`chargingType`参数
   - 添加校验函数`isValidChargingType`

2. **instance_controller.go**: 
   - 解析`chargingType`参数
   - 复用校验函数

#### 2.2.2 Service层  
1. **instancegroup/service.go**: 方法签名添加`chargingType *string`参数
2. **instance/service.go**: 方法签名添加`chargingType *string`参数

#### 2.2.3 Model层
1. **t_cce_instancegroup.go**: 
   - `InstanceGroupListOption`结构体添加`ChargingType *string`字段
   - `GetInstanceGroupsEx`方法添加查询条件：
     ```sql
     JSON_EXTRACT(instance_template, '$.instanceChargingType') = ?
     ```

2. **t_cce_instance.go**:
   - `InstanceListOption`结构体添加`ChargingType *string`字段  
   - `GetInstanceEx`方法添加查询条件：
     ```sql
     instance_charging_type = ?
     ```

### 2.3 参数校验

使用现有常量：
- `bcc.PaymentTimingPrepaid` = "Prepaid"
- `bcc.PaymentTimingPostpaid` = "Postpaid"
- `bcc.PaymentTimingBid` = "bid"

校验函数：
```go
func isValidChargingType(chargingType string) bool {
    validTypes := []string{
        string(bcc.PaymentTimingPrepaid),
        string(bcc.PaymentTimingPostpaid), 
        string(bcc.PaymentTimingBid),
    }
    for _, validType := range validTypes {
        if chargingType == validType {
            return true
        }
    }
    return false
}
```

### 2.4 数据库查询

- **节点组**: 使用`JSON_EXTRACT(instance_template, '$.instanceChargingType')`从JSON字段提取
- **节点**: 直接查询`instance_charging_type`字段

## 3. 实现特点

- **最小修改**: 只在现有方法中添加一个查询条件
- **向后兼容**: 参数可选，不传时行为不变
- **复用现有**: 使用现有的查询方法和数据结构
- **无破坏性**: 不修改现有方法签名的核心逻辑

## 4. 修改文件清单

1. `services/cluster/cluster-service/controllers/instancegroup_controller.go`
2. `services/cluster/cluster-service/controllers/instance_controller.go`
3. `services/cluster/cluster-service/services/instancegroup/service.go`
4. `services/cluster/cluster-service/services/instance/service.go`
5. `pkg/models/t_cce_instancegroup.go`
6. `pkg/models/t_cce_instance.go`

## 5. 测试重点

- 参数校验：有效值通过，无效值拒绝
- 查询逻辑：筛选结果正确
- 兼容性：不传参数时功能正常
- 边界条件：空值、特殊字符处理
